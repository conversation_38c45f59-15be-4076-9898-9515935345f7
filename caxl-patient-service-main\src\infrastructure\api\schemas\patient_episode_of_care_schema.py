"""Schema for Patient Episode of Care"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from src.infrastructure.api.schemas.patient_episode_of_care_order_schema import (
    PatientEpisodeOfCareOrderResponseSchema,
)


class PatientEpisodeOfCareBaseSchema(BaseModel):
    """Base schema for Patient Episode of Care"""

    patient_id: UUID
    patient_mrn_id: UUID | None = None
    location_id: UUID | None = None
    discharge_summary: str | None = None
    clinical_diagnosis: str | None = None
    date_of_start_of_care: datetime | None = None
    insurance_name: str | None = Field(None, max_length=100)
    insurance_type: str | None = Field(None, max_length=50)
    insurance_number: str | None = Field(None, max_length=50)


class PatientEpisodeOfCareCreateSchema(PatientEpisodeOfCareBaseSchema):
    """Schema for creating Patient Episode of Care"""

    pass


class PatientEpisodeOfCareUpdateSchema(PatientEpisodeOfCareBaseSchema):
    """Schema for updating Patient Episode of Care"""

    pass


class PatientEpisodeOfCareResponseSchema(PatientEpisodeOfCareBaseSchema):
    """Schema for Patient Episode of Care response"""

    id: UUID
    mod_at: datetime
    mod_by: UUID
    mod_service: str
    record_status: str

    class Config:
        """Pydantic config"""

        from_attributes = True


class PatientEpisodeOfCareGetByIdResponseSchema(PatientEpisodeOfCareResponseSchema):
    """Schema for Patient Episode of Care getbyid response"""

    orders: list[PatientEpisodeOfCareOrderResponseSchema] = Field(default_factory=list)

    class Config:
        """Pydantic config"""

        from_attributes = True


class PatientEpisodeOfCareListResponseSchema(BaseModel):
    """Schema for Patient Episode of Care list response"""

    episodes: list[PatientEpisodeOfCareResponseSchema]
    page: int
    page_size: int
    total: int
    total_pages: int
