"""Repository implementation for Patient Episode of Care"""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import joinedload

from src.domain.entities.patient_episode_of_care import PatientEpisodeOfCareEntity
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.repositories.patient_episode_of_care_repository import (
    PatientEpisodeOfCareRepository,
)
from src.infrastructure.adapters.persistence.mappers.patient_episode_of_care_mapper import (
    PatientEpisodeOfCareMapper,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care import (
    PatientEpisodeOfCare,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order import (
    PatientEpisodeOfCareOrder,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)
from src.infrastructure.adapters.persistence.repositories.base_repository import BaseRepositoryImpl
from src.infrastructure.adapters.persistence.session import SessionManager


class PatientEpisodeOfCareRepositoryImpl(
    BaseRepositoryImpl[PatientEpisodeOfCareEntity, PatientEpisodeOfCare],
    PatientEpisodeOfCareRepository,
):
    """Repository implementation for Patient Episode of Care"""

    def __init__(self, session_manager: SessionManager, mapper: PatientEpisodeOfCareMapper):
        """Initialize repository"""
        super().__init__(session_manager, mapper)

    async def find_by_id(self, episode_id: UUID) -> PatientEpisodeOfCareEntity | None:
        """Find episode of care by ID"""
        async for session in self._session_manager.get_session():
            result = await session.execute(
                select(PatientEpisodeOfCare)
                .where(
                    PatientEpisodeOfCare.id == episode_id,
                    PatientEpisodeOfCare.record_status == "ACTIVE",
                )
                .options(
                    joinedload(
                        PatientEpisodeOfCare.patient_episode_of_care_orders.and_(
                            PatientEpisodeOfCareOrder.record_status == "ACTIVE"
                        )
                    ).joinedload(
                        PatientEpisodeOfCareOrder.directives.and_(
                            PatientEpisodeOfCareOrderDirective.record_status == "ACTIVE"
                        )
                    )
                )
            )
            model = result.unique().scalar_one_or_none()
            if model:
                # Ensure orders are loaded
                await session.refresh(model, ["patient_episode_of_care_orders"])
            return self._mapper.to_domain(model) if model else None

    async def find_all(
        self, page: int = 1, page_size: int = 10
    ) -> list[PatientEpisodeOfCareEntity]:
        """Find all episodes of care with pagination"""
        async for session in self._session_manager.get_session():
            query = (
                select(PatientEpisodeOfCare)
                .where(PatientEpisodeOfCare.record_status == "ACTIVE")
                .options(joinedload(PatientEpisodeOfCare.patient_episode_of_care_orders))
            )
            query = query.offset((page - 1) * page_size).limit(page_size)
            result = await session.execute(query)
            models = result.unique().scalars().all()
            return [self._mapper.to_domain(model) for model in models]

    async def save(
        self, episode: PatientEpisodeOfCareEntity, audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareEntity:
        """Save episode of care"""
        async for session in self._session_manager.get_session():
            model = self._mapper.to_model(episode)
            session.add(model)
            await session.commit()
            await session.refresh(model)
            return self._mapper.to_domain(model)

    async def update(
        self, episode: PatientEpisodeOfCareEntity, audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareEntity:
        """Update episode of care"""
        async for session in self._session_manager.get_session():
            model = await session.get(PatientEpisodeOfCare, episode.id)
            if not model:
                return None
            updated_model = self._mapper.to_model(episode)
            for key, value in updated_model.__dict__.items():
                if not key.startswith("_"):
                    setattr(model, key, value)
            await session.commit()
            await session.refresh(model)
            return self._mapper.to_domain(model)

    async def delete(self, episode_id: UUID, audit_stamp: AuditStamp) -> bool:
        """Delete episode of care"""
        async for session in self._session_manager.get_session():
            model = await session.get(PatientEpisodeOfCare, episode_id)
            if not model:
                return False
            model.record_status = audit_stamp.record_status
            model.mod_at = audit_stamp.mod_at
            model.mod_by = audit_stamp.mod_by
            model.mod_service = audit_stamp.mod_service
            await session.commit()
            return True

    async def count(self) -> int:
        """Get total count of episodes of care"""
        async for session in self._session_manager.get_session():
            result = await session.execute(select(PatientEpisodeOfCare))
            return len(result.unique().scalars().all())
