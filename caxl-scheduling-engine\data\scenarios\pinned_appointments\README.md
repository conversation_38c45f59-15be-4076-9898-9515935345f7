# Pinned Appointments Test Scenario

## Overview
This scenario tests the handling of pinned appointments that have fixed dates and times that cannot be changed during optimization.

## Test Data
- **Providers**: 2 healthcare providers
- **Patients**: 3 patients
- **Appointments**: 5 appointments (2 pinned, 3 flexible)

## Pinned Appointments
- **apt-pin-001**: Pinned to 2025-06-25 at 14:00 (medication schedule requirement)
- **apt-pin-002**: Pinned to 2025-06-25 at 10:30 (patient availability constraint)

## Expected Outcomes
- Pinned appointments maintain their assigned times
- Flexible appointments are scheduled around pinned ones
- Day plan respects pinned appointment times
- No conflicts with pinned appointments

## Test Cases
1. **Assignment Preservation**: Verify pinned appointments keep their times
2. **Conflict Avoidance**: Verify no scheduling conflicts with pinned appointments
3. **Day Plan Respect**: Verify day plan doesn't reschedule pinned appointments
4. **Replan Handling**: Verify replanning works around pinned appointments
