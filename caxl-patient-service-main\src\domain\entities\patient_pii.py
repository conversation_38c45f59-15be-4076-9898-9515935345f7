"""Patient PII Entity"""

from dataclasses import dataclass
from typing import Optional
from uuid import UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.value_objects.patient_contact_info import PatientContactInfo
from src.domain.value_objects.personal_info import PersonalInfo


@dataclass
class PatientPII(EntityBase):
    """Patient PII Entity"""

    def __init__(self,
                 audit_stamp: AuditStamp,
                 personal_info: PersonalInfo,
                 id: Optional[UUID] = None,
                 contact_info: PatientContactInfo = None,
                 ):
        super().__init__(
            id=id,
            audit_stamp=audit_stamp
        )
        self._personal_info = personal_info
        self._contact_info = contact_info

    def __post_init__(self):
        if not self._contact_info:
            raise ValueError("Patient contact details is required")
        if self._personal_info.ssn:
            raise ValueError("Invalid SSN")

    @property
    def personal_info(self) -> PersonalInfo:
        return self._personal_info

    @personal_info.setter
    def personal_info(self, value: PersonalInfo) -> None:
        self._personal_info = value

    @property
    def contact_info(self) -> PatientContactInfo:
        return self._contact_info

    @contact_info.setter
    def contact_info(self, value: PatientContactInfo) -> None:
        self._contact_info = value
