"""Base mapper for application layer"""

from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase

T = TypeVar("T", bound=EntityBase)  # Domain entity type
R = TypeVar("R")  # Response DTO type
L = TypeVar("L")  # List response DTO type


class BaseMapper(Generic[T, R, L], ABC):
    """Base mapper for converting between domain entities and DTOs"""

    @abstractmethod
    def to_response(self, entity: T | None) -> R | None:
        """Convert domain entity to response DTO"""
        pass

    @abstractmethod
    def to_list_response(self, entities: list[T], total: int, page: int, page_size: int) -> L:
        """Convert list of domain entities to list response DTO"""
        pass

    @abstractmethod
    def to_domain(self, dto: R | None, audit_stamp: AuditStamp) -> T | None:
        """Convert DTO to domain entity"""
        pass

    def to_response_list(self, entities: list[T]) -> list[R]:
        """Convert list of domain entities to response DTOs"""
        return [self.to_response(entity) for entity in entities if entity is not None]

    def to_domain_list(self, dtos: list[R]) -> list[T]:
        """Convert list of DTOs to domain entities"""
        return [self.to_domain(dto) for dto in dtos if dto is not None]
