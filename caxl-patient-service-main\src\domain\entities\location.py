"""Patient Contact Information Value Object"""

from uuid import UUID

from src.domain.enums import Location<PERSON>ame
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class LocationInfo(EntityBase):
    """Location information value object"""

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        location_name: LocationName,
        location_id: UUID,
        is_primary: bool = False,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._location_name = location_name
        self._location_id = location_id
        self._is_primary = is_primary

    @property
    def location_name(self) -> LocationName:
        return self._location_name

    @location_name.setter
    def location_name(self, value: LocationName) -> None:
        if not value:
            raise ValueError("Location name is required")
        self._location_name = value

    @property
    def location_id(self) -> UUID:
        return self._location_id

    @location_id.setter
    def location_id(self, value: UUID) -> None:
        if not value:
            raise ValueError("Location ID is required")
        self._location_id = value

    @property
    def is_primary(self) -> bool:
        return self._is_primary

    @is_primary.setter
    def is_primary(self, value: bool) -> None:
        self._is_primary = value
