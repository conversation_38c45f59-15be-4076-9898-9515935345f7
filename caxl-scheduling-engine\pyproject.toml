[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "caxl-scheduling-engine"
version = "0.1.0"
description = "CareAxl Scheduling Engine - API-based optimization service for healthcare appointment scheduling"
authors = [
    {name = "CareAxl Team", email = "<EMAIL>"},
]

dependencies = [
    "fastapi>=0.110.0",
    "sqlalchemy>=2.0.27",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.10",
    "python-dotenv>=1.0.1",
    "dependency-injector>=4.46.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.9",
    "email-validator>=2.1.0.post1",
    "uvicorn>=0.33.0",
    "pydantic>=2.7.2",
    "pydantic-settings>=2.9.1",
    "httpx==0.25.0",
    "timefold>=1.23.0b0",
    "pyyaml>=6.0",
    "schedule>=1.2.0",
    "loguru>=0.7.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
]
requires-python = ">=3.11"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "black==24.10.0",
    "ruff==0.1.10",
    "mypy==1.15.0",
    "aiosqlite>=0.21.0",
    "pytest==8.3.5",
    "pytest-asyncio==0.26.0",
    "pytest-cov==4.1.0",
    "pre-commit>=3.7.1",
    "bandit==1.7.5",
    "pytest-mock==3.14.0",
    "pytest-xdist>=3.3.1",
    "factory-boy>=3.3.0",
    "faker>=19.6.2",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing --cov-report=html"

[tool.black]
line-length = 100
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.ruff]
line-length = 100
target-version = "py311"
select = ["E", "F", "B", "I", "N", "UP", "PL", "RUF"]
ignore = ["E501", "B008", "PLR0913", "UP007"]
extend-exclude = ["tests", "src/domain/foundations"]

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.coverage.run]
source = ["src"]
omit = ["tests/*", "**/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if __name__ == .__main__.:",
    "raise NotImplementedError",
    "if TYPE_CHECKING:",
    "pass",
    "@abstractmethod",
]

[tool.ruff.per-file-ignores]
"tests/*" = ["PLR2004", "S101"]
"src/infrastructure/persistence/models/*" = ["PLR2004"]
