from sqlalchemy import Column, Foreign<PERSON>ey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models import BaseModel


class Patient(BaseModel):
    __tablename__ = "patient"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_id", UUID(as_uuid=True), primary_key=True)
    mpu_id = Column(String)
    nhid = Column(String)
    patient_profile_id = Column(
        UUID(as_uuid=True), ForeignKey("patient_profile.patient_profile_id")
    )
    patient_pref_id = Column(UUID(as_uuid=True), ForeignKey("patient_pref.patient_pref_id"))
    patient_pii_id = Column(UUID(as_uuid=True), ForeignKey("patient_pii.patient_pii_id"))

    profile = relationship("PatientProfile", back_populates="patient", lazy="joined")
    preferences = relationship("PatientPreferences", back_populates="patient", lazy="joined")
    pii = relationship("PatientPII", back_populates="patient", lazy="joined")

    caregivers = relationship("PatientCaregiver", back_populates="patient", lazy="joined")
    referring_mrns = relationship("PatientReferringMRN", back_populates="patient", lazy="joined")
    patient_episodes_of_care = relationship(
        "PatientEpisodeOfCare", back_populates="patient", lazy="joined"
    )
