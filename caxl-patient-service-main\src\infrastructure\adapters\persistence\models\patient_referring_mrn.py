from sqlalchemy import Column, Date, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientReferringMRN(BaseModel):
    __tablename__ = "patient_referring_mrn"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_referring_id", UUID(as_uuid=True), primary_key=True)
    patient_id = Column(UUID(as_uuid=True), ForeignKey("patient.patient_id"))
    referring_mrn = Column(String)
    referring_name = Column(String)
    referring_state = Column(String)
    referring_hospital = Column(String)
    referring_npi = Column(String)
    inpatient_discharge_date = Column(Date)

    patient = relationship("Patient", back_populates="referring_mrns", lazy="joined")
