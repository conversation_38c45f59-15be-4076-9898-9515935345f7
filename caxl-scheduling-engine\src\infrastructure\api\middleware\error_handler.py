"""Error handling middleware"""

import traceback
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for global error handling"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with error handling"""
        
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as exc:
            # Re-raise HTTP exceptions to be handled by FastAPI
            raise exc
            
        except Exception as exc:
            # Handle unexpected exceptions
            correlation_id = getattr(request.state, 'correlation_id', 'unknown')
            
            # Log error (simplified for demo)
            print(f"[{correlation_id}] Unhandled error: {str(exc)}")
            print(f"[{correlation_id}] Traceback: {traceback.format_exc()}")
            
            # Return generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": "An unexpected error occurred",
                    "correlation_id": correlation_id
                }
            )
