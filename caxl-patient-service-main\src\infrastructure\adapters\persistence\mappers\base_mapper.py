"""Base mapper interface for persistence layer"""

from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from src.domain.foundations.base.entity_base import EntityBase

T = TypeVar("T", bound=EntityBase)  # Domain entity type
M = TypeVar("M")  # Model type


class BaseMapper(Generic[T, M], ABC):
    """Base mapper interface for converting between domain entities and persistence models"""

    @property
    @abstractmethod
    def model(self) -> type[M]:
        """Get the model class"""
        pass

    @abstractmethod
    def to_domain(self, model: M | None) -> T | None:
        """Convert model to domain entity"""
        pass

    @abstractmethod
    def to_model(self, entity: T | None) -> M | None:
        """Convert domain entity to model"""
        pass

    def to_domain_list(self, models: list[M]) -> list[T]:
        """Convert list of models to domain entities"""
        return [self.to_domain(model) for model in models if model is not None]

    def to_model_list(self, entities: list[T]) -> list[M]:
        """Convert list of domain entities to models"""
        return [self.to_model(entity) for entity in entities if entity is not None]
