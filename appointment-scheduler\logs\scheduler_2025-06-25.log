2025-06-25 00:03:41 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:05:52 | INFO | src.appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:07:12 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:07:43 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:10:20 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:18:56 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:23:49 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 00:24:15 | INFO | src.appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:57:48 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:58:00 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:58:14 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:59:41 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:59:57 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 07:59:58 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 07:59:58 | INFO | appointment_scheduler.api.routes:get_carestaff:136 | Loaded 3 care staff members
2025-06-25 08:01:31 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:01:32 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 08:01:32 | INFO | appointment_scheduler.api.routes:get_carestaff:136 | Loaded 3 care staff members
2025-06-25 08:03:32 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:03:33 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 08:03:33 | INFO | appointment_scheduler.api.routes:get_carestaff:136 | Loaded 3 care staff members
2025-06-25 08:06:32 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:06:32 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 08:06:32 | INFO | appointment_scheduler.api.routes:get_carestaff:136 | Loaded 3 care staff members
2025-06-25 08:08:32 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:10:29 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:12:07 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:12:21 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:12:22 | INFO | __main__:main:57 | Starting Appointment Scheduler API server...
2025-06-25 08:13:02 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 08:13:23 | INFO | appointment_scheduler.logging_config:setup_logging:39 | Logging configured from D:\Work\Scheduler\appointment-scheduler\config\logger.yaml
2025-06-25 08:13:24 | INFO | appointment_scheduler.api.routes:get_patients:76 | Loaded 5 patients
2025-06-25 08:13:24 | INFO | appointment_scheduler.api.routes:get_carestaff:136 | Loaded 3 care staff members
