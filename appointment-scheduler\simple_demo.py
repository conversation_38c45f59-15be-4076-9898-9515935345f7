#!/usr/bin/env python3
"""
Simple Demo Script for Healthcare Scheduling System
"""

import sys

def show_feature_toggles():
    """Show how to configure feature toggles."""
    print("\n⚙️  FEATURE TOGGLE CONFIGURATION")
    print("=" * 50)
    print("You can enable/disable features in config/scheduler.yml:")
    print()
    print("Basic Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: false")
    print("  enable_provider_capacity_management: false")
    print("  enable_route_optimization: false")
    print()
    print("Premium Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: false")
    print()
    print("Enterprise Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: true")
    print("  enable_advanced_traffic_integration: true")
    print()
    print("💡 To modify these settings, edit config/scheduler.yml")

def main():
    """Main function."""
    print("🏥 Healthcare Scheduling System - Simple Demo")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python simple_demo.py toggles    - Show feature toggle examples")
        print()
        return
    
    command = sys.argv[1].lower()
    
    if command == "toggles":
        show_feature_toggles()
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'toggles'")

if __name__ == "__main__":
    main() 