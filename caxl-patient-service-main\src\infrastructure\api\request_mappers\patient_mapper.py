"""Patient request mappers for API layer"""

from uuid import UUID, uuid4

from src.application.dtos.patient import (
    BasicPersonalInfoDTO,
    CaregiverDTO,
    ContactInfoDTO,
    EmailInfoDTO,
    EmergencyContactDTO,
    EmergencyContactRelationshipDTO,
    InsuranceDTO,
    LabPreferenceDTO,
    LocationInfoDTO,
    LocationNameDTO,
    MedicalProfileDTO,
    PatientDTO,
    PatientPIIDTO,
    PatientPreferencesDTO,
    PersonalInfoDTO,
    PharmacyPreferenceDTO,
    PhoneInfoDTO,
    ReferringMRNDTO,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient import (
    BasicPersonalInfoSchema,
    CaregiverSchema,
    ContactInfoSchema,
    CreatePatientRequestSchema,
    EmailInfoSchema,
    EmergencyContactSchema,
    InsuranceSchema,
    LabPreferenceSchema,
    LocationSchema,
    MedicalProfileSchema,
    PatientPIISchema,
    PatientPreferencesSchema,
    PersonalInfoSchema,
    PharmacyPreferenceSchema,
    PhoneInfoSchema,
    ReferringMRNSchema,
    UpdatePatientRequestSchema,
)


def map_basic_personal_info_to_dto(schema: BasicPersonalInfoSchema) -> BasicPersonalInfoDTO:
    """Map basic personal info schema to DTO"""
    return BasicPersonalInfoDTO(
        first_name=schema.first_name,
        last_name=schema.last_name,
        gender=schema.gender,
        dob=schema.dob,
    )


def map_personal_info_to_dto(schema: PersonalInfoSchema) -> PersonalInfoDTO:
    """Map personal info schema to DTO"""
    return PersonalInfoDTO(
        basic_info=map_basic_personal_info_to_dto(schema.basic_info),
        ssn=schema.ssn,
        ethnicity=schema.ethnicity,
        race=schema.race,
    )


def map_contact_info_to_dto(schema: ContactInfoSchema) -> ContactInfoDTO:
    """Map contact info schema to DTO"""
    return ContactInfoDTO(
        emergency_contacts=[
            map_emergency_contact_to_dto(contact) for contact in schema.emergency_contacts
        ],
        phones=[map_phone_to_dto(phone) for phone in schema.phones],
        emails=[map_email_to_dto(email) for email in schema.emails],
        locations=[map_location_to_dto(location) for location in schema.locations],
    )


def map_pii_to_dto(schema: PatientPIISchema) -> PatientPIIDTO:
    """Map PII schema to DTO"""
    return PatientPIIDTO(
        id=schema.id,
        personal_info=map_personal_info_to_dto(schema.personal_info),
        contact_info=map_contact_info_to_dto(schema.contact_info),
    )


def map_lab_preference_to_dto(schema: LabPreferenceSchema) -> LabPreferenceDTO:
    """Map lab preference schema to DTO"""
    return LabPreferenceDTO(
        name=schema.name,
        location_id=schema.location_id,
        phone=schema.phone,
        phone_country_code=schema.phone_country_code,
        email=schema.email,
    )


def map_pharmacy_preference_to_dto(schema: PharmacyPreferenceSchema) -> PharmacyPreferenceDTO:
    """Map pharmacy preference schema to DTO"""
    return PharmacyPreferenceDTO(
        name=schema.name,
        location_id=schema.location_id,
        phone=schema.phone,
        email=schema.email,
    )


def map_preferences_to_dto(schema: PatientPreferencesSchema) -> PatientPreferencesDTO:
    """Map preferences schema to DTO"""
    return PatientPreferencesDTO(
        id=schema.id,
        pref_language=schema.pref_language,
        lab_preference=(
            map_lab_preference_to_dto(schema.lab_preference) if schema.lab_preference else None
        ),
        pharmacy_preference=(
            map_pharmacy_preference_to_dto(schema.pharmacy_preference)
            if schema.pharmacy_preference
            else None
        ),
        time_pref=schema.time_pref,
    )


def map_medical_profile_to_dto(schema: MedicalProfileSchema) -> MedicalProfileDTO:
    """Map medical profile schema to DTO"""
    return MedicalProfileDTO(
        id=schema.id,
        insurance=map_insurance_to_dto(schema.insurance) if schema.insurance else None,
        clinical_diagnosis=schema.clinical_diagnosis,
        medical_history=schema.medical_history,
        social_history=schema.social_history,
        allergies=schema.allergies,
        notes=schema.notes,
    )


def map_caregiver_to_dto(schema: CaregiverSchema) -> CaregiverDTO:
    """Map caregiver schema to DTO"""
    return CaregiverDTO(
        id=schema.id,
        personal_info=map_basic_personal_info_to_dto(schema.personal_info),
        relationship=schema.relationship,
        patient_id=schema.patient_id,
        email=schema.email,
        email_verified=schema.email_verified,
        phone_country_code=schema.phone_country_code,
        phone_number=schema.phone_number,
        is_phone_verified=schema.is_phone_verified,
        location_id=schema.location_id,
    )


def map_referring_mrn_to_dto(schema: ReferringMRNSchema) -> ReferringMRNDTO:
    """Map referring MRN schema to DTO"""
    return ReferringMRNDTO(
        id=schema.id,
        referring_mrn=schema.referring_mrn,
        referring_name=schema.referring_name,
        referring_state=schema.referring_state,
        referring_hospital=schema.referring_hospital,
        referring_npi=schema.referring_npi,
        inpatient_discharge_date=schema.inpatient_discharge_date,
    )


def map_phone_to_dto(schema: PhoneInfoSchema) -> PhoneInfoDTO:
    """Map phone info schema to DTO"""
    return PhoneInfoDTO(
        id=schema.id,
        phone_number=schema.phone_number,
        phone_country_code=schema.phone_country_code,
        phone_type=schema.phone_type,
        is_primary=schema.is_primary,
        is_verified=schema.is_verified,
        preferred_for_sms=schema.preferred_for_sms,
    )


def map_email_to_dto(schema: EmailInfoSchema) -> EmailInfoDTO:
    """Map email info schema to DTO"""
    return EmailInfoDTO(
        id=schema.id,
        email=schema.email,
        email_type=schema.email_type,
        is_verified=schema.is_verified,
    )


def map_location_to_dto(schema: LocationSchema) -> LocationInfoDTO:
    """Map location info schema to DTO"""
    return LocationInfoDTO(
        id=schema.id,
        location_id=schema.location_id,
        location_name=LocationNameDTO(schema.location_name),
        is_primary=schema.is_primary,
    )


def map_emergency_contact_to_dto(schema: EmergencyContactSchema) -> EmergencyContactDTO:
    """Map emergency contact schema to DTO"""
    return EmergencyContactDTO(
        id=schema.id,
        first_name=schema.first_name,
        last_name=schema.last_name,
        relationship=EmergencyContactRelationshipDTO(schema.relationship),
        phone_number=schema.phone_number,
        phone_country_code=schema.phone_country_code,
        email=schema.email,
    )


def map_insurance_to_dto(schema: InsuranceSchema) -> InsuranceDTO:
    """Map insurance schema to DTO"""
    return InsuranceDTO(
        name=schema.name,
        type=schema.type,
        number=schema.number,
    )


def map_create_patient_request_to_dto(
    schema: CreatePatientRequestSchema, user_context: AuthSession
) -> PatientDTO:
    """Map create patient request schema to DTO"""
    return PatientDTO(
        id=uuid4(),
        pii=map_pii_to_dto(schema.pii) if schema.pii else None,
        preferences=map_preferences_to_dto(schema.preferences) if schema.preferences else None,
        profile=map_medical_profile_to_dto(schema.profile) if schema.profile else None,
        caregivers=(
            [map_caregiver_to_dto(caregiver) for caregiver in schema.caregivers]
            if schema.caregivers
            else None
        ),
        referring_mrns=(
            [map_referring_mrn_to_dto(mrn) for mrn in schema.referring_mrns]
            if schema.referring_mrns
            else None
        ),
        mpu_id=schema.mpu_id,
        nhid=schema.nhid,
        tenant_id=user_context.tenant_id,
    )


def map_update_patient_request_to_dto(
    patient_id: UUID, schema: UpdatePatientRequestSchema, user_context: AuthSession
) -> PatientDTO:
    """Map update patient request schema to DTO"""
    return PatientDTO(
        id=patient_id,
        pii=map_pii_to_dto(schema.pii) if schema.pii else None,
        preferences=map_preferences_to_dto(schema.preferences) if schema.preferences else None,
        profile=map_medical_profile_to_dto(schema.profile) if schema.profile else None,
        referring_mrns=(
            [map_referring_mrn_to_dto(mrn) for mrn in schema.referring_mrns]
            if schema.referring_mrns
            else []
        ),
        mpu_id=schema.mpu_id,
        nhid=schema.nhid,
        tenant_id=user_context.tenant_id,
    )
