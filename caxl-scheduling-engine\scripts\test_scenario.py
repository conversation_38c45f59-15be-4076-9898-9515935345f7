#!/usr/bin/env python3
"""
Test script for CareAxl Scheduling Engine scenarios.

This script loads test data from scenario folders and runs scheduling tests,
similar to the original appointment-scheduler test system.
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, List
import logging

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.data.data_loader import SchedulingDataLoader, list_available_scenarios
from src.application.services.scheduling_engine import SchedulingEngine
from src.infrastructure.config.config_manager import config_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ScenarioTester:
    """Test runner for scheduling scenarios."""
    
    def __init__(self):
        self.engine = SchedulingEngine()
        self.results = {}
    
    async def run_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """Run a complete test scenario."""
        logger.info(f"🚀 Running scenario: {scenario_name}")
        
        try:
            # Load scenario data
            scenario_path = f"data/scenarios/{scenario_name}"
            loader = SchedulingDataLoader(scenario_path)
            data = loader.load_all_data()
            
            logger.info(f"📊 Loaded data: {len(data['providers'])} providers, "
                       f"{len(data['consumers'])} patients, {len(data['appointments'])} appointments")
            
            # Update external service clients with test data
            self._update_service_clients(data)
            
            # Run tests
            results = {
                'scenario': scenario_name,
                'data_summary': {
                    'providers': len(data['providers']),
                    'consumers': len(data['consumers']),
                    'appointments': len(data['appointments'])
                },
                'tests': {}
            }
            
            # Test 1: Assignment
            logger.info("🔄 Testing appointment assignment...")
            assignment_result = await self._test_assignment(data)
            results['tests']['assignment'] = assignment_result
            
            # Test 2: Day Plan
            logger.info("🗓️ Testing day plan optimization...")
            dayplan_result = await self._test_dayplan(data)
            results['tests']['dayplan'] = dayplan_result
            
            # Test 3: Pinned Appointments (if applicable)
            pinned_appointments = [apt for apt in data['appointments'] if apt.get('is_pinned', False)]
            if pinned_appointments:
                logger.info("📌 Testing pinned appointments...")
                pinned_result = await self._test_pinned_appointments(data, pinned_appointments)
                results['tests']['pinned_appointments'] = pinned_result
            
            # Test 4: Replan (if applicable)
            if len(data['appointments']) > 1:
                logger.info("🔄 Testing replanning...")
                replan_result = await self._test_replan(data)
                results['tests']['replan'] = replan_result
            
            logger.info(f"✅ Scenario {scenario_name} completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"❌ Scenario {scenario_name} failed: {e}")
            return {
                'scenario': scenario_name,
                'error': str(e),
                'success': False
            }
    
    def _update_service_clients(self, data: Dict[str, Any]):
        """Update external service clients with test data."""
        # In a real implementation, this would populate the external services
        # For now, the mock clients will use their hardcoded data
        logger.info("📡 Updated external service clients with test data")
    
    async def _test_assignment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test appointment assignment."""
        start_time = time.time()
        
        # Get pending appointments
        pending_appointments = [apt for apt in data['appointments'] if apt.get('status') == 'pending']
        appointment_ids = [apt['id'] for apt in pending_appointments]
        
        if not appointment_ids:
            return {
                'success': False,
                'message': 'No pending appointments to assign',
                'duration_seconds': 0
            }
        
        # Run assignment
        result = await self.engine.assign_appointments(
            appointment_ids=appointment_ids,
            constraints={}
        )
        
        duration = time.time() - start_time
        
        return {
            'success': result.get('success', False),
            'message': result.get('message', ''),
            'assigned_count': result.get('assigned_count', 0),
            'failed_count': result.get('failed_count', 0),
            'duration_seconds': duration,
            'assignments': result.get('assignments', [])[:3],  # Show first 3 for brevity
            'constraints_applied': result.get('details', {}).get('constraints_applied', [])
        }
    
    async def _test_dayplan(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test day plan optimization."""
        start_time = time.time()
        
        # Run day plan for tomorrow
        result = await self.engine.run_day_plan(
            target_date="2025-06-25",
            provider_ids=None
        )
        
        duration = time.time() - start_time
        
        return {
            'success': result.get('success', False),
            'message': result.get('message', ''),
            'optimized_count': result.get('optimized_count', 0),
            'duration_seconds': duration,
            'visit_schedules_count': len(result.get('visit_schedules', [])),
            'sample_schedule': result.get('visit_schedules', [])[:1]  # Show first schedule
        }
    
    async def _test_pinned_appointments(self, data: Dict[str, Any], pinned_appointments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test pinned appointment handling."""
        start_time = time.time()
        
        # Verify pinned appointments maintain their times
        pinned_ids = [apt['id'] for apt in pinned_appointments]
        
        # Run assignment including pinned appointments
        result = await self.engine.assign_appointments(
            appointment_ids=pinned_ids,
            constraints={}
        )
        
        duration = time.time() - start_time
        
        # Check if pinned appointments kept their times
        assignments = result.get('assignments', [])
        pinned_preserved = 0
        
        for assignment in assignments:
            apt_id = assignment.get('appointment_id')
            original_apt = next((apt for apt in pinned_appointments if apt['id'] == apt_id), None)
            
            if original_apt and original_apt.get('is_pinned'):
                if assignment.get('assigned_time') == original_apt.get('pinned_time'):
                    pinned_preserved += 1
        
        return {
            'success': result.get('success', False),
            'message': result.get('message', ''),
            'total_pinned': len(pinned_appointments),
            'pinned_preserved': pinned_preserved,
            'duration_seconds': duration,
            'pinned_details': [
                {
                    'id': apt['id'],
                    'pinned_time': apt.get('pinned_time'),
                    'reason': apt.get('pinned_reason')
                }
                for apt in pinned_appointments
            ]
        }
    
    async def _test_replan(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test replanning functionality."""
        start_time = time.time()
        
        # Take first 2 appointments for replanning test
        appointments = data['appointments'][:2]
        appointment_ids = [apt['id'] for apt in appointments]
        
        # Test patient request replanning
        result = await self.engine.replan_appointments(
            appointment_ids=appointment_ids,
            reason="patient_request",
            preferred_date="2025-06-27"
        )
        
        duration = time.time() - start_time
        
        return {
            'success': result.get('success', False),
            'message': result.get('message', ''),
            'replanned_count': result.get('replanned_count', 0),
            'failed_count': result.get('failed_count', 0),
            'duration_seconds': duration,
            'new_assignments': result.get('new_assignments', [])
        }


async def main():
    """Main test runner."""
    if len(sys.argv) < 2:
        print("Usage: python test_scenario.py <scenario_name> [--all]")
        print("\nAvailable scenarios:")
        scenarios = list_available_scenarios()
        for scenario in scenarios:
            print(f"  - {scenario['name']}: {scenario['description']}")
        return
    
    tester = ScenarioTester()
    
    if sys.argv[1] == "--all":
        # Run all scenarios
        scenarios = list_available_scenarios()
        for scenario in scenarios:
            result = await tester.run_scenario(scenario['name'])
            print(f"\n{'='*60}")
            print(f"SCENARIO: {scenario['name']}")
            print(f"{'='*60}")
            print(json.dumps(result, indent=2))
    else:
        # Run specific scenario
        scenario_name = sys.argv[1]
        result = await tester.run_scenario(scenario_name)
        print(json.dumps(result, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
