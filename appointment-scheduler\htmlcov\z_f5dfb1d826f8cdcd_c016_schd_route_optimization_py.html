<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\appointment_scheduler\constraints\c016_schd_route_optimization.py: 26%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\appointment_scheduler\constraints\c016_schd_route_optimization.py</b>:
            <span class="pc_cov">26%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">78 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">20<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">58<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_f5dfb1d826f8cdcd_day_constraints_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Route Optimization Constraints for Day Planning (C016)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">This module provides route optimization constraints for the day plan stage.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">Using simplified constraint patterns to avoid Timefold API issues.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">timefold</span><span class="op">.</span><span class="nam">solver</span><span class="op">.</span><span class="nam">score</span> <span class="key">import</span> <span class="nam">ConstraintFactory</span><span class="op">,</span> <span class="nam">HardSoftScore</span><span class="op">,</span> <span class="nam">Constraint</span><span class="op">,</span> <span class="nam">Joiners</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">planning_models</span> <span class="key">import</span> <span class="nam">TimeSlotAssignment</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">base_constraints</span> <span class="key">import</span> <span class="nam">_calculate_travel_time_between_appointments</span><span class="op">,</span> <span class="nam">_calculate_distance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="nam">PROVIDER_CAPACITY</span> <span class="op">=</span> <span class="str">"providerCapacity"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">BREAK_TIME_VIOLATION</span> <span class="op">=</span> <span class="str">"breakTimeViolation"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">MINIMIZE_TRAVEL_TIME</span> <span class="op">=</span> <span class="str">"minimizeTravelTime"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="nam">GEOGRAPHIC_CLUSTERING</span> <span class="op">=</span> <span class="str">"geographicClustering"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">def</span> <span class="nam">route_optimization_constraints</span><span class="op">(</span><span class="nam">factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="str">"""Return route optimization constraints."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="key">return</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">        <span class="nam">provider_capacity</span><span class="op">(</span><span class="nam">factory</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">        <span class="nam">break_time_violation</span><span class="op">(</span><span class="nam">factory</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">        <span class="nam">minimize_travel_time</span><span class="op">(</span><span class="nam">factory</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">        <span class="nam">geographic_clustering</span><span class="op">(</span><span class="nam">factory</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="com"># --- Hard constraints ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="key">def</span> <span class="nam">provider_capacity</span><span class="op">(</span><span class="nam">factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Constraint</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="str">"""Providers must not exceed their daily capacity limits."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="nam">factory</span><span class="op">.</span><span class="nam">for_each</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">              <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">:</span> <span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">provider</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">id</span> <span class="op">!=</span> <span class="nam">b</span><span class="op">.</span><span class="nam">id</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">                             <span class="nam">a</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">                             <span class="nam">b</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">                             <span class="nam">_exceeds_capacity</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="op">.</span><span class="nam">penalize</span><span class="op">(</span><span class="nam">HardSoftScore</span><span class="op">.</span><span class="nam">ONE_HARD</span><span class="op">,</span> <span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">_calculate_capacity_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="op">.</span><span class="nam">as_constraint</span><span class="op">(</span><span class="nam">PROVIDER_CAPACITY</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="key">def</span> <span class="nam">break_time_violation</span><span class="op">(</span><span class="nam">factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Constraint</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="str">"""Providers must have adequate breaks between appointments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="nam">factory</span><span class="op">.</span><span class="nam">for_each</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">              <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">:</span> <span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">provider</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">id</span> <span class="op">!=</span> <span class="nam">b</span><span class="op">.</span><span class="nam">id</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">                             <span class="nam">a</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">                             <span class="nam">b</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">                             <span class="nam">_has_insufficient_break_time</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="op">.</span><span class="nam">penalize</span><span class="op">(</span><span class="nam">HardSoftScore</span><span class="op">.</span><span class="nam">ONE_HARD</span><span class="op">,</span> <span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">_calculate_break_violation_minutes</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="op">.</span><span class="nam">as_constraint</span><span class="op">(</span><span class="nam">BREAK_TIME_VIOLATION</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="com"># --- Soft constraints ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t"><span class="key">def</span> <span class="nam">minimize_travel_time</span><span class="op">(</span><span class="nam">factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Constraint</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="str">"""Minimize total travel time across all providers."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="nam">factory</span><span class="op">.</span><span class="nam">for_each</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">        <span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">              <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">:</span> <span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">provider</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">        <span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">id</span> <span class="op">!=</span> <span class="nam">b</span><span class="op">.</span><span class="nam">id</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                             <span class="nam">a</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                             <span class="nam">b</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="op">.</span><span class="nam">penalize</span><span class="op">(</span><span class="nam">HardSoftScore</span><span class="op">.</span><span class="nam">ONE_SOFT</span><span class="op">,</span> <span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">_travel_time_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">        <span class="op">.</span><span class="nam">as_constraint</span><span class="op">(</span><span class="nam">MINIMIZE_TRAVEL_TIME</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="key">def</span> <span class="nam">geographic_clustering</span><span class="op">(</span><span class="nam">factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Constraint</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">    <span class="str">"""Prefer geographic clustering of appointments for each provider."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">        <span class="nam">factory</span><span class="op">.</span><span class="nam">for_each</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">        <span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">TimeSlotAssignment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">              <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">:</span> <span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">provider</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">id</span> <span class="op">!=</span> <span class="nam">b</span><span class="op">.</span><span class="nam">id</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">                             <span class="nam">a</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">                             <span class="nam">b</span><span class="op">.</span><span class="nam">time_slot</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                             <span class="nam">_has_clustering_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">        <span class="op">.</span><span class="nam">penalize</span><span class="op">(</span><span class="nam">HardSoftScore</span><span class="op">.</span><span class="nam">ONE_SOFT</span><span class="op">,</span> <span class="key">lambda</span> <span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">_calculate_clustering_penalty_for_pair</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="nam">b</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">        <span class="op">.</span><span class="nam">as_constraint</span><span class="op">(</span><span class="nam">GEOGRAPHIC_CLUSTERING</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t"><span class="com"># --- Helper functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t"><span class="key">def</span> <span class="nam">_exceeds_capacity</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="str">"""Check if provider exceeds capacity with these assignments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">    <span class="com"># This is a simplified check - in a real implementation, you'd need to count all assignments</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">    <span class="com"># For now, we'll just check if both assignments have high task points</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">    <span class="nam">points_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'task_points'</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">    <span class="nam">points_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'task_points'</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span><span class="nam">points_a</span> <span class="op">+</span> <span class="nam">points_b</span><span class="op">)</span> <span class="op">></span> <span class="num">20</span>  <span class="com"># Simplified threshold</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t"><span class="key">def</span> <span class="nam">_calculate_capacity_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="str">"""Calculate capacity penalty for this pair of assignments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">    <span class="nam">points_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'task_points'</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">    <span class="nam">points_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">.</span><span class="nam">scheduled_appointment</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'task_points'</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="key">return</span> <span class="nam">max</span><span class="op">(</span><span class="num">0</span><span class="op">,</span> <span class="op">(</span><span class="nam">points_a</span> <span class="op">+</span> <span class="nam">points_b</span><span class="op">)</span> <span class="op">-</span> <span class="num">20</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t"><span class="key">def</span> <span class="nam">_has_insufficient_break_time</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="str">"""Check if there's insufficient break time between appointments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">    <span class="nam">a_time_slot</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="nam">b_time_slot</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="key">if</span> <span class="nam">a_time_slot</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">b_time_slot</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="nam">end_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a_time_slot</span><span class="op">,</span> <span class="str">'end_time'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="nam">start_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b_time_slot</span><span class="op">,</span> <span class="str">'start_time'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">    <span class="key">if</span> <span class="nam">end_a</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">start_b</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">end_a</span> <span class="op">>=</span> <span class="nam">start_b</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="nam">gap</span> <span class="op">=</span> <span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">combine</span><span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">today</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">start_b</span><span class="op">)</span> <span class="op">-</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">           <span class="nam">datetime</span><span class="op">.</span><span class="nam">combine</span><span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">today</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">end_a</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">total_seconds</span><span class="op">(</span><span class="op">)</span> <span class="op">/</span> <span class="num">60</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="nam">min_break</span> <span class="op">=</span> <span class="num">15</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="nam">provider</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span><span class="op">,</span> <span class="str">'provider'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="key">if</span> <span class="nam">provider</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">provider</span><span class="op">,</span> <span class="str">'capacity'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">        <span class="nam">min_break</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">provider</span><span class="op">.</span><span class="nam">capacity</span><span class="op">,</span> <span class="str">'min_break_between_tasks'</span><span class="op">,</span> <span class="num">15</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">    <span class="key">return</span> <span class="nam">bool</span><span class="op">(</span><span class="nam">gap</span> <span class="op">&lt;</span> <span class="nam">min_break</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t"><span class="key">def</span> <span class="nam">_calculate_break_violation_minutes</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="str">"""Calculate break violation penalty in minutes."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">    <span class="nam">a_time_slot</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">    <span class="nam">b_time_slot</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="nam">end_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a_time_slot</span><span class="op">,</span> <span class="str">'end_time'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">if</span> <span class="nam">a_time_slot</span> <span class="key">else</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="nam">start_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b_time_slot</span><span class="op">,</span> <span class="str">'start_time'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">if</span> <span class="nam">b_time_slot</span> <span class="key">else</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">    <span class="key">if</span> <span class="nam">end_a</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">start_b</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">    <span class="nam">gap</span> <span class="op">=</span> <span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">combine</span><span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">today</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">start_b</span><span class="op">)</span> <span class="op">-</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">           <span class="nam">datetime</span><span class="op">.</span><span class="nam">combine</span><span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">today</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">end_a</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">total_seconds</span><span class="op">(</span><span class="op">)</span> <span class="op">/</span> <span class="num">60</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="nam">min_break</span> <span class="op">=</span> <span class="num">15</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">    <span class="nam">provider</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span><span class="op">,</span> <span class="str">'provider'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">    <span class="key">if</span> <span class="nam">provider</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">provider</span><span class="op">,</span> <span class="str">'capacity'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="nam">min_break</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">provider</span><span class="op">.</span><span class="nam">capacity</span><span class="op">,</span> <span class="str">'min_break_between_tasks'</span><span class="op">,</span> <span class="num">15</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">    <span class="key">return</span> <span class="nam">int</span><span class="op">(</span><span class="nam">max</span><span class="op">(</span><span class="num">0</span><span class="op">,</span> <span class="nam">min_break</span> <span class="op">-</span> <span class="nam">gap</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="key">def</span> <span class="nam">_travel_time_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="str">"""Calculate travel time penalty between two appointments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="nam">appointment_data_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="nam">appointment_data_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">    <span class="key">if</span> <span class="nam">appointment_data_a</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">appointment_data_b</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="com"># Get time and date context for traffic calculation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="nam">time_slot_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">    <span class="nam">time_slot_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">    <span class="nam">target_time</span> <span class="op">=</span> <span class="nam">time_slot_a</span> <span class="key">if</span> <span class="nam">time_slot_a</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">else</span> <span class="nam">time_slot_b</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="nam">target_date</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">appointment_data_a</span><span class="op">,</span> <span class="str">'assigned_date'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">    <span class="key">return</span> <span class="nam">_calculate_travel_time_between_appointments</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="nam">appointment_data_a</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="nam">appointment_data_b</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="nam">target_time</span><span class="op">=</span><span class="nam">target_time</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="nam">target_date</span><span class="op">=</span><span class="nam">target_date</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t"><span class="key">def</span> <span class="nam">_has_clustering_penalty</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">    <span class="str">"""Check if this pair contributes to clustering penalty."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">    <span class="com"># Only consider pairs that have time slots assigned</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">    <span class="key">if</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'time_slot'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">    <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t"><span class="key">def</span> <span class="nam">_calculate_clustering_penalty_for_pair</span><span class="op">(</span><span class="nam">a</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">,</span> <span class="nam">b</span><span class="op">:</span> <span class="nam">TimeSlotAssignment</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">    <span class="str">"""Calculate clustering penalty for this pair of assignments."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">    <span class="nam">appointment_data_a</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">a</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">    <span class="nam">appointment_data_b</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">b</span><span class="op">,</span> <span class="str">'scheduled_appointment'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">    <span class="key">if</span> <span class="nam">appointment_data_a</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">appointment_data_b</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">        <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="nam">loc1</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">appointment_data_a</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'location'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">    <span class="nam">loc2</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">appointment_data_b</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">,</span> <span class="str">'location'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="key">if</span> <span class="nam">loc1</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="nam">loc2</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="key">return</span> <span class="nam">int</span><span class="op">(</span><span class="nam">_calculate_distance</span><span class="op">(</span><span class="nam">loc1</span><span class="op">,</span> <span class="nam">loc2</span><span class="op">)</span> <span class="op">*</span> <span class="num">1000</span><span class="op">)</span>  <span class="com"># scale for penalty</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">    <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_f5dfb1d826f8cdcd_day_constraints_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
</footer>
</body>
</html>
