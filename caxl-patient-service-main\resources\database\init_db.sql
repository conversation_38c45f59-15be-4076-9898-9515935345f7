-- Create enum types
CREATE TYPE record_status AS ENUM (
    'ACTIVE',
    'INACTIVE',
    'PENDING',
    'ARCHIVED',
    'DELETED'
);

CREATE TYPE caregiver_relationship AS ENUM (
    'SPOUSE',
    'CHILD',
    'PARENT',
    'SIBLING',
    'FRIEND',
    'OTHER'
);

-- Create tables

CREATE TABLE IF NOT EXISTS patient_pii (
    patient_pii_id UUID PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    gender VARCHAR(10) NOT NULL,
    dob DATE NOT NULL CHECK (dob < CURRENT_DATE),
    ssn VARCHAR(11),
    ethnicity VARCHAR(50),
    race VARCHAR(50),
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL
);

CREATE TABLE IF NOT EXISTS patient_pii_email (
    patient_pii_email_id UUID PRIMARY KEY,
    patient_pii_id UUID NOT NULL,
    email VARCHAR(255) NOT NULL,
    email_type VARCHAR(50) NOT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_pii_id) REFERENCES patient_pii(patient_pii_id)
);

CREATE TABLE IF NOT EXISTS patient_pii_phone (
    patient_pii_phone_id UUID PRIMARY KEY,
    patient_pii_id UUID NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    phone_country_code VARCHAR(10) NOT NULL,
    phone_type VARCHAR(50),
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    preferred_for_sms BOOLEAN DEFAULT FALSE,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_pii_id) REFERENCES patient_pii(patient_pii_id)
);

CREATE TABLE IF NOT EXISTS patient_pii_location (
    patient_pii_location_id UUID PRIMARY KEY,
    patient_pii_id UUID NOT NULL,
    location_name VARCHAR(255) NOT NULL,
    location_id UUID NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_pii_id) REFERENCES patient_pii(patient_pii_id)
);

CREATE TABLE IF NOT EXISTS patient_pref (
    patient_pref_id UUID PRIMARY KEY,
    pref_language VARCHAR(50),
    lab_name VARCHAR(255),
    lab_location_id UUID,
    lab_phone VARCHAR(20),
    lab_phone_country_code VARCHAR(10),
    lab_email VARCHAR(255),
    pharmacy_name VARCHAR(255),
    pharmacy_location_id UUID,
    pharmacy_phone VARCHAR(20),
    pharmacy_email VARCHAR(255),
    time_pref JSONB,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL
);

CREATE TABLE IF NOT EXISTS patient_profile (
    patient_profile_id UUID PRIMARY KEY,
    insurance_name VARCHAR(255),
    insurance_type VARCHAR(50),
    insurance_number VARCHAR(50),
    clinical_diagnosis TEXT,
    medical_history TEXT,
    social_history TEXT,
    allergies TEXT,
    notes TEXT,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL
);

CREATE TABLE IF NOT EXISTS patient_emergencycontact_pii (
    patient_emergencycontact_pii_id UUID PRIMARY KEY,
    patient_pii_id UUID NOT NULL,
    relationship VARCHAR(50),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    phone_country_code VARCHAR(10),
    email VARCHAR(255),
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_pii_id) REFERENCES patient_pii(patient_pii_id)
);

CREATE TABLE IF NOT EXISTS patient (
    patient_id UUID PRIMARY KEY,
    mpu_id VARCHAR(255),
    nhid VARCHAR(255),
    patient_profile_id UUID,
    patient_pref_id UUID,
    patient_pii_id UUID,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_profile_id) REFERENCES patient_profile(patient_profile_id),
    FOREIGN KEY (patient_pref_id) REFERENCES patient_pref(patient_pref_id),
    FOREIGN KEY (patient_pii_id) REFERENCES patient_pii(patient_pii_id)
);

CREATE TABLE IF NOT EXISTS patient_caregiver (
    patient_caregiver_id UUID PRIMARY KEY,
    patient_id UUID NOT NULL,
    caregiver_relationship caregiver_relationship NOT NULL,
    caregiver_firstname VARCHAR(100),
    caregiver_lastname VARCHAR(100),
    caregiver_gender VARCHAR(10),
    caregiver_email VARCHAR(255),
    caregiver_email_verified BOOLEAN,
    caregiver_phone_number VARCHAR(20),
    caregiver_phone_country_code VARCHAR(10),
    caregiver_phone_verified BOOLEAN,
    location_id UUID,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_id) REFERENCES patient(patient_id)
);

CREATE TABLE IF NOT EXISTS patient_referring_mrn (
    patient_referring_id UUID PRIMARY KEY,
    patient_id UUID NOT NULL,
    referring_mrn VARCHAR(255),
    referring_name VARCHAR(255),
    referring_state VARCHAR(50),
    referring_hospital VARCHAR(255),
    referring_npi VARCHAR(50),
    inpatient_discharge_date DATE,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(30) NOT NULL,
    FOREIGN KEY (patient_id) REFERENCES patient(patient_id)
);

CREATE TABLE IF NOT EXISTS patient_episode_of_care (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL REFERENCES patient(patient_id) ON DELETE CASCADE,
    patient_mrn_id UUID,
    location_id UUID,
    discharge_summary TEXT,
    clinical_diagnosis TEXT,
    date_of_start_of_care TIMESTAMP WITH TIME ZONE,
    insurance_name VARCHAR(100),
    insurance_type VARCHAR(50),
    insurance_number VARCHAR(50),
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(100) NOT NULL
);

-- Create patient_episode_of_care_order table
CREATE TABLE IF NOT EXISTS patient_episode_of_care_order (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_episode_of_care_id UUID NOT NULL REFERENCES patient_episode_of_care(id) ON DELETE CASCADE,
    order_status VARCHAR(50) NOT NULL,
    order_date TIMESTAMP WITH TIME ZONE NOT NULL,
    order_notes TEXT,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(100) NOT NULL,
    CONSTRAINT fk_patient_episode_of_care FOREIGN KEY (patient_episode_of_care_id) REFERENCES patient_episode_of_care(id) ON DELETE CASCADE
);

-- Create patient_episode_of_care_order_directive table
CREATE TABLE IF NOT EXISTS patient_episode_of_care_order_directive (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_episode_of_care_order_id UUID NOT NULL REFERENCES patient_episode_of_care_order(id) ON DELETE CASCADE,
    instructions TEXT NOT NULL,
    mod_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mod_by VARCHAR(100) NOT NULL,
    mod_service VARCHAR(100) NOT NULL,
    record_status record_status DEFAULT 'ACTIVE',
    tenant_id VARCHAR(100) NOT NULL,
    CONSTRAINT fk_patient_episode_of_care_order FOREIGN KEY (patient_episode_of_care_order_id) REFERENCES patient_episode_of_care_order(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_patient_mpu_id ON patient(mpu_id);
CREATE INDEX idx_patient_nhid ON patient(nhid);
CREATE INDEX idx_patient_pii_ssn ON patient_pii(ssn);
CREATE INDEX idx_patient_pii_email_patient_id ON patient_pii_email(patient_pii_id);
CREATE INDEX idx_patient_pii_phone_patient_id ON patient_pii_phone(patient_pii_id);
CREATE INDEX idx_patient_pii_location_patient_id ON patient_pii_location(patient_pii_id);
CREATE INDEX idx_patient_caregiver_patient_id ON patient_caregiver(patient_id);
CREATE INDEX idx_patient_emergencycontact_pii_patient_id ON patient_emergencycontact_pii(patient_pii_id);
CREATE INDEX idx_patient_referring_mrn_patient_id ON patient_referring_mrn(patient_id);
CREATE INDEX idx_patient_episode_of_care_patient_id ON patient_episode_of_care(patient_id);
CREATE INDEX idx_patient_episode_of_care_patient_mrn_id ON patient_episode_of_care(patient_mrn_id);
CREATE INDEX idx_patient_episode_of_care_location_id ON patient_episode_of_care(location_id);
CREATE INDEX idx_patient_episode_of_care_record_status ON patient_episode_of_care(record_status);
CREATE INDEX idx_patient_episode_of_care_date_of_start_of_care ON patient_episode_of_care(date_of_start_of_care);

-- Add indexes for patient_episode_of_care_order table
CREATE INDEX idx_patient_episode_of_care_order_episode_id ON patient_episode_of_care_order(patient_episode_of_care_id);
CREATE INDEX idx_patient_episode_of_care_order_status ON patient_episode_of_care_order(order_status);
CREATE INDEX idx_patient_episode_of_care_order_date ON patient_episode_of_care_order(order_date);
CREATE INDEX idx_patient_episode_of_care_order_record_status ON patient_episode_of_care_order(record_status);
CREATE INDEX idx_patient_episode_of_care_order_tenant_id ON patient_episode_of_care_order(tenant_id);

-- Add indexes for patient_episode_of_care_order_directive table
CREATE INDEX idx_patient_episode_of_care_order_directive_order_id ON patient_episode_of_care_order_directive(patient_episode_of_care_order_id);
CREATE INDEX idx_patient_episode_of_care_order_directive_record_status ON patient_episode_of_care_order_directive(record_status);
CREATE INDEX idx_patient_episode_of_care_order_directive_tenant_id ON patient_episode_of_care_order_directive(tenant_id);

-- Add constraints
ALTER TABLE patient_pii
    ADD CONSTRAINT chk_gender CHECK (gender IN ('MALE', 'FEMALE', 'OTHER'));

ALTER TABLE patient_pii_email
    ADD CONSTRAINT chk_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE patient_pii_phone
    ADD CONSTRAINT chk_phone_number CHECK (phone_number ~ '^\+?[1-9]\d{1,14}$');

ALTER TABLE patient_caregiver
    ADD CONSTRAINT chk_caregiver_gender CHECK (caregiver_gender IN ('MALE', 'FEMALE', 'OTHER'));

ALTER TABLE patient_caregiver
    ADD CONSTRAINT chk_caregiver_email CHECK (caregiver_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE patient_caregiver
    ADD CONSTRAINT chk_caregiver_phone CHECK (caregiver_phone_number ~ '^\+?[1-9]\d{1,14}$');

ALTER TABLE patient_emergencycontact_pii
    ADD CONSTRAINT chk_emergency_phone CHECK (phone_number ~ '^\+?[1-9]\d{1,14}$');

ALTER TABLE patient_emergencycontact_pii
    ADD CONSTRAINT chk_emergency_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
