<!--
Pull Request Template for GitHub

This template helps contributors provide consistent, high-quality PR descriptions.
It contains required sections, explanations, and checklist items.

Keep comments to contributors as Markdown comments (they won't appear in the rendered view).
-->

## Summary

<!--
Summary of the changes introduced
-->

---

## Linked Issue(s)

<!--
Use GitHub’s syntax to link and auto-close issues:
Fixes JIRA-ID#123
Why: Auto-closes the ticket on merge and keeps traceability
-->

Fixes JIRA-ID#123

---

## Description & Screenshots (if required)

<!--
Include:
- Problem Statement: Describe the root cause or user problem
- High-level Solution: What was done to solve it
- Scope: What's included / excluded to avoid scope creep
- Optional: Insert screenshots or diagrams if UI or architecture changes
Why: Gives reviewers context and prevents scope creep
-->

- **Problem Statement**:

- **High-level Solution**:

- **Scope** (What's included / excluded):

_Optional – Screenshots or diagrams:_

---

## Implementation Details

<!--
Include:
- Key Classes/Modules Touched: List main classes, modules, or files changed
- Design Patterns or Libraries Introduced: List any new design patterns, libraries, or frameworks
Why: Speeds up code review and future maintenance
-->

- **Key Classes/Modules Touched**:

- **Design Patterns or Libraries Introduced**:

---

## Testing & Verification

<!--
Include:
- Unit/Integration Tests: Describe test coverage and key scenarios
- Manual Steps & Expected Results: Include specific manual test steps and expected outcomes
- Screenshots/Logs (if applicable): For UI/API changes
Why: Ensures the PR is safe to merge
-->

- **Unit/Integration Tests**:

- **Manual Steps & Expected Results**:

- **Screenshots/Logs (if applicable)**:

---

## Backward Compatibility / Migrations

<!--
Indicate:
- DB Migrations? Yes/No
- Config Changes? Yes/No
Why: Alerts DevOps and QA early
-->

- **DB Migrations**: Yes / No
- **Config Changes**: Yes / No

---

## Checklist

<!--
Contributor should self-verify before marking a PR as ready for review.
Why: Provides a self-serve quality gate
-->

- [ ] Code builds locally
- [ ] Lint & tests pass
- [ ] Docs updated if needed
