"""API v1 routes"""

from fastapi import APIRouter

from . import appointments, providers, consumers, scheduling

api_router = APIRouter()

# Include all route modules
api_router.include_router(appointments.router, prefix="/appointments", tags=["appointments"])
api_router.include_router(providers.router, prefix="/providers", tags=["providers"])
api_router.include_router(consumers.router, prefix="/consumers", tags=["consumers"])
api_router.include_router(scheduling.router, prefix="/scheduling", tags=["scheduling"])
