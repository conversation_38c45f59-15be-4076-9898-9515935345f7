"""Patient domain events"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any
from uuid import UUID, uuid4

from src.domain.foundations.events.domain_event import DomainEvent


@dataclass(frozen=True, kw_only=True)
class PatientCreatedEvent(DomainEvent):
    """Event raised when a new patient is created"""

    aggregate_id: UUID = field()
    event_type: str = field(default="PatientCreated")
    event_id: UUID = field(default_factory=uuid4)
    occurred_on: datetime = field(default_factory=datetime.now)


@dataclass(frozen=True, kw_only=True)
class PatientUpdatedEvent(DomainEvent):
    """Event raised when a patient is updated"""

    aggregate_id: UUID = field()
    event_id: UUID = field(default_factory=uuid4)
    event_type: str = field(default="PatientUpdated")
    occurred_on: datetime = field(default_factory=datetime.now)
    updated_fields: dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True, kw_only=True)
class CaregiverAssignedEvent(DomainEvent):
    """Event raised when a caregiver is assigned to a patient"""

    aggregate_id: UUID = field()
    caregiver_id: UUID = field()
    event_id: UUID = field(default_factory=uuid4)
    event_type: str = field(default="CaregiverAssigned")
    occurred_on: datetime = field(default_factory=datetime.now)
