# Continuity of Care Scenario

**Purpose**: Demonstrate continuity of care optimization
**Best for**: Showing how the system maintains provider-patient relationships
**Complexity**: Medium

## Features Demonstrated
- Continuity of care optimization
- Provider-patient relationship maintenance
- Care episode grouping
- Historical assignment consideration

## Data Overview
- **Providers**: 3 (with established patient relationships)
- **Patients**: 6 (with multiple appointments in care episodes)
- **Appointments**: 12 (grouped by care episodes)
- **Care Episodes**: 3 distinct episodes

## Care Episodes
1. **Episode A**: Patient with diabetes management (4 appointments)
2. **Episode B**: Patient with wound care (3 appointments)
3. **Episode C**: Patient with mobility assistance (5 appointments)

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/continuity_of_care/* data/

# Enable continuity of care
# Edit config/scheduler.yml: enable_continuity_of_care: true

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Patients should be assigned to the same provider across their care episode
- Provider-patient relationships should be maintained
- Care episodes should be grouped together
- Continuity should be prioritized over other soft constraints 