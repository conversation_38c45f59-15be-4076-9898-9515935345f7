#!/usr/bin/env python3
"""
Scenario Management Utility

This script helps you easily switch between different test scenarios
for the healthcare appointment scheduling system.
"""

import os
import shutil
import sys
import yaml
from pathlib import Path


def list_scenarios():
    """List all available scenarios."""
    scenarios_dir = Path("data/scenarios")
    if not scenarios_dir.exists():
        print("[ERROR] No scenarios directory found!")
        return
    
    print("[LIST] Available Scenarios:")
    print("=" * 50)
    
    for scenario_dir in sorted(scenarios_dir.iterdir()):
        if scenario_dir.is_dir():
            readme_file = scenario_dir / "README.md"
            if readme_file.exists():
                with open(readme_file, 'r') as f:
                    first_line = f.readline().strip()
                    if first_line.startswith('#'):
                        title = first_line[1:].strip()
                    else:
                        title = scenario_dir.name.replace('_', ' ').title()
            else:
                title = scenario_dir.name.replace('_', ' ').title()
            
            print(f"  [DIR] {scenario_dir.name}")
            print(f"     {title}")
            print()


def switch_scenario(scenario_name):
    """Switch to a specific scenario."""
    scenarios_dir = Path("data/scenarios")
    data_dir = Path("data")
    
    scenario_path = scenarios_dir / scenario_name
    
    if not scenario_path.exists():
        print(f"[ERROR] Scenario '{scenario_name}' not found!")
        print(f"Available scenarios:")
        list_scenarios()
        return False
    
    # Backup current data
    backup_dir = data_dir / "backup"
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Create backup of current data files
    backup_files = []
    for file_name in ["providers.yml", "consumers.yml", "appointments.yml"]:
        file_path = data_dir / file_name
        if file_path.exists():
            backup_files.append(file_name)
            shutil.copy2(file_path, backup_dir / file_name)
    
    if backup_files:
        print(f"[BACKUP] Backed up current data: {', '.join(backup_files)}")
    
    # Copy scenario data
    print(f"[SWITCH] Switching to scenario: {scenario_name}")
    
    for file_name in ["providers.yml", "consumers.yml", "appointments.yml"]:
        src_file = scenario_path / file_name
        dst_file = data_dir / file_name
        
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
            print(f"  [OK] Copied {file_name}")
        else:
            print(f"  [WARN] {file_name} not found in scenario")
    
    print(f"\n[SUCCESS] Successfully switched to scenario: {scenario_name}")
    
    # Show scenario info
    readme_file = scenario_path / "README.md"
    if readme_file.exists():
        print(f"\n[INFO] Scenario Information:")
        print("-" * 30)
        with open(readme_file, 'r') as f:
            lines = f.readlines()
            for line in lines[:10]:  # Show first 10 lines
                if line.strip():
                    print(line.rstrip())
    
    return True


def restore_backup():
    """Restore from backup."""
    backup_dir = Path("data/backup")
    data_dir = Path("data")
    
    if not backup_dir.exists():
        print("[ERROR] No backup found!")
        return False
    
    print("[RESTORE] Restoring from backup...")
    
    for file_name in ["providers.yml", "consumers.yml", "appointments.yml"]:
        src_file = backup_dir / file_name
        dst_file = data_dir / file_name
        
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
            print(f"  [OK] Restored {file_name}")
    
    # Clean up backup
    shutil.rmtree(backup_dir)
    print("[SUCCESS] Backup restored and cleaned up")
    
    return True


def show_current_scenario():
    """Show which scenario is currently active."""
    data_dir = Path("data")
    
    print("[CURRENT] Current Data Files:")
    print("=" * 30)
    
    for file_name in ["providers.yml", "consumers.yml", "appointments.yml"]:
        file_path = data_dir / file_name
        if file_path.exists():
            print(f"  [OK] {file_name}")
        else:
            print(f"  [MISSING] {file_name} (missing)")
    
    # Try to detect scenario by checking file contents
    print(f"\n[DETECT] Attempting to detect current scenario...")
    
    providers_file = data_dir / "providers.yml"
    if providers_file.exists():
        try:
            with open(providers_file, 'r') as f:
                data = yaml.safe_load(f)
                if data and 'providers' in data:
                    first_provider = data['providers'][0]
                    provider_id = first_provider.get('id', '')
                    
                    if provider_id.startswith('basic-'):
                        print("  [SCENARIO] Current scenario appears to be: Basic Demo")
                    elif provider_id.startswith('geo-'):
                        print("  [SCENARIO] Current scenario appears to be: Geographic Clustering")
                    elif provider_id.startswith('cont-'):
                        print("  [SCENARIO] Current scenario appears to be: Continuity of Care")
                    elif provider_id.startswith('pref-'):
                        print("  [SCENARIO] Current scenario appears to be: Patient Preferences")
                    elif provider_id.startswith('cap-'):
                        print("  [SCENARIO] Current scenario appears to be: Capacity Management")
                    elif provider_id.startswith('skill-'):
                        print("  [SCENARIO] Current scenario appears to be: Skill Hierarchy")
                    elif provider_id.startswith('avail-'):
                        print("  [SCENARIO] Current scenario appears to be: Availability Edge Cases")
                    else:
                        print("  [SCENARIO] Current scenario: Unknown (custom data)")
        except Exception as e:
            print(f"  [WARN] Could not detect scenario: {e}")


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("🏥 Healthcare Scheduling Scenario Manager")
        print("=" * 45)
        print()
        print("Usage:")
        print("  python switch_scenario.py list                    - List all scenarios")
        print("  python switch_scenario.py switch <scenario_name>  - Switch to scenario")
        print("  python switch_scenario.py current                 - Show current scenario")
        print("  python switch_scenario.py restore                 - Restore from backup")
        print()
        print("Examples:")
        print("  python switch_scenario.py switch basic_demo")
        print("  python switch_scenario.py switch geographic_clustering")
        print("  python switch_scenario.py switch continuity_of_care")
        print()
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        list_scenarios()
    elif command == "switch":
        if len(sys.argv) < 3:
            print("[ERROR] Please specify a scenario name!")
            print("Example: python switch_scenario.py switch basic_demo")
            return
        scenario_name = sys.argv[2]
        switch_scenario(scenario_name)
    elif command == "current":
        show_current_scenario()
    elif command == "restore":
        restore_backup()
    else:
        print(f"[ERROR] Unknown command: {command}")
        print("Use 'list', 'switch', 'current', or 'restore'")


if __name__ == "__main__":
    main() 