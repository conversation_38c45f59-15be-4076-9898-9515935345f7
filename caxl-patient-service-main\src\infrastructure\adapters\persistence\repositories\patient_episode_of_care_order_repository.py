"""Repository implementation for Patient Episode of Care Order"""

from uuid import UUID

from sqlalchemy import delete, select
from sqlalchemy.orm import joinedload

from src.domain.entities.patient_episode_of_care_order import PatientEpisodeOfCareOrder
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.repositories.patient_episode_of_care_order_repository import (
    PatientEpisodeOfCareOrderRepository,
)
from src.infrastructure.adapters.persistence.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order import (
    PatientEpisodeOfCareOrder as PatientEpisodeOfCareOrderModel,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective as PatientEpisodeOfCareOrderDirectiveModel,
)
from src.infrastructure.adapters.persistence.repositories.base_repository import BaseRepositoryImpl
from src.infrastructure.adapters.persistence.session import SessionManager


class PatientEpisodeOfCareOrderRepositoryImpl(
    BaseRepositoryImpl[PatientEpisodeOfCareOrder, PatientEpisodeOfCareOrderModel],
    PatientEpisodeOfCareOrderRepository,
):
    """Repository implementation for Patient Episode of Care Order"""

    def __init__(self, session_manager: SessionManager, mapper: PatientEpisodeOfCareOrderMapper):
        """Initialize repository"""
        super().__init__(session_manager, mapper)

    async def find_by_id(self, order_id: UUID) -> PatientEpisodeOfCareOrder | None:
        """Find order by ID"""
        async for session in self._session_manager.get_session():
            result = await session.execute(
                select(PatientEpisodeOfCareOrderModel)
                .where(
                    PatientEpisodeOfCareOrderModel.id == order_id,
                    PatientEpisodeOfCareOrderModel.record_status == "ACTIVE",
                )
                .options(
                    joinedload(
                        PatientEpisodeOfCareOrderModel.directives.and_(
                            PatientEpisodeOfCareOrderDirectiveModel.record_status == "ACTIVE"
                        )
                    )
                )
            )
            model = result.unique().scalar_one_or_none()
            return self._mapper.to_domain(model) if model else None

    async def find_all(self, page: int = 1, page_size: int = 10) -> list[PatientEpisodeOfCareOrder]:
        """Find all orders with pagination"""
        async for session in self._session_manager.get_session():
            query = (
                select(PatientEpisodeOfCareOrderModel)
                .where(PatientEpisodeOfCareOrderModel.record_status == "ACTIVE")
                .options(
                    joinedload(
                        PatientEpisodeOfCareOrderModel.directives.and_(
                            PatientEpisodeOfCareOrderDirectiveModel.record_status == "ACTIVE"
                        )
                    )
                )
            )
            query = query.offset((page - 1) * page_size).limit(page_size)
            result = await session.execute(query)
            models = result.unique().scalars().all()
            return [self._mapper.to_domain(model) for model in models]

    async def save(
        self, order: PatientEpisodeOfCareOrder, audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareOrder:
        """Save order"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                # First save the order without directives
                model = self._mapper.to_model(order)
                model.directives = []  # Clear directives temporarily
                session.add(model)
                await session.flush()

                # Now add the directives with the correct order ID
                if order.directives:
                    directive_models = []
                    for directive in order.directives:
                        directive_model = PatientEpisodeOfCareOrderDirectiveModel(
                            patient_episode_of_care_order_id=model.id,
                            instructions=directive.instructions,
                            tenant_id=audit_stamp.tenant_id,
                            mod_at=audit_stamp.mod_at,
                            mod_by=audit_stamp.mod_by,
                            mod_service=audit_stamp.mod_service,
                            record_status=audit_stamp.record_status.value,
                        )
                        directive_models.append(directive_model)
                        session.add(directive_model)
                    model.directives = directive_models
                    await session.flush()

                # Eagerly load the order with its directives
                result = await session.execute(
                    select(PatientEpisodeOfCareOrderModel)
                    .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                    .where(PatientEpisodeOfCareOrderModel.id == model.id)
                )
                model = result.unique().scalar_one()
                return self._mapper.to_domain(model)

    async def update(
        self, order: PatientEpisodeOfCareOrder, audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareOrder | None:
        """Update order"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                try:
                    # Get the order with its directives
                    result = await session.execute(
                        select(PatientEpisodeOfCareOrderModel)
                        .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                        .where(PatientEpisodeOfCareOrderModel.id == order.id)
                    )
                    model = result.unique().scalar_one_or_none()
                    if not model:
                        return None

                    # Update the order fields
                    model.patient_episode_of_care_id = order.patient_episode_of_care_id
                    model.order_status = order.order_status.value
                    model.order_date = order.order_date
                    model.order_notes = order.order_notes

                    # Update audit fields
                    model.mod_at = audit_stamp.mod_at
                    model.mod_by = audit_stamp.mod_by
                    model.mod_service = audit_stamp.mod_service
                    model.record_status = audit_stamp.record_status.value

                    # Remove all existing directives
                    if model.directives:
                        for directive in model.directives:
                            await session.execute(
                                delete(PatientEpisodeOfCareOrderDirectiveModel).where(
                                    PatientEpisodeOfCareOrderDirectiveModel.id == directive.id
                                )
                            )
                        model.directives = []
                        await session.flush()

                    # Add new directives
                    if order.directives:
                        directive_models = []
                        for directive in order.directives:
                            directive_model = PatientEpisodeOfCareOrderDirectiveModel(
                                patient_episode_of_care_order_id=model.id,
                                instructions=directive.instructions,
                                tenant_id=audit_stamp.tenant_id,
                                mod_at=audit_stamp.mod_at,
                                mod_by=audit_stamp.mod_by,
                                mod_service=audit_stamp.mod_service,
                                record_status=audit_stamp.record_status.value,
                            )
                            directive_models.append(directive_model)
                            session.add(directive_model)
                        model.directives = directive_models
                        await session.flush()

                    # Eagerly load the updated order with its directives
                    result = await session.execute(
                        select(PatientEpisodeOfCareOrderModel)
                        .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                        .where(PatientEpisodeOfCareOrderModel.id == model.id)
                    )
                    updated_model = result.unique().scalar_one_or_none()
                    if not updated_model:
                        return None
                    return self._mapper.to_domain(updated_model)

                except Exception as e:
                    await session.rollback()
                    raise e

    async def delete(self, order_id: UUID, audit_stamp: AuditStamp) -> bool:
        """Delete order"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                # Get the order with its directives
                result = await session.execute(
                    select(PatientEpisodeOfCareOrderModel)
                    .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                    .where(PatientEpisodeOfCareOrderModel.id == order_id)
                )
                model = result.unique().scalar_one_or_none()

                if not model:
                    return False

                # Update record status for all directives
                for directive in model.directives:
                    directive.record_status = audit_stamp.record_status
                    directive.mod_at = audit_stamp.mod_at
                    directive.mod_by = audit_stamp.mod_by
                    directive.mod_service = audit_stamp.mod_service

                # Update record status for the order
                model.record_status = audit_stamp.record_status
                model.mod_at = audit_stamp.mod_at
                model.mod_by = audit_stamp.mod_by
                model.mod_service = audit_stamp.mod_service

                return True

    async def count(self) -> int:
        """Get total count of orders"""
        async for session in self._session_manager.get_session():
            result = await session.execute(select(PatientEpisodeOfCareOrderModel))
            return len(result.unique().scalars().all())

    async def add_directives(
        self,
        order_id: UUID,
        directives: list[PatientEpisodeOfCareOrderDirectiveModel],
        audit_stamp: AuditStamp,
    ) -> PatientEpisodeOfCareOrder | None:
        """Add directives to an order"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                # Verify order exists
                result = await session.execute(
                    select(PatientEpisodeOfCareOrderModel).where(
                        PatientEpisodeOfCareOrderModel.id == order_id
                    )
                )
                if not result.unique().scalar_one_or_none():
                    return None

                # Create directive models
                for directive in directives:
                    directive_model = PatientEpisodeOfCareOrderDirectiveModel(
                        patient_episode_of_care_order_id=order_id,
                        instructions=directive.instructions,
                        tenant_id=audit_stamp.tenant_id,
                        mod_at=audit_stamp.mod_at,
                        mod_by=audit_stamp.mod_by,
                        mod_service=audit_stamp.mod_service,
                        record_status=audit_stamp.record_status.value,
                    )
                    session.add(directive_model)

                await session.flush()

                # Return the updated order
                result = await session.execute(
                    select(PatientEpisodeOfCareOrderModel)
                    .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                    .where(PatientEpisodeOfCareOrderModel.id == order_id)
                )
                model = result.unique().scalar_one()
                return self._mapper.to_domain(model)

    async def remove_directives(
        self, order_id: UUID, directive_ids: list[UUID], audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareOrder | None:
        """Remove directives from an order by updating their record status"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                # Update record status for specified directives
                stmt = select(PatientEpisodeOfCareOrderDirectiveModel).where(
                    PatientEpisodeOfCareOrderDirectiveModel.patient_episode_of_care_order_id
                    == order_id,
                    PatientEpisodeOfCareOrderDirectiveModel.id.in_(directive_ids),
                )
                result = await session.execute(stmt)
                directives = result.scalars().all()

                for directive in directives:
                    directive.record_status = "DELETED"
                    directive.mod_at = audit_stamp.mod_at
                    directive.mod_by = audit_stamp.mod_by
                    directive.mod_service = audit_stamp.mod_service

                await session.flush()

                # Return the updated order
                result = await session.execute(
                    select(PatientEpisodeOfCareOrderModel)
                    .options(joinedload(PatientEpisodeOfCareOrderModel.directives))
                    .where(PatientEpisodeOfCareOrderModel.id == order_id)
                )
                model = result.unique().scalar_one()
                return self._mapper.to_domain(model)
