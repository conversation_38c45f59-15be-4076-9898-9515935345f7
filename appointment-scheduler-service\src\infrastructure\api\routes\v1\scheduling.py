"""Scheduling API routes"""

from datetime import date
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from src.config.settings import settings


class SchedulingRequest(BaseModel):
    """Scheduling request model"""
    appointment_ids: List[str] = []
    target_date: str = ""
    constraints: Dict[str, Any] = {}


class SchedulingResponse(BaseModel):
    """Scheduling response model"""
    success: bool
    message: str
    scheduled_count: int
    failed_count: int
    processing_time_seconds: float
    details: Dict[str, Any] = {}


class DayPlanRequest(BaseModel):
    """Day plan request model"""
    target_date: str


class DayPlanResponse(BaseModel):
    """Day plan response model"""
    success: bool
    message: str
    assigned_count: int
    processing_time_seconds: float
    visit_order: List[Dict[str, Any]] = []


router = APIRouter()


@router.post(
    "/assign",
    response_model=SchedulingResponse,
    status_code=status.HTTP_200_OK,
    summary="Assign Appointments",
    description="Run appointment assignment optimization",
)
async def assign_appointments(request: SchedulingRequest) -> SchedulingResponse:
    """Assign appointments to providers"""
    
    # For now, return a mock response
    # In a full implementation, this would call the scheduling service
    return SchedulingResponse(
        success=True,
        message="Assignment completed successfully",
        scheduled_count=8,
        failed_count=0,
        processing_time_seconds=30.0,
        details={
            "algorithm": "timefold_optimization",
            "constraints_applied": ["skill_matching", "geographic_optimization", "availability"]
        }
    )


@router.post(
    "/dayplan",
    response_model=DayPlanResponse,
    status_code=status.HTTP_200_OK,
    summary="Run Day Plan",
    description="Optimize daily schedule and routing",
)
async def run_dayplan(request: DayPlanRequest) -> DayPlanResponse:
    """Run day plan optimization"""
    
    # For now, return a mock response
    # In a full implementation, this would call the day plan service
    return DayPlanResponse(
        success=True,
        message="Day plan completed successfully",
        assigned_count=6,
        processing_time_seconds=60.0,
        visit_order=[
            {
                "provider_id": "provider-1",
                "provider_name": "Sarah Johnson, RN",
                "visits": [
                    {
                        "time": "08:00-09:00",
                        "patient": "Margaret Smith",
                        "location": "New York",
                        "address": "123 Park Avenue, Apt 4B"
                    },
                    {
                        "time": "09:30-10:45",
                        "patient": "David Kim",
                        "location": "New York", 
                        "address": "321 Broadway, Apt 15A"
                    }
                ]
            }
        ]
    )


@router.get(
    "/status",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Scheduling Status",
    description="Get current scheduling system status",
)
async def get_scheduling_status() -> Dict[str, Any]:
    """Get scheduling system status"""
    
    return {
        "status": "operational",
        "solver_engine": "timefold",
        "version": settings.VERSION,
        "features": {
            "assignment_optimization": True,
            "day_plan_optimization": True,
            "route_optimization": True,
            "constraint_satisfaction": True
        },
        "performance": {
            "avg_assignment_time_seconds": 30.0,
            "avg_dayplan_time_seconds": 60.0,
            "success_rate_percent": 95.0
        }
    }
