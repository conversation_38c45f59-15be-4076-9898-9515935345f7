"""DTOs for Patient Episode of Care"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from src.application.dtos.patient_episode_of_care_order_dto import (
    PatientEpisodeOfCareOrderResponseDTO,
)


class PatientEpisodeOfCareBaseDTO(BaseModel):
    """Base DTO for Patient Episode of Care"""

    patient_id: UUID
    patient_mrn_id: UUID | None = None
    location_id: UUID | None = None
    discharge_summary: str | None = None
    clinical_diagnosis: str | None = None
    date_of_start_of_care: datetime | None = None
    insurance_name: str | None = Field(None, max_length=100)
    insurance_type: str | None = Field(None, max_length=50)
    insurance_number: str | None = Field(None, max_length=50)


class PatientEpisodeOfCareCreateDTO(PatientEpisodeOfCareBaseDTO):
    """DTO for creating Patient Episode of Care"""

    pass


class PatientEpisodeOfCareUpdateDTO(PatientEpisodeOfCareBaseDTO):
    """DTO for updating Patient Episode of Care"""

    id: UUID


class PatientEpisodeOfCareResponseDTO(PatientEpisodeOfCareBaseDTO):
    """DTO for Patient Episode of Care response"""

    id: UUID
    mod_at: datetime
    mod_by: UUID
    mod_service: str
    record_status: str
    orders: list[PatientEpisodeOfCareOrderResponseDTO] = Field(default_factory=list)

    class Config:
        """Pydantic config"""

        from_attributes = True
        populate_by_name = True
