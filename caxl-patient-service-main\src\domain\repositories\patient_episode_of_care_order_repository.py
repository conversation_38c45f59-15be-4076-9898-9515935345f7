"""Repository interface for Patient Episode of Care Order"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.domain.entities.patient_episode_of_care_order import PatientEpisodeOfCareOrder
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.repository_base import BaseRepository
from src.domain.value_objects.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)


class PatientEpisodeOfCareOrderRepository(BaseRepository[PatientEpisodeOfCareOrder], ABC):
    """Repository interface for Patient Episode of Care Order"""

    @abstractmethod
    async def add_directives(
        self,
        order_id: UUID,
        directives: list[PatientEpisodeOfCareOrderDirective],
        audit_stamp: AuditStamp,
    ) -> PatientEpisodeOfCareOrder | None:
        """Add directives to an order"""
        pass

    @abstractmethod
    async def remove_directives(
        self, order_id: UUID, directive_ids: list[UUID], audit_stamp: AuditStamp
    ) -> PatientEpisodeOfCareOrder | None:
        """Remove directives from an order"""
        pass
