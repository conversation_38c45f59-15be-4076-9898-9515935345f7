"""FastAPI routes for patient operations"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query

from src.application.dtos.patient import (
    PatientDTO,
)
from src.application.usecases.patient import PatientUseCase
from src.config.logging import logging
from src.container import Container
from src.infrastructure.api.dependencies.headers import get_tenant_id, get_user_id
from src.infrastructure.api.dependencies.session import get_user_context
from src.infrastructure.api.request_mappers.patient_mapper import (
    map_create_patient_request_to_dto,
    map_update_patient_request_to_dto,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient import (
    CreatePatientRequestSchema,
    PatientListResponseSchema,
    UpdatePatientRequestSchema,
)

logger = logging.getLogger(__name__)


async def get_patient_use_case() -> PatientUseCase:
    """Get patient use case instance"""
    container = Container()
    return container.patient_use_case()


router = APIRouter(
    prefix="/patients",
    tags=["patients"],
    dependencies=[
        Depends(get_tenant_id),
        Depends(get_user_id),
    ],
)


@router.post("", response_model=PatientDTO)
async def create_patient_route(
    request: CreatePatientRequestSchema,
    usecase: PatientUseCase = Depends(get_patient_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> PatientDTO:
    """Create a new patient"""
    try:
        patient_dto = map_create_patient_request_to_dto(request, user_context)
        return await usecase.create_patient(patient_dto, context=user_context)
    except Exception as e:
        logger.error(f"Error creating patient: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("")
async def get_patients_route(
    usecase: PatientUseCase = Depends(get_patient_use_case),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
) -> PatientListResponseSchema:
    """Get a list of patients"""
    try:
        response = await usecase.list_patients(page=page, page_size=page_size)
        return PatientListResponseSchema(
            patients=response.patients,
            page=response.page,
            page_size=response.page_size,
            total=response.total_records,
            total_pages=response.total_pages,
        )
    except Exception as e:
        logger.error(f"Error listing patients: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("/{patient_id}", response_model=PatientListResponseSchema)
async def get_patient(
    patient_id: UUID,
    patient_use_case: PatientUseCase = Depends(get_patient_use_case),
) -> PatientListResponseSchema:
    """Get patient by ID"""
    try:
        patient = await patient_use_case.get_patient(patient_id)
        if not patient:
            raise HTTPException(status_code=404, detail=f"Patient not found with ID: {patient_id}")
        return PatientListResponseSchema(
            patients=[patient], total=1, page=1, page_size=1, total_pages=1
        )
    except Exception as e:
        logger.error(f"Failed to get patient: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.put("/{patient_id}", response_model=PatientListResponseSchema)
async def update_patient(
    patient_id: UUID,
    request: UpdatePatientRequestSchema,
    patient_use_case: PatientUseCase = Depends(get_patient_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> PatientListResponseSchema:
    """Update existing patient"""
    try:
        patient_dto = map_update_patient_request_to_dto(patient_id, request, user_context)
        patient = await patient_use_case.update_patient(patient_dto, context=user_context)
        return PatientListResponseSchema(
            patients=[patient], total=1, page=1, page_size=1, total_pages=1
        )
    except Exception as e:
        logger.error(f"Failed to update patient: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.delete("/{patient_id}")
async def delete_patient(
    patient_id: UUID,
    patient_use_case: PatientUseCase = Depends(get_patient_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> dict:
    """Delete patient"""
    try:
        success = await patient_use_case.delete_patient(patient_id, context=user_context)
        if not success:
            raise HTTPException(status_code=404, detail=f"Patient not found with ID: {patient_id}")
        return {"message": "Patient deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete patient: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e
