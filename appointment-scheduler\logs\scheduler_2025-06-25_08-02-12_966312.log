2025-06-25 08:02:12 | INFO | Received dayplan request for 2024-01-15
2025-06-25 08:02:12 | INFO | Starting DayPlan job for 2024-01-15...
2025-06-25 08:02:12 | INFO | 🚀 === DAY PLAN JOB STARTED ===
2025-06-25 08:02:12 | INFO | 📅 Target Date: 2024-01-15
2025-06-25 08:02:12 | INFO | 🆔 Batch ID: dayplan_20240115_080212
2025-06-25 08:02:12 | INFO | 📝 Log file: logs\day_plan_2025-06-25.log
2025-06-25 08:02:12 | INFO | === STAGE 1: Loading Scheduled Appointments ===
2025-06-25 08:02:12 | INFO | 📋 Loaded 6 scheduled appointments for 2024-01-15
2025-06-25 08:02:12 | INFO | === STAGE 2: Loading Available Time Slots ===
2025-06-25 08:02:12 | INFO | ⏰ Loaded 20 time slots for 2024-01-15
2025-06-25 08:02:12 | INFO | === STAGE 3: Creating Time Slot Assignments ===
2025-06-25 08:02:12 | INFO | 📝 Created 6 time slot assignments
2025-06-25 08:02:12 | INFO | === STAGE 4: Solving Time Assignment Problem ===
2025-06-25 08:02:12 | INFO | Solving time assignment problem with 6 appointments
2025-06-25 08:02:12 | INFO | Route optimization constraints enabled
2025-06-25 08:02:12 | INFO | Created solver config with 60s timeout
2025-06-25 08:02:12 | INFO | Building solver...
2025-06-25 08:02:14 | INFO | Solver built successfully
2025-06-25 08:02:14 | INFO | Starting solver...
2025-06-25 08:02:15 | ERROR | Solver failed after 0.67 seconds: 
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'
Traceback (most recent call last):
  File "DefaultSolver.java", line 194, in solve
  File "AbstractSolver.java", line 79, in runPhases
  File "DefaultConstructionHeuristicPhase.java", line 74, in solve
  File "ConstructionHeuristicDecider.java", line 112, in decideNextStep
  File "ConstructionHeuristicDecider.java", line 157, in doMove
  File "AbstractScoreDirector.java", line 297, in executeTemporaryMove
  File "MoveDirector.java", line 141, in executeTemporary
  File "BavetConstraintStreamScoreDirector.java", line 69, in calculateScore
  File "BavetConstraintSession.java", line 38, in calculateScore
  File "AbstractSession.java", line 97, in settle
  File "NodeNetwork.java", line 47, in settle
  File "NodeNetwork.java", line 63, in settleLayer
  File "StaticPropagationQueue.java", line 125, in propagateInserts
  File "StaticPropagationQueue.java", line 117, in processAndClear
  File "ConditionalTupleLifecycle.java", line 18, in insert
  File "TupleLifecycle.java", line 40, in lambda$conditionally$2
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line -1, in 
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 38, in 
    _exceeds_capacity(a, b)))
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 92, in _exceeds_capacity
    return (points_a + points_b) > 20  # Simplified threshold
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'

2025-06-25 08:02:15 | ERROR | ❌ Error in DayPlan job: 
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'
Traceback (most recent call last):
  File "DefaultSolver.java", line 194, in solve
  File "AbstractSolver.java", line 79, in runPhases
  File "DefaultConstructionHeuristicPhase.java", line 74, in solve
  File "ConstructionHeuristicDecider.java", line 112, in decideNextStep
  File "ConstructionHeuristicDecider.java", line 157, in doMove
  File "AbstractScoreDirector.java", line 297, in executeTemporaryMove
  File "MoveDirector.java", line 141, in executeTemporary
  File "BavetConstraintStreamScoreDirector.java", line 69, in calculateScore
  File "BavetConstraintSession.java", line 38, in calculateScore
  File "AbstractSession.java", line 97, in settle
  File "NodeNetwork.java", line 47, in settle
  File "NodeNetwork.java", line 63, in settleLayer
  File "StaticPropagationQueue.java", line 125, in propagateInserts
  File "StaticPropagationQueue.java", line 117, in processAndClear
  File "ConditionalTupleLifecycle.java", line 18, in insert
  File "TupleLifecycle.java", line 40, in lambda$conditionally$2
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line -1, in 
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 38, in 
    _exceeds_capacity(a, b)))
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 92, in _exceeds_capacity
    return (points_a + points_b) > 20  # Simplified threshold
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'

2025-06-25 08:02:15 | ERROR | DayPlan job failed: 
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'
Traceback (most recent call last):
  File "DefaultSolver.java", line 194, in solve
  File "AbstractSolver.java", line 79, in runPhases
  File "DefaultConstructionHeuristicPhase.java", line 74, in solve
  File "ConstructionHeuristicDecider.java", line 112, in decideNextStep
  File "ConstructionHeuristicDecider.java", line 157, in doMove
  File "AbstractScoreDirector.java", line 297, in executeTemporaryMove
  File "MoveDirector.java", line 141, in executeTemporary
  File "BavetConstraintStreamScoreDirector.java", line 69, in calculateScore
  File "BavetConstraintSession.java", line 38, in calculateScore
  File "AbstractSession.java", line 97, in settle
  File "NodeNetwork.java", line 47, in settle
  File "NodeNetwork.java", line 63, in settleLayer
  File "StaticPropagationQueue.java", line 125, in propagateInserts
  File "StaticPropagationQueue.java", line 117, in processAndClear
  File "ConditionalTupleLifecycle.java", line 18, in insert
  File "TupleLifecycle.java", line 40, in lambda$conditionally$2
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line -1, in 
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 38, in 
    _exceeds_capacity(a, b)))
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 92, in _exceeds_capacity
    return (points_a + points_b) > 20  # Simplified threshold
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'

2025-06-25 08:02:15 | ERROR | Day plan failed: 
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'
Traceback (most recent call last):
  File "DefaultSolver.java", line 194, in solve
  File "AbstractSolver.java", line 79, in runPhases
  File "DefaultConstructionHeuristicPhase.java", line 74, in solve
  File "ConstructionHeuristicDecider.java", line 112, in decideNextStep
  File "ConstructionHeuristicDecider.java", line 157, in doMove
  File "AbstractScoreDirector.java", line 297, in executeTemporaryMove
  File "MoveDirector.java", line 141, in executeTemporary
  File "BavetConstraintStreamScoreDirector.java", line 69, in calculateScore
  File "BavetConstraintSession.java", line 38, in calculateScore
  File "AbstractSession.java", line 97, in settle
  File "NodeNetwork.java", line 47, in settle
  File "NodeNetwork.java", line 63, in settleLayer
  File "StaticPropagationQueue.java", line 125, in propagateInserts
  File "StaticPropagationQueue.java", line 117, in processAndClear
  File "ConditionalTupleLifecycle.java", line 18, in insert
  File "TupleLifecycle.java", line 40, in lambda$conditionally$2
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line -1, in 
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 38, in 
    _exceeds_capacity(a, b)))
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c016_schd_route_optimization.py", line 92, in _exceeds_capacity
    return (points_a + points_b) > 20  # Simplified threshold
TypeError: unsupported operand type(s) for +: 'NoneType' and 'NoneType'

