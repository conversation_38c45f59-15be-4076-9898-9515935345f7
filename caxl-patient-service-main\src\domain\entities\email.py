"""Email information entity"""

import re
from uuid import UUID, uuid4

from src.domain.exceptions.base import ValidationError
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class EmailInfo(EntityBase):
    """Email information entity"""

    def __init__(
        self,
        email: str,
        audit_stamp: AuditStamp,
        email_type: str | None = None,
        is_verified: bool = False,
        id: UUID | None = None,
    ):
        """Initialize email information"""
        super().__init__(id=id or uuid4(), audit_stamp=audit_stamp)
        self._email = email
        self._email_type = email_type
        self._is_verified = is_verified
        self._ensure_invariants()

    def _ensure_invariants(self) -> None:
        """Ensure all business rules and invariants are satisfied"""
        if not self._email:
            raise ValidationError("Email address is required")
        if not self._is_valid_email(self._email):
            raise ValidationError("Invalid email address format")

    @staticmethod
    def _is_valid_email(email: str) -> bool:
        # RFC 5322 compliant email validation regex
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return bool(re.match(pattern, email))

    @property
    def email(self) -> str:
        """Get email address"""
        return self._email

    @email.setter
    def email(self, value: str) -> None:
        """Set email address"""
        self._email = value
        self._ensure_invariants()

    @property
    def email_type(self) -> str | None:
        """Get email type"""
        return self._email_type

    @email_type.setter
    def email_type(self, value: str | None) -> None:
        """Set email type"""
        self._email_type = value

    @property
    def is_verified(self) -> bool:
        """Get verification status"""
        return self._is_verified

    @is_verified.setter
    def is_verified(self, value: bool) -> None:
        """Set verification status"""
        self._is_verified = value
