"""Entity for Patient Episode of Care"""

from datetime import datetime
from uuid import UUID

from src.domain.entities.patient_episode_of_care_order import PatientEpisodeOfCareOrder
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class PatientEpisodeOfCareEntity(EntityBase):
    """Entity for Patient Episode of Care"""

    def __init__(
        self,
        patient_id: UUID,
        audit_stamp: AuditStamp,
        patient_mrn_id: UUID | None = None,
        location_id: UUID | None = None,
        discharge_summary: str | None = None,
        clinical_diagnosis: str | None = None,
        date_of_start_of_care: datetime | None = None,
        insurance_name: str | None = None,
        insurance_type: str | None = None,
        insurance_number: str | None = None,
        orders: list[PatientEpisodeOfCareOrder] | None = None,
        id: UUID | None = None,
    ):
        """Initialize entity"""
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._patient_id = patient_id
        self._patient_mrn_id = patient_mrn_id
        self._location_id = location_id
        self._discharge_summary = discharge_summary
        self._clinical_diagnosis = clinical_diagnosis
        self._date_of_start_of_care = date_of_start_of_care
        self._insurance_name = insurance_name
        self._insurance_type = insurance_type
        self._insurance_number = insurance_number
        self._orders = orders or []

    @property
    def patient_id(self) -> UUID:
        """Get patient ID"""
        return self._patient_id

    @property
    def patient_mrn_id(self) -> UUID | None:
        """Get patient MRN ID"""
        return self._patient_mrn_id

    @property
    def location_id(self) -> UUID | None:
        """Get location ID"""
        return self._location_id

    @property
    def discharge_summary(self) -> str | None:
        """Get discharge summary"""
        return self._discharge_summary

    @property
    def clinical_diagnosis(self) -> str | None:
        """Get clinical diagnosis"""
        return self._clinical_diagnosis

    @property
    def date_of_start_of_care(self) -> datetime | None:
        """Get date of start of care"""
        return self._date_of_start_of_care

    @property
    def insurance_name(self) -> str | None:
        """Get insurance name"""
        return self._insurance_name

    @property
    def insurance_type(self) -> str | None:
        """Get insurance type"""
        return self._insurance_type

    @property
    def insurance_number(self) -> str | None:
        """Get insurance number"""
        return self._insurance_number

    @property
    def orders(self) -> list[PatientEpisodeOfCareOrder]:
        """Get orders"""
        return self._orders
