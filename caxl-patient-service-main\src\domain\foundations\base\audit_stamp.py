from __future__ import annotations

from datetime import UTC, datetime  # Added timezone for robust UTC handling
from typing import Any

from .record_status_enum import RecordStatusEnum
from .value_object_base import ValueObjectBase


# Import the centralized RecordStatusEnum


class AuditStamp(ValueObjectBase):
    """
    Represents the audit trail information for an entity,
    as per the defined domain schema. This is an immutable value object.
    """

    # tenant_id: str
    # mod_at: datetime # Should be stored as UTC
    # mod_by: str
    # mod_service: str
    # record_status: 'RecordStatusEnum'

    def __init__(
        self,
        tenant_id: str,
        mod_at: datetime,
        mod_by: str,
        mod_service: str,
        record_status: str | RecordStatusEnum,
    ):
        # Explicitly ensure _frozen is False at the very start of __init__
        # This is speculative and should ideally be redundant.
        object.__setattr__(self, "_frozen", False)

        # Store attributes with a leading underscore for property-based access
        self._tenant_id = tenant_id
        self._mod_at = mod_at
        self._mod_by = mod_by
        self._mod_service = mod_service

        if isinstance(record_status, RecordStatusEnum):
            self._record_status = record_status
        elif isinstance(record_status, str):
            self._record_status = RecordStatusEnum.from_value(record_status)
        else:
            # If it's not an enum instance or a string, it's an invalid type for initialization.
            # The RecordStatusEnum.from_value method itself also raises an error for non-strings,
            # but this provides a clearer error message at the AuditStamp level.
            raise ValueError(
                f"AuditStamp: record_status must be a RecordStatusEnum instance or a string value, "
                f"got {type(record_status)}: {record_status}"
            )

        self.validate()  # Call validation logic

        # super().__init__() # ValueObjectBase has no __init__, so this calls object.__init__()
        self._freeze()  # Ensure the object is frozen after initialization

    def validate(self) -> None:
        """Validate the AuditStamp's invariants."""
        if not self._tenant_id:
            raise ValueError("AuditStamp: Tenant ID cannot be empty.")
        if not isinstance(self._mod_at, datetime):
            raise ValueError("AuditStamp: Modification datetime must be a valid datetime object.")
        if self._mod_at.tzinfo is None:
            # Consider raising an error or defaulting to UTC, e.g., mod_at = mod_at.replace(tzinfo=timezone.utc)
            # For now, we proceed, assuming inputs will be handled correctly upstream or are naive UTC.
            pass  # Or add specific validation for timezone-awareness if required by your domain
        if not self._mod_by:
            raise ValueError("AuditStamp: Modified by identifier cannot be empty.")
        if not self._mod_service:
            raise ValueError("AuditStamp: Modifying service identifier cannot be empty.")
        if not isinstance(self._record_status, RecordStatusEnum):
            raise ValueError(
                f"AuditStamp: Record status must be a valid RecordStatusEnum instance. {self._record_status}"
            )

    @property
    def tenant_id(self) -> str:
        return self._tenant_id

    @property
    def mod_at(self) -> datetime:
        return self._mod_at

    @property
    def mod_by(self) -> str:
        return self._mod_by

    @property
    def mod_service(self) -> str:
        return self._mod_service

    @property
    def record_status(self) -> RecordStatusEnum:
        return self._record_status

    def to_dict(self) -> dict[str, Any]:
        """Convert AuditStamp to dictionary representation."""
        return {
            "tenant_id": self.tenant_id,
            "mod_at": self.mod_at.isoformat(),
            "mod_by": self.mod_by,
            "mod_service": self.mod_service,
            "record_status": self.record_status.value,
        }

    @classmethod
    def from_dict(cls: type[AuditStamp], data: dict[str, Any]) -> AuditStamp:
        """Create an AuditStamp instance from a dictionary."""
        if not data:
            raise ValueError("Data for AuditStamp.from_dict cannot be empty.")

        mod_at_str = data.get("mod_at")
        if not mod_at_str:
            raise ValueError("'mod_at' is required in AuditStamp data.")
        try:
            if mod_at_str.endswith("Z"):
                mod_at_dt = datetime.fromisoformat(mod_at_str[:-1] + "+00:00")
            else:
                mod_at_dt = datetime.fromisoformat(mod_at_str)
        except ValueError as e:
            raise ValueError(f"Invalid 'mod_at' format: {mod_at_str}. Error: {e}") from e

        status_str = data.get("record_status")
        if not status_str:
            raise ValueError("'record_status' is required in AuditStamp data.")
        try:
            record_status_enum = RecordStatusEnum(status_str)
        except ValueError as e:
            # Provide a more helpful error if the string is not a valid RecordStatusEnum member
            valid_statuses = [member.value for member in RecordStatusEnum]
            raise ValueError(
                f"Invalid 'record_status' value: '{status_str}'. Valid values are: {valid_statuses}"
            ) from e

        return cls(
            tenant_id=data.get("tenant_id", ""),  # Ensure tenant_id is present
            mod_at=mod_at_dt,
            mod_by=data.get("mod_by", ""),  # Ensure mod_by is present
            mod_service=data.get("mod_service", ""),  # Ensure mod_service is present
            record_status=record_status_enum,
        )

    def copy_with_update(
        self,
        mod_by: str,
        mod_service: str,
        record_status: RecordStatusEnum | None = None,
        mod_at: datetime | None = None,
    ) -> AuditStamp:
        """Creates a new AuditStamp instance with updated modification details."""
        return AuditStamp(
            tenant_id=self.tenant_id,
            mod_at=mod_at if mod_at else datetime.now(UTC),
            mod_by=mod_by,
            mod_service=mod_service,
            record_status=record_status if record_status else self.record_status,
        )

    def __repr__(self) -> str:
        return (
            f"AuditStamp(tenant_id='{self.tenant_id}', mod_at='{self.mod_at.isoformat()}', "
            f"mod_by='{self.mod_by}', mod_service='{self.mod_service}', "
            f"record_status='{self.record_status.value}')"
        )
