from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""

    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"  # nosec B104 - Binding to all interfaces is intentional for development
    PORT: int = 8002

    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "CAXL Patient Service"

    # Database settings
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "postgres"
    DB_ECHO: bool = False

    # Tenant settings
    TENANT_DB_PREFIX: str = "caxl_patient_service_"
    TENANT_ID: str = "default-tenant"
    TENANT_HEADER_NAME: str = "x-tenant-id"
    USER_HEADER_NAME: str = "x-user-id"
    DEFAULT_MOD_SERVICE: str = "PatientService"

    # CORS settings
    CORS_ALLOW_ORIGINS: list[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_EXPOSE_HEADERS: list[str] = ["*"]
    CORS_ALLOW_METHODS: list[str] = ["*"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]

    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True, extra="allow")

    @property
    def env_suffix(self) -> str:
        """Get the environment suffix."""
        if self.ENVIRONMENT == "development":
            return "_dev"
        elif self.ENVIRONMENT == "staging":
            return "_staging"
        elif self.ENVIRONMENT == "production":
            return "_prod"
        else:
            return ""

    @property
    def database_url(self) -> str:
        """Get the default database URL."""
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.TENANT_DB_PREFIX}{self.TENANT_ID}{self.env_suffix}"


@lru_cache
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


settings = get_settings()
