# Quick Start Guide - Appointment Scheduling System

This guide will help you get the US-based appointment scheduling system up and running quickly.

## What You'll Learn

This system demonstrates how to:
- Assign healthcare appointments to providers using constraint optimization
- Configure service-specific rules via YAML files
- Run nightly batch processing with rolling windows
- Balance multiple constraints (skills, geography, workload, continuity)
- Use Timefold for advanced constraint solving

## Problem Scenario

You manage a home healthcare agency with:
- Multiple service types (nursing, therapy, social work)
- Providers with different skills and service areas
- Patients with specific needs and preferences
- Geographic and scheduling constraints
- Need for continuity of care

## Quick Setup

### 1. Install Dependencies

```bash
cd appointment-scheduler
pip install timefold==1.23.0b0 pyyaml pydantic geopy schedule loguru
```

### 2. Run the Example

```bash
# Run the interactive example
python example.py

# Or run the job directly
python -m appointment_scheduler.jobs.assign_appointments

# Or run the scheduler
python -m appointment_scheduler.scheduler --mode once --job-type nightly
```

## Key Configuration Concepts

### Service-Specific Rules

Each service type has its own configuration file:

```yaml
# config/skilled_nursing.yml
service_type: skilled_nursing
required_skills:
  - "Registered Nurse (RN)"
  - "Skilled Nursing Care"
  - "Wound Care"

geographic_radius_miles: 30.0
max_daily_appointments_per_provider: 6

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.9
workload_balance_weight: 0.7
geographic_clustering_weight: 0.6
patient_preference_weight: 0.8
```

### Constraint Types

**Hard Constraints (Must Satisfy):**
- Provider skills match patient needs
- Geographic service area limits
- Provider capacity limits
- No double-booking
- Timed visit preservation

**Soft Constraints (Preferences):**
- Workload balancing across providers
- Geographic clustering for efficiency
- Continuity of care relationships
- Patient preferences

## Example Output

```
🏥 US-Based Appointment Scheduling System
==================================================
Initializing appointment assignment job...
Loading demo data...
Loaded 8 patients
Loaded 5 providers
Loaded 280 time slots

Running appointment assignment...

==================================================
ASSIGNMENT RESULTS
==================================================
Batch ID: demo_batch_001
Total Appointments: 8
Successfully Assigned: 7
Unassigned: 1
Success Rate: 87.5%
Average Score: 85.6
Processing Time: 12.34s

Assignment Details:
------------------------------
 1. ✅ Patient patient_1 -> Provider provider_1
 2. ✅ Patient patient_2 -> Provider provider_2
 3. ✅ Patient patient_3 -> Provider provider_1
 4. ✅ Patient patient_4 -> Provider provider_3
 5. ✅ Patient patient_5 -> Provider provider_2
 6. ✅ Patient patient_6 -> Provider provider_5
 7. ✅ Patient patient_7 -> Provider provider_4
 8. ❌ Patient patient_8 -> Provider None

Constraint Analysis:
------------------------------
Satisfied Constraints:
  ✅ assigned: 7
  ✅ provider_skills: 7
  ✅ geographic_distance: 7
  ✅ timed_visit: 7
Violated Constraints:
  ❌ unassigned: 1
```

## Running in Production

### 1. Database Integration

Replace demo data with database queries:

```python
def _load_pending_patients(self) -> List[Patient]:
    """Load patients with pending appointments from database."""
    # Replace with actual database query
    query = """
    SELECT * FROM patients 
    WHERE status = 'pending' 
    AND appointment_date BETWEEN ? AND ?
    """
    # Execute query and convert to Patient objects
    return patients
```

### 2. Scheduled Execution

```bash
# Run as a daemon (continuous scheduling)
python -m appointment_scheduler.scheduler --mode daemon --schedule-time 02:00

# Or use systemd/cron for production
```

### 3. Configuration Management

```bash
# Use environment variables for sensitive settings
export APPOINTMENT_CONFIG_FOLDER=/etc/appointment/config
export APPOINTMENT_LOG_LEVEL=INFO
export APPOINTMENT_DB_CONNECTION=postgresql://...
```

## Customization Examples

### Adding a New Service Type

1. **Create configuration file:**
```yaml
# config/respiratory_therapy.yml
service_type: respiratory_therapy
required_skills:
  - "Respiratory Therapist"
  - "Ventilator Management"
geographic_radius_miles: 20.0
max_daily_appointments_per_provider: 4
```

2. **Add to domain model:**
```python
class ServiceType(str, Enum):
    RESPIRATORY_THERAPY = "respiratory_therapy"
```

### Modifying Constraints

```python
# In constraints.py
def custom_constraint(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: custom_condition(assignment))
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment: penalty_score(assignment))
            .as_constraint("CustomConstraint"))
```

### Geographic Clustering

```python
# Optimize for route efficiency
def geographic_clustering(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each_unique_pair(AppointmentAssignment,
                                 Joiners.equal(lambda assignment: assignment.provider),
                                 Joiners.equal(lambda assignment: assignment.time_slot.date))
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment1, assignment2: 
                   int(100 - calculate_distance(assignment1.patient.location, 
                                               assignment2.patient.location)))
            .as_constraint("GeographicClustering"))
```

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure all dependencies are installed
2. **Configuration not found**: Check that YAML files exist in config folder
3. **No assignments**: Verify provider skills match patient requirements
4. **Performance issues**: Adjust max_solving_time_seconds in configuration

### Debug Mode

```bash
# Run with debug logging
export APPOINTMENT_LOG_LEVEL=DEBUG
python -m appointment_scheduler.jobs.assign_appointments
```

### Validation

```python
# Validate configurations
from appointment_scheduler.config_manager import ConfigManager

config_manager = ConfigManager("config")
issues = config_manager.validate_configs()
if issues:
    print("Configuration issues:", issues)
```

## Next Steps

1. **Integrate with your database**: Replace demo data with real queries
2. **Customize constraints**: Add business-specific rules
3. **Add monitoring**: Implement health checks and metrics
4. **Scale horizontally**: Consider multiple instances for large datasets
5. **Add web interface**: Create API endpoints for manual assignments

This system provides a solid foundation for healthcare appointment scheduling with advanced constraint optimization! 