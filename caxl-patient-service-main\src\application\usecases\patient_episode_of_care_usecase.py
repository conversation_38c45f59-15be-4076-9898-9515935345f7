"""Patient Episode of Care use case implementation"""

from datetime import datetime
from uuid import UUID

from src.application.dtos.patient_episode_of_care_dto import (
    PatientEpisodeOfCareCreateDTO,
    PatientEpisodeOfCareResponseDTO,
    PatientEpisodeOfCareUpdateDTO,
)
from src.application.mappers.patient_episode_of_care_mapper import PatientEpisodeOfCareMapper
from src.application.ports.input.patient_episode_of_care_use_case_port import (
    PatientEpisodeOfCareUseCasePort,
)
from src.config.logging import logging
from src.config.settings import settings
from src.domain.exceptions.base import ValidationError
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.repositories.patient_episode_of_care_repository import (
    PatientEpisodeOfCareRepository,
)
from src.domain.repositories.patient_repository import PatientRepository
from src.infrastructure.api.schemas.auth_schemas import AuthSession

logger = logging.getLogger(__name__)


class PatientEpisodeOfCareUseCase(PatientEpisodeOfCareUseCasePort):
    """Patient Episode of Care use case implementation"""

    def __init__(
        self,
        patient_episode_repository: PatientEpisodeOfCareRepository,
        patient_repository: PatientRepository,
        patient_episode_mapper: PatientEpisodeOfCareMapper,
    ):
        """Initialize use case with dependencies"""
        self._repository = patient_episode_repository
        self._patient_repository = patient_repository
        self._mapper = patient_episode_mapper

    async def get_by_id(self, episode_id: UUID) -> PatientEpisodeOfCareResponseDTO | None:
        """Get episode of care by ID"""
        episode = await self._repository.find_by_id(episode_id)
        return self._mapper.to_dto(episode) if episode else None

    async def get_all(
        self, page: int = 1, page_size: int = 10
    ) -> list[PatientEpisodeOfCareResponseDTO]:
        """Get all episodes of care with pagination"""
        episodes = await self._repository.find_all(page, page_size)
        return [self._mapper.to_dto(episode) for episode in episodes]

    async def create(
        self, dto: PatientEpisodeOfCareCreateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareResponseDTO:
        """Create new episode of care"""
        try:
            # Validate required fields
            if not dto.patient_id:
                raise ValidationError("Patient ID is required")

            # Check if patient exists
            patient = await self._patient_repository.find_by_id(dto.patient_id)
            if not patient:
                raise ValidationError(f"Patient not found with ID: {dto.patient_id}")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Convert DTO to domain entity
            episode = self._mapper.to_domain(dto, audit_stamp)

            # Save episode
            saved_episode = await self._repository.save(episode, audit_stamp)
            return self._mapper.to_dto(saved_episode)
        except Exception as e:
            logger.error(f"Failed to create episode of care: {e!s}")
            raise ValidationError(f"Failed to create episode of care: {e!s}") from e

    async def update(
        self, dto: PatientEpisodeOfCareUpdateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareResponseDTO:
        """Update existing episode of care"""
        try:
            # Get existing episode
            existing_episode = await self._repository.find_by_id(dto.id)
            if not existing_episode:
                raise ValidationError(f"Episode of care not found with ID: {dto.id}")

            # Check if patient exists if patient_id is being updated
            if dto.patient_id and dto.patient_id != existing_episode.patient_id:
                patient = await self._patient_repository.find_by_id(dto.patient_id)
                if not patient:
                    raise ValidationError(f"Patient not found with ID: {dto.patient_id}")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Convert DTO to domain entity
            episode = self._mapper.to_domain(dto, audit_stamp)

            # Update episode
            updated_episode = await self._repository.update(episode, audit_stamp)
            return self._mapper.to_dto(updated_episode)
        except Exception as e:
            logger.error(f"Failed to update episode of care: {e!s}")
            raise ValidationError(f"Failed to update episode of care: {e!s}") from e

    async def delete(self, episode_id: UUID, context: AuthSession) -> bool:
        """Delete episode of care"""
        try:
            # Get existing episode
            existing_episode = await self._repository.find_by_id(episode_id)
            if not existing_episode:
                raise ValidationError(f"Episode of care not found with ID: {episode_id}")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.DELETED,
            )

            # Delete episode
            return await self._repository.delete(episode_id, audit_stamp)
        except Exception as e:
            logger.error(f"Failed to delete episode of care: {e!s}")
            raise ValidationError(f"Failed to delete episode of care: {e!s}") from e

    async def count(self) -> int:
        """Get total count of episodes of care"""
        try:
            return await self._repository.count()
        except Exception as e:
            logger.error(f"Failed to count episodes of care: {e!s}")
            raise ValidationError(f"Failed to count episodes of care: {e!s}") from e
