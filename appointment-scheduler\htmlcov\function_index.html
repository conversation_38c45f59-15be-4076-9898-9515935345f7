<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f___init___py.html">src\appointment_scheduler\__init__.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4___init___py.html">src\appointment_scheduler\api\__init__.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t14">src\appointment_scheduler\api\app.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t14"><data value='create_app'>create_app</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t37">src\appointment_scheduler\api\app.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t37"><data value='root'>create_app.root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t46">src\appointment_scheduler\api\app.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t46"><data value='health_check'>create_app.health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t53">src\appointment_scheduler\api\app.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html#t53"><data value='main'>main</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html">src\appointment_scheduler\api\app.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_models_py.html">src\appointment_scheduler\api\models.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t23">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t23"><data value='get_data_loader'>get_data_loader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t28">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t28"><data value='get_scheduler'>get_scheduler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t34">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t34"><data value='get_patients'>get_patients</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t85">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t85"><data value='get_carestaff'>get_carestaff</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t145">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t145"><data value='assign_appointments'>assign_appointments</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t182">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t182"><data value='reassign_appointments'>reassign_appointments</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t218">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html#t218"><data value='run_dayplan'>run_dayplan</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html">src\appointment_scheduler\api\routes.py</a></td>
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t24">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t24"><data value='init__'>ConfigManager.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t37">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t37"><data value='load_scheduler_config'>ConfigManager._load_scheduler_config</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t61">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t61"><data value='create_default_scheduler_config'>ConfigManager._create_default_scheduler_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t66">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t66"><data value='load_service_configs'>ConfigManager._load_service_configs</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t102">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t102"><data value='create_default_service_configs'>ConfigManager._create_default_service_configs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t107">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t107"><data value='get_scheduler_config'>ConfigManager.get_scheduler_config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t115">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t115"><data value='get_service_config'>ConfigManager.get_service_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t119">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t119"><data value='get_all_service_configs'>ConfigManager.get_all_service_configs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t123">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t123"><data value='get_service_types'>ConfigManager.get_service_types</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t127">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t127"><data value='validate_configs'>ConfigManager.validate_configs</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t156">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t156"><data value='validate_configuration'>ConfigManager.validate_configuration</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t223">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t223"><data value='reload_configuration'>ConfigManager.reload_configuration</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t241">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html#t241"><data value='get_config_summary'>ConfigManager.get_config_summary</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html">src\appointment_scheduler\config_manager.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd___init___py.html">src\appointment_scheduler\constraints\__init__.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_assignment_constraints_py.html#t26">src\appointment_scheduler\constraints\assignment_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_assignment_constraints_py.html#t26"><data value='define_constraints'>define_constraints</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_assignment_constraints_py.html">src\appointment_scheduler\constraints\assignment_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_assignment_constraints_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t19">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t19"><data value='provider_serves_location'>_provider_serves_location</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t26">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t26"><data value='provider_available_on_date'>_provider_available_on_date</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t33">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t33"><data value='calculate_distance'>_calculate_distance</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t54">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t54"><data value='is_within_service_radius'>_is_within_service_radius</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t63">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t63"><data value='has_required_skills'>_has_required_skills</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t70">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t70"><data value='is_working_day'>_is_working_day</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t83">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t83"><data value='get_provider_workload_for_date'>_get_provider_workload_for_date</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t93">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t93"><data value='get_traffic_config'>_get_traffic_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t104">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t104"><data value='is_urban_area'>_is_urban_area</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t119">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t119"><data value='get_speed_factor'>_get_speed_factor</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t169">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t169"><data value='get_google_maps_route_time'>_get_google_maps_route_time</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t239">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t239"><data value='get_google_weather_data'>_get_google_weather_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t278">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t278"><data value='get_weather_adjustment'>_get_weather_adjustment</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t366">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t366"><data value='calculate_travel_time_between_appointments'>_calculate_travel_time_between_appointments</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t402">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t402"><data value='is_sufficient_travel_time'>_is_sufficient_travel_time</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t409">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t409"><data value='get_appointment_duration_minutes'>_get_appointment_duration_minutes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t414">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t414"><data value='is_urgent_appointment'>_is_urgent_appointment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t419">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t419"><data value='get_service_type_from_skills'>_get_service_type_from_skills</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t450">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t450"><data value='calculate_provider_utilization'>_calculate_provider_utilization</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t463">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t463"><data value='is_provider_overbooked'>_is_provider_overbooked</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t469">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t469"><data value='get_consecutive_appointments_count'>_get_consecutive_appointments_count</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t490">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t490"><data value='validate_appointment_constraints'>_validate_appointment_constraints</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t502">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html#t502"><data value='calculate_distance_km'>_calculate_distance_km</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t20">src\appointment_scheduler\constraints\c001_asgn_provider_skill_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t20"><data value='required_skills'>required_skills</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t30">src\appointment_scheduler\constraints\c001_asgn_provider_skill_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t30"><data value='has_required_skills_enhanced'>_has_required_skills_enhanced</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t77">src\appointment_scheduler\constraints\c001_asgn_provider_skill_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html#t77"><data value='calculate_skill_penalty'>_calculate_skill_penalty</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html">src\appointment_scheduler\constraints\c001_asgn_provider_skill_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t20">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t20"><data value='provider_availability'>provider_availability</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t31">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t31"><data value='is_provider_available_enhanced'>_is_provider_available_enhanced</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t71">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t71"><data value='is_working_day_enhanced'>_is_working_day_enhanced</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t84">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t84"><data value='is_blackout_period'>_is_blackout_period</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t103">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t103"><data value='is_holiday'>_is_holiday</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t116">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t116"><data value='has_weekend_coverage'>_has_weekend_coverage</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t129">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html#t129"><data value='calculate_availability_penalty'>_calculate_availability_penalty</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t22">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t22"><data value='geographic_service_area'>geographic_service_area</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t34">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t34"><data value='is_within_geofence'>_is_within_geofence</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t56">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t56"><data value='has_polygon_geofence'>_has_polygon_geofence</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t63">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t63"><data value='get_provider_geofence_polygon'>_get_provider_geofence_polygon</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t82">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t82"><data value='is_point_in_polygon'>_is_point_in_polygon</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t115">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t115"><data value='is_within_service_radius_enhanced'>_is_within_service_radius_enhanced</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t138">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t138"><data value='calculate_distance_enhanced'>_calculate_distance_enhanced</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t168">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t168"><data value='are_valid_coordinates'>_are_valid_coordinates</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t184">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t184"><data value='get_service_radius'>_get_service_radius</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t224">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t224"><data value='is_rural_area'>_is_rural_area</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t236">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t236"><data value='is_urban_area'>_is_urban_area</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t248">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t248"><data value='is_cross_state_boundary'>_is_cross_state_boundary</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t258">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t258"><data value='calculate_geographic_penalty'>_calculate_geographic_penalty</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t302">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t302"><data value='distance_to_polygon_edge'>_distance_to_polygon_edge</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t326">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t326"><data value='distance_to_line_segment'>_distance_to_line_segment</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t357">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html#t357"><data value='calculate_polygon_penalty'>_calculate_polygon_penalty</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t21">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t21"><data value='time_availability'>time_availability</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t33">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t33"><data value='is_provider_available_on_date_enhanced'>_is_provider_available_on_date_enhanced</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t66">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t66"><data value='is_working_day_enhanced'>_is_working_day_enhanced</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t74">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t74"><data value='is_blackout_period'>_is_blackout_period</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t93">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t93"><data value='is_holiday'>_is_holiday</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t106">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t106"><data value='has_weekend_coverage'>_has_weekend_coverage</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t118">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html#t118"><data value='calculate_availability_penalty'>_calculate_availability_penalty</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html#t11">src\appointment_scheduler\constraints\c004_asgn_timed_visit_date_assignment.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html#t11"><data value='provider_role_match'>provider_role_match</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html">src\appointment_scheduler\constraints\c004_asgn_timed_visit_date_assignment.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html#t13">src\appointment_scheduler\constraints\c005_asgn_workload_balance_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html#t13"><data value='workload_balancing'>workload_balancing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html">src\appointment_scheduler\constraints\c005_asgn_workload_balance_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html#t11">src\appointment_scheduler\constraints\c006_asgn_geographic_clustering_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html#t11"><data value='capacity_thresholds'>capacity_thresholds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html">src\appointment_scheduler\constraints\c006_asgn_geographic_clustering_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t19">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t19"><data value='patient_preference_matching'>patient_preference_matching</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t29">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t29"><data value='calculate_preference_penalty'>_calculate_preference_penalty</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t54">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t54"><data value='check_timing_preferences'>_check_timing_preferences</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t83">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t83"><data value='check_priority_preferences'>_check_priority_preferences</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t106">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t106"><data value='check_location_preferences'>_check_location_preferences</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t120">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html#t120"><data value='check_custom_preferences'>_check_custom_preferences</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t19">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t19"><data value='provider_capacity_management'>provider_capacity_management</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t29">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t29"><data value='calculate_capacity_penalty'>_calculate_capacity_penalty</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t65">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t65"><data value='exceeds_daily_task_count'>_exceeds_daily_task_count</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t71">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t71"><data value='exceeds_daily_task_points'>_exceeds_daily_task_points</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t78">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t78"><data value='exceeds_daily_hours'>_exceeds_daily_hours</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t85">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t85"><data value='exceeds_weekly_hours'>_exceeds_weekly_hours</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t98">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t98"><data value='exceeds_emergency_capacity'>_exceeds_emergency_capacity</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t107">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html#t107"><data value='calculate_specialization_penalty'>_calculate_specialization_penalty</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html#t11">src\appointment_scheduler\constraints\c009_asgn_continuity_of_care_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html#t11"><data value='continuity_of_care'>continuity_of_care</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html">src\appointment_scheduler\constraints\c009_asgn_continuity_of_care_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html#t12">src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html#t12"><data value='time_slot_availability'>time_slot_availability</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html#t22">src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html#t22"><data value='is_valid_time_slot'>_is_valid_time_slot</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html">src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html#t11">src\appointment_scheduler\constraints\c011_schd_appointment_overlap_prevention.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html#t11"><data value='no_double_booking'>no_double_booking</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html">src\appointment_scheduler\constraints\c011_schd_appointment_overlap_prevention.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t14">src\appointment_scheduler\constraints\c012_schd_flexible_appointment_timing_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t14"><data value='appointment_duration_fit'>appointment_duration_fit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t24">src\appointment_scheduler\constraints\c012_schd_flexible_appointment_timing_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t24"><data value='preferred_hours'>preferred_hours</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t35">src\appointment_scheduler\constraints\c012_schd_flexible_appointment_timing_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html#t35"><data value='is_within_preferred_hours'>_is_within_preferred_hours</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html">src\appointment_scheduler\constraints\c012_schd_flexible_appointment_timing_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t22">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t22"><data value='healthcare_task_sequencing'>healthcare_task_sequencing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t38">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t38"><data value='calculate_sequencing_penalty'>_calculate_sequencing_penalty</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t74">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t74"><data value='check_procedure_dependencies'>_check_procedure_dependencies</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t115">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t115"><data value='check_patient_preparation'>_check_patient_preparation</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t153">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t153"><data value='check_medical_protocols'>_check_medical_protocols</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t198">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t198"><data value='check_complexity_sequencing'>_check_complexity_sequencing</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t232">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html#t232"><data value='check_time_preferences'>_check_time_preferences</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html#t13">src\appointment_scheduler\constraints\c014_schd_route_travel_time_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html#t13"><data value='travel_time_consideration'>travel_time_consideration</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html#t27">src\appointment_scheduler\constraints\c014_schd_route_travel_time_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html#t27"><data value='insufficient_travel_time'>_insufficient_travel_time</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html">src\appointment_scheduler\constraints\c014_schd_route_travel_time_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html#t11">src\appointment_scheduler\constraints\c015_schd_timed_appointment_pinning.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html#t11"><data value='provider_break_time'>provider_break_time</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html#t25">src\appointment_scheduler\constraints\c015_schd_timed_appointment_pinning.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html#t25"><data value='insufficient_break_time'>_insufficient_break_time</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html">src\appointment_scheduler\constraints\c015_schd_timed_appointment_pinning.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t19">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t19"><data value='route_optimization_constraints'>route_optimization_constraints</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t29">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t29"><data value='provider_capacity'>provider_capacity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t43">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t43"><data value='break_time_violation'>break_time_violation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t58">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t58"><data value='minimize_travel_time'>minimize_travel_time</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t71">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t71"><data value='geographic_clustering'>geographic_clustering</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t86">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t86"><data value='exceeds_capacity'>_exceeds_capacity</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t94">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t94"><data value='calculate_capacity_penalty'>_calculate_capacity_penalty</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t100">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t100"><data value='has_insufficient_break_time'>_has_insufficient_break_time</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t118">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t118"><data value='calculate_break_violation_minutes'>_calculate_break_violation_minutes</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t134">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t134"><data value='travel_time_penalty'>_travel_time_penalty</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t154">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t154"><data value='has_clustering_penalty'>_has_clustering_penalty</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t161">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html#t161"><data value='calculate_clustering_penalty_for_pair'>_calculate_clustering_penalty_for_pair</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_day_constraints_py.html#t24">src\appointment_scheduler\constraints\day_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_day_constraints_py.html#t24"><data value='define_day_constraints'>define_day_constraints</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_day_constraints_py.html">src\appointment_scheduler\constraints\day_constraints.py</a></td>
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_day_constraints_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t29">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t29"><data value='init__'>DataLoader.__init__</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t34">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t34"><data value='load_providers'>DataLoader.load_providers</data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t56">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t56"><data value='load_consumers'>DataLoader.load_consumers</data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t78">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t78"><data value='load_appointments'>DataLoader.load_appointments</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t101">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t101"><data value='load_all_data'>DataLoader.load_all_data</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t122">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t122"><data value='create_provider_from_data'>DataLoader._create_provider_from_data</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t184">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t184"><data value='create_consumer_from_data'>DataLoader._create_consumer_from_data</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t214">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t214"><data value='create_appointment_from_data'>DataLoader._create_appointment_from_data</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t265">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t265"><data value='parse_time'>DataLoader._parse_time</data></a></td>
                <td>11</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="5 11">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t282">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t282"><data value='parse_time_range'>DataLoader._parse_time_range</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t291">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t291"><data value='parse_date'>DataLoader._parse_date</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t302">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t302"><data value='create_demo_data'>create_demo_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t308">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t308"><data value='create_demo_providers'>create_demo_providers</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t314">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t314"><data value='create_demo_consumers'>create_demo_consumers</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t320">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t320"><data value='create_demo_appointments'>create_demo_appointments</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t330">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t330"><data value='create_demo_scheduled_appointments'>create_demo_scheduled_appointments</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t366">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t366"><data value='parse_uuid'>parse_uuid</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t373">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t373"><data value='parse_date'>parse_date</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t378">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t378"><data value='parse_time'>parse_time</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t385">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t385"><data value='parse_weekdays'>parse_weekdays</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t388">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t388"><data value='parse_location'>parse_location</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t393">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t393"><data value='parse_provider_availability'>parse_provider_availability</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t406">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t406"><data value='parse_provider_capacity'>parse_provider_capacity</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t411">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t411"><data value='parse_provider_preferences'>parse_provider_preferences</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t416">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t416"><data value='parse_consumer_preferences'>parse_consumer_preferences</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t430">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t430"><data value='parse_timing'>parse_timing</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t437">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t437"><data value='parse_relationships'>parse_relationships</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t442">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t442"><data value='parse_pinning'>parse_pinning</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t447">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t447"><data value='load_providers'>load_providers</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t474">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t474"><data value='load_consumers'>load_consumers</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t490">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html#t490"><data value='load_appointments'>load_appointments</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html">src\appointment_scheduler\data_loader.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t33">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t33"><data value='weekday_from_int'>weekday_from_int</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t83">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t83"><data value='is_full_day_off'>DateSpecificProviderAvailability.is_full_day_off</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t88">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t88"><data value='is_partial_day'>DateSpecificProviderAvailability.is_partial_day</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t104">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t104"><data value='duration_hours'>ShiftPattern.duration_hours</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t174">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t174"><data value='get_shift_hours'>ProviderAvailability.get_shift_hours</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t201">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t201"><data value='get_default_shift_hours'>ProviderAvailability._get_default_shift_hours</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t227">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t227"><data value='is_available_at_time'>ProviderAvailability.is_available_at_time</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t257">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t257"><data value='shift_crosses_midnight'>ProviderAvailability._shift_crosses_midnight</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t261">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t261"><data value='is_time_within_split_shifts'>ProviderAvailability.is_time_within_split_shifts</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t276">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t276"><data value='get_all_available_shifts_for_day'>ProviderAvailability.get_all_available_shifts_for_day</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t431">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t431"><data value='pin_assignment'>AppointmentPinning.pin_assignment</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t439">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t439"><data value='pin_provider_assignment'>AppointmentPinning.pin_provider_assignment</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t444">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t444"><data value='pin_date_assignment'>AppointmentPinning.pin_date_assignment</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t449">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t449"><data value='pin_time_assignment'>AppointmentPinning.pin_time_assignment</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t454">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t454"><data value='unpin_assignment'>AppointmentPinning.unpin_assignment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t458">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t458"><data value='unpin_all'>AppointmentPinning.unpin_all</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t466">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t466"><data value='is_any_pinned'>AppointmentPinning.is_any_pinned</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t470">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t470"><data value='get_pinning_status'>AppointmentPinning.get_pinning_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t498">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t498"><data value='is_flexible'>AppointmentTiming.is_flexible</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t502">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t502"><data value='get_time_window'>AppointmentTiming.get_time_window</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t525">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t525"><data value='has_dependencies'>AppointmentRelationships.has_dependencies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t529">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t529"><data value='has_relationships'>AppointmentRelationships.has_relationships</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t535">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t535"><data value='is_part_of_sequence'>AppointmentRelationships.is_part_of_sequence</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t561">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t561"><data value='is_flexible_timing'>AppointmentData.is_flexible_timing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t565">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t565"><data value='get_time_window'>AppointmentData.get_time_window</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t569">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t569"><data value='has_dependencies'>AppointmentData.has_dependencies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t573">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t573"><data value='has_relationships'>AppointmentData.has_relationships</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t577">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t577"><data value='is_any_pinned'>AppointmentData.is_any_pinned</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t581">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t581"><data value='get_pinning_status'>AppointmentData.get_pinning_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t595">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t595"><data value='str__'>AppointmentAssignment.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t610">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t610"><data value='str__'>ScheduledAppointment.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t622">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t622"><data value='str__'>TimeSlotAssignment.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t637">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t637"><data value='get_provider_assignments'>AppointmentSchedule.get_provider_assignments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t642">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html#t642"><data value='get_provider_daily_workload'>AppointmentSchedule.get_provider_daily_workload</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html">src\appointment_scheduler\domain.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>283</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="283 283">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7___init___py.html">src\appointment_scheduler\jobs\__init__.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t52">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t52"><data value='init__'>AssignAppointmentJob.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t77">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t77"><data value='run'>AssignAppointmentJob.run</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t174">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t174"><data value='filter_appointments_by_service'>AssignAppointmentJob._filter_appointments_by_service</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t189">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t189"><data value='filter_appointments_by_date_range'>AssignAppointmentJob._filter_appointments_by_date_range</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t201">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t201"><data value='create_domain_assignments'>AssignAppointmentJob._create_domain_assignments</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t215">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t215"><data value='create_planning_assignments'>AssignAppointmentJob._create_planning_assignments</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t229">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t229"><data value='create_available_dates'>AssignAppointmentJob._create_available_dates</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t243">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t243"><data value='solve_assignment_problem'>AssignAppointmentJob._solve_assignment_problem</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t303">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t303"><data value='process_assignment_results'>AssignAppointmentJob._process_assignment_results</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t419">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t419"><data value='determine_service_type'>AssignAppointmentJob._determine_service_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t435">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t435"><data value='check_constraint_satisfaction'>AssignAppointmentJob._check_constraint_satisfaction</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t483">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t483"><data value='count_constraint_violations'>AssignAppointmentJob._count_constraint_violations</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t490">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t490"><data value='handle_completion'>AssignAppointmentJob._handle_completion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t500">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t500"><data value='setup_logging'>setup_logging</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t522">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html#t522"><data value='main'>main</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="36 39">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t38">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t38"><data value='init__'>DayPlanJob.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t44">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t44"><data value='setup_logging'>DayPlanJob._setup_logging</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t62">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t62"><data value='run'>DayPlanJob.run</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t164">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t164"><data value='setup_route_optimization'>DayPlanJob._setup_route_optimization</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t169">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t169"><data value='load_scheduled_appointments'>DayPlanJob._load_scheduled_appointments</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t175">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t175"><data value='load_available_time_slots'>DayPlanJob._load_available_time_slots</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t184">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t184"><data value='create_time_assignments'>DayPlanJob._create_time_assignments</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t196">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t196"><data value='solve_time_assignment_problem'>DayPlanJob._solve_time_assignment_problem</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t236">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t236"><data value='process_time_assignment_results'>DayPlanJob._process_time_assignment_results</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t263">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t263"><data value='get_satisfied_constraints'>DayPlanJob._get_satisfied_constraints</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t280">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t280"><data value='get_violated_constraints'>DayPlanJob._get_violated_constraints</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t297">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t297"><data value='is_within_preferred_hours'>DayPlanJob._is_within_preferred_hours</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t322">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t322"><data value='is_double_booked'>DayPlanJob._is_double_booked</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t343">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t343"><data value='create_empty_result'>DayPlanJob._create_empty_result</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t355">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t355"><data value='display_visit_order'>DayPlanJob._display_visit_order</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t447">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t447"><data value='handle_completion'>DayPlanJob._handle_completion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t457">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html#t457"><data value='main'>main</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="31 32">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t13">src\appointment_scheduler\logging_config.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t13"><data value='setup_logging'>setup_logging</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t47">src\appointment_scheduler\logging_config.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t47"><data value='configure_loguru_from_yaml'>_configure_loguru_from_yaml</data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t116">src\appointment_scheduler\logging_config.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t116"><data value='setup_basic_logging'>_setup_basic_logging</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t132">src\appointment_scheduler\logging_config.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html#t132"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html">src\appointment_scheduler\logging_config.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t36">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t36"><data value='str__'>AppointmentAssignment.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t50">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t50"><data value='str__'>TimeSlotAssignment.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t65">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t65"><data value='str__'>ScheduledAppointment.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t86">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t86"><data value='get_provider_assignments'>AppointmentSchedule.get_provider_assignments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t91">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t91"><data value='get_provider_daily_workload'>AppointmentSchedule.get_provider_daily_workload</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t114">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t114"><data value='create_planning_assignment'>create_planning_assignment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t124">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t124"><data value='create_planning_schedule'>create_planning_schedule</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t140">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t140"><data value='create_domain_assignment'>create_domain_assignment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t150">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html#t150"><data value='create_domain_schedule'>create_domain_schedule</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html">src\appointment_scheduler\planning_models.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t28">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t28"><data value='init__'>AppointmentScheduler.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t37">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t37"><data value='setup_logging'>AppointmentScheduler._setup_logging</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t53">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t53"><data value='run_assign_appointments'>AppointmentScheduler.run_assign_appointments</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t64">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t64"><data value='run_day_plan'>AppointmentScheduler.run_day_plan</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t75">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t75"><data value='run_today_day_plan'>AppointmentScheduler.run_today_day_plan</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t79">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t79"><data value='run_tomorrow_day_plan'>AppointmentScheduler.run_tomorrow_day_plan</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t83">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t83"><data value='setup_schedule'>AppointmentScheduler.setup_schedule</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t95">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t95"><data value='run_daemon'>AppointmentScheduler.run_daemon</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t115">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t115"><data value='run_once'>AppointmentScheduler.run_once</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t130">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t130"><data value='stop'>AppointmentScheduler.stop</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t136">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html#t136"><data value='main'>main</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html">src\appointment_scheduler\scheduler.py</a></td>
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="25 26">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b___init___py.html">src\appointment_scheduler\utils\__init__.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t19">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t19"><data value='init__'>ScenarioValidator.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t24">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t24"><data value='validate_scenario'>ScenarioValidator.validate_scenario</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t48">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t48"><data value='validate_file_structure'>ScenarioValidator._validate_file_structure</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t70">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t70"><data value='validate_yaml_file'>ScenarioValidator._validate_yaml_file</data></a></td>
                <td>14</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="6 14">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t91">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t91"><data value='validate_providers_file'>ScenarioValidator._validate_providers_file</data></a></td>
                <td>16</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="9 16">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t115">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t115"><data value='validate_provider'>ScenarioValidator._validate_provider</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t139">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t139"><data value='validate_consumers_file'>ScenarioValidator._validate_consumers_file</data></a></td>
                <td>16</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="9 16">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t163">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t163"><data value='validate_consumer'>ScenarioValidator._validate_consumer</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t183">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t183"><data value='validate_appointments_file'>ScenarioValidator._validate_appointments_file</data></a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t206">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t206"><data value='validate_appointment'>ScenarioValidator._validate_appointment</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t224">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t224"><data value='validate_location'>ScenarioValidator._validate_location</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t239">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t239"><data value='validate_relationships'>ScenarioValidator._validate_relationships</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t262">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html#t262"><data value='validate_all_scenarios'>ScenarioValidator.validate_all_scenarios</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t20">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t20"><data value='create_temp_scenario'>TestHelpers.create_temp_scenario</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t42">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t42"><data value='cleanup_temp_scenario'>TestHelpers.cleanup_temp_scenario</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t48">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t48"><data value='create_minimal_provider_data'>TestHelpers.create_minimal_provider_data</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t72">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t72"><data value='create_minimal_consumer_data'>TestHelpers.create_minimal_consumer_data</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t87">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t87"><data value='create_minimal_appointment_data'>TestHelpers.create_minimal_appointment_data</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t103">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t103"><data value='create_minimal_scenario'>TestHelpers.create_minimal_scenario</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t112">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t112"><data value='create_edge_case_scenario'>TestHelpers.create_edge_case_scenario</data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t153">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t153"><data value='validate_assignment_result'>TestHelpers.validate_assignment_result</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t172">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t172"><data value='validate_dayplan_result'>TestHelpers.validate_dayplan_result</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t188">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html#t188"><data value='create_scenario_with_coverage'>TestHelpers.create_scenario_with_coverage</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3097</td>
                <td>2062</td>
                <td>0</td>
                <td class="right" data-ratio="1035 3097">33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
