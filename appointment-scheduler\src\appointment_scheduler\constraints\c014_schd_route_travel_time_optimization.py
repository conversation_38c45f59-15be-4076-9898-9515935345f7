"""
Route Travel Time Optimization Constraint (C014)

This constraint optimizes travel time between consecutive appointments for the same provider.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from ..planning_models import TimeSlotAssignment
from .base_constraints import _calculate_travel_time_between_appointments

def travel_time_consideration(constraint_factory: ConstraintFactory) -> Constraint:
    """Consider travel time between consecutive appointments for the same provider."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .join(TimeSlotAssignment,
                  Joiners.equal(lambda assignment: assignment.scheduled_appointment.provider))
            .filter(lambda assignment1, assignment2: (assignment1.id != assignment2.id and
                                                     assignment1.time_slot is not None and
                                                     assignment2.time_slot is not None and
                                                     _insufficient_travel_time(assignment1, assignment2)))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment1, assignment2: 1)
            .as_constraint("Travel time consideration"))


def _insufficient_travel_time(assignment1: TimeSlotAssignment, assignment2: TimeSlotAssignment) -> bool:
    """Check if there's insufficient travel time between appointments."""
    if assignment1.time_slot is None or assignment2.time_slot is None:
        return False

    # Calculate time difference between appointments
    # time_slot is a time object, not an object with start_time attribute
    time1 = assignment1.time_slot
    time2 = assignment2.time_slot

    # Convert to minutes for comparison
    minutes1 = time1.hour * 60 + time1.minute
    minutes2 = time2.hour * 60 + time2.minute
    
    time_diff = abs(minutes2 - minutes1)
    
    # Calculate required travel time with time and date context
    travel_time = _calculate_travel_time_between_appointments(
        assignment1.scheduled_appointment.appointment_data,
        assignment2.scheduled_appointment.appointment_data,
        target_time=time1,  # Use the first appointment's time for traffic calculation
        target_date=assignment1.scheduled_appointment.assigned_date
    )
    
    # Need at least travel time + 15 minutes buffer
    required_time = travel_time + 15
    return time_diff < required_time 