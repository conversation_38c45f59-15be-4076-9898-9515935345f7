# CareAxl Scheduling Engine

A production-ready healthcare scheduling optimization engine built with FastAPI, following Clean Architecture and Domain-Driven Design principles. This API-based service provides intelligent appointment assignment and day plan optimization by pulling data from external services and returning optimized scheduling solutions.

## 🏗️ Architecture

This service follows the **Clean Architecture** pattern with **Domain-Driven Design (DDD)** principles, organized into distinct layers:

- **Domain Layer**: Core business logic, entities, and value objects
- **Application Layer**: Use cases and application services  
- **Infrastructure Layer**: External concerns (API, database, external services)

### Key Features

- **API-Based Engine**: Pulls data from external patient, provider, and appointment services
- **Intelligent Optimization**: Advanced Timefold-powered constraint satisfaction algorithms
- **Dual-Stage Process**: Assignment optimization + Day plan routing optimization
- **Real-time API**: FastAPI with async support and OpenAPI documentation
- **Geographic Optimization**: Location-based provider assignment and routing
- **Constraint Handling**: Complex business rules, preferences, and availability
- **Enterprise Architecture**: Clean Architecture with DDD patterns
- **Microservice Ready**: Stateless engine that integrates with existing healthcare systems

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Git

### Local Development

```bash
# Clone the repository
git clone <repository-url>
cd caxl-scheduling-engine

# Install dependencies
pip install -e .

# Run the service
python -m src.main

# View API documentation
open http://localhost:8080/docs
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up --build

# View logs
make logs
```

## 📁 Project Structure

```
caxl-scheduling-engine/
├── src/                          # Application source code
│   ├── domain/                   # Domain layer (entities, value objects, enums)
│   │   ├── entities/             # Domain entities
│   │   ├── value_objects/        # Value objects
│   │   ├── enums/                # Domain enumerations
│   │   └── repositories/         # Repository interfaces
│   ├── application/              # Application layer
│   │   ├── usecases/             # Use cases (business workflows)
│   │   ├── ports/                # Interfaces/ports
│   │   ├── dtos/                 # Data transfer objects
│   │   └── mappers/              # Domain-DTO mappers
│   ├── infrastructure/           # Infrastructure layer
│   │   ├── api/                  # FastAPI routes and models
│   │   ├── adapters/             # External service adapters
│   │   └── persistence/          # Database implementations
│   ├── config/                   # Configuration
│   ├── container.py              # Dependency injection
│   └── main.py                   # Application entry point
├── tests/                        # Test suite
├── resources/                    # Resources (database scripts, etc.)
├── docker-compose.yml            # Development compose
├── Dockerfile                    # Production container
├── Dockerfile.dev               # Development container
├── Makefile                      # Development commands
└── pyproject.toml               # Python project configuration
```

## 🔧 Development

### Environment Setup

```bash
# Install dependencies
make install

# Setup development environment with pre-commit hooks
make setup-dev

# Run linting
make lint

# Run type checking
make type-check

# Run security scan
make security-check
```

### Testing

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific test categories
make test-unit
make test-integration
make test-api
```

### API Development

The service exposes a RESTful API built with FastAPI:

- **OpenAPI Documentation**: http://localhost:8080/docs
- **Health Check**: http://localhost:8080/health
- **Readiness Check**: http://localhost:8080/ready

#### Key Endpoints

- `GET /api/v1/appointments/` - List appointments
- `POST /api/v1/appointments/` - Create appointment
- `GET /api/v1/providers/` - List healthcare providers
- `GET /api/v1/consumers/` - List patients/consumers
- `POST /api/v1/scheduling/assign` - Run appointment assignment
- `POST /api/v1/scheduling/dayplan` - Run day plan optimization
- `GET /api/v1/scheduling/status` - Get scheduling system status

## 🐳 Docker

### Development

```bash
# Build development image
make docker-build-dev

# Run development container with hot reload
make docker-run-dev
```

### Production

```bash
# Build production image
make docker-build

# Run production container
make docker-run
```

## 📊 Monitoring

The service includes comprehensive monitoring capabilities:

- **Health Checks**: Kubernetes-ready liveness/readiness probes
- **Structured Logging**: JSON logging with correlation IDs
- **Error Handling**: Global error handling with proper HTTP status codes
- **Request Tracing**: Request/response logging middleware

## 🔒 Security

- **Input Validation**: Pydantic models with strict validation
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Security Headers**: Comprehensive security headers
- **Environment Variables**: Secure configuration management

## 🌍 Environment Configuration

The service supports multiple environments through configuration:

- **Development**: Local development with hot reload
- **Testing**: Isolated test environment  
- **Production**: Production-ready configuration

Copy `.env.example` to `.env` and configure as needed.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the architecture patterns
4. Add tests for new functionality
5. Run the test suite and linting
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [API Documentation](http://localhost:8080/docs)
- **Issues**: GitHub Issues
- **Architecture**: Clean Architecture with DDD patterns
