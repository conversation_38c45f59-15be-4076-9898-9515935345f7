"""Patient Episode of Care Order DTOs"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel

from src.domain.enums import OrderStatus


class PatientEpisodeOfCareOrderDirectiveDTO(BaseModel):
    """DTO for Patient Episode of Care Order Directive"""

    id: UUID | None = None
    instructions: str


class PatientEpisodeOfCareOrderBaseDTO(BaseModel):
    """Base DTO for Patient Episode of Care Order"""

    patient_episode_of_care_id: UUID
    order_status: OrderStatus
    order_date: datetime
    order_notes: str | None = None
    directives: list[PatientEpisodeOfCareOrderDirectiveDTO] | None = None


class PatientEpisodeOfCareOrderCreateDTO(PatientEpisodeOfCareOrderBaseDTO):
    """DTO for creating a Patient Episode of Care Order"""

    pass


class PatientEpisodeOfCareOrderUpdateDTO(BaseModel):
    """DTO for updating a Patient Episode of Care Order"""

    id: UUID
    order_status: OrderStatus
    order_date: datetime
    order_notes: str | None = None
    directives: list[PatientEpisodeOfCareOrderDirectiveDTO] | None = None


class PatientEpisodeOfCareOrderResponseDTO(PatientEpisodeOfCareOrderBaseDTO):
    """DTO for responding with a Patient Episode of Care Order"""

    id: UUID
    mod_at: datetime
    mod_by: UUID
    mod_service: str
    record_status: str

    class Config:
        from_attributes = True


class AddDirectiveDTO(BaseModel):
    """DTO for adding a single directive"""

    instructions: str


class AddDirectivesDTO(BaseModel):
    """DTO for adding directives to an order"""

    directives: list[AddDirectiveDTO]


class RemoveDirectivesDTO(BaseModel):
    """DTO for removing directives from an order"""

    directive_ids: list[UUID]
