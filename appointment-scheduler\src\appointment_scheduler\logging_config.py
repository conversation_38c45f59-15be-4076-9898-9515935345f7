"""
Logging configuration for the healthcare appointment scheduler.

This module provides centralized logging configuration using YAML files.
"""

from pathlib import Path
import logging
import os

from loguru import logger
import yaml
def setup_logging(config_path: str = "config/logger.yaml"):
    """
    Setup logging configuration from YAML file.
    
    Args:
        config_path: Path to the logging configuration YAML file
    """
    try:
        # Find the config file relative to the project root
        project_root = Path(__file__).parent.parent.parent
        config_file = project_root / config_path
        
        if not config_file.exists():
            logger.warning(f"Logging config file not found at {config_file}, using default configuration")
            return
        
        # Load YAML configuration
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Remove existing loguru handlers
        logger.remove()
        
        # Configure loguru based on YAML config
        _configure_loguru_from_yaml(config)
        
        logger.info(f"Logging configured from {config_file}")
        
    except Exception as e:
        logger.error(f"Failed to setup logging from {config_path}: {e}")
        # Fall back to basic configuration
        _setup_basic_logging()


def _configure_loguru_from_yaml(config: dict):
    """Configure loguru logger from YAML configuration."""
    
    # Get formatters
    formatters = config.get('formatters', {})
    
    # Get handlers
    handlers = config.get('handlers', {})
    
    # Get loggers
    loggers_config = config.get('loggers', {})
    
    # Configure root logger
    root_config = config.get('root', {})
    root_level = root_config.get('level', 'INFO')
    
    # Add console handler
    if 'console' in handlers:
        console_config = handlers['console']
        console_level = console_config.get('level', 'INFO')
        console_format = formatters.get(console_config.get('formatter', 'simple'), {}).get('format', '{time:HH:mm:ss} | {level} | {message}')
        
        logger.add(
            lambda msg: print(msg, end=""),
            level=console_level,
            format=console_format,
            filter=lambda record: record["name"] not in loggers_config or loggers_config[record["name"]].get('level', 'INFO') != 'DISABLED'
        )
    
    # Add file handler
    if 'file' in handlers:
        file_config = handlers['file']
        file_level = file_config.get('level', 'DEBUG')
        file_format = formatters.get(file_config.get('formatter', 'detailed'), {}).get('format', '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}')
        filename = file_config.get('filename', 'logs/scheduler.log')
        
        # Create logs directory if it doesn't exist
        log_dir = Path(filename).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            filename,
            level=file_level,
            format=file_format,
            rotation="1 day",
            retention="30 days",
            filter=lambda record: record["name"] not in loggers_config or loggers_config[record["name"]].get('level', 'INFO') != 'DISABLED'
        )
    
    # Add error file handler
    if 'error_file' in handlers:
        error_config = handlers['error_file']
        error_level = error_config.get('level', 'ERROR')
        error_format = formatters.get(error_config.get('formatter', 'detailed'), {}).get('format', '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}')
        error_filename = error_config.get('filename', 'logs/errors.log')
        
        # Create logs directory if it doesn't exist
        error_log_dir = Path(error_filename).parent
        error_log_dir.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            error_filename,
            level=error_level,
            format=error_format,
            rotation="1 day",
            retention="30 days"
        )


def _setup_basic_logging():
    """Setup basic logging configuration as fallback."""
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        "logs/scheduler.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}",
        rotation="1 day",
        retention="30 days"
    )


def get_logger(name: str):
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


# Initialize logging when module is imported
setup_logging() 