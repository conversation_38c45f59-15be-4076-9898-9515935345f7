import logging.config
from typing import Any

from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class LoggingSettings(BaseSettings):
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"
    LOG_FILE_MAX_BYTES: int = 10485760  # 10MB
    LOG_FILE_BACKUP_COUNT: int = 5

    model_config = ConfigDict(env_prefix="LOG_")

    @property
    def config(self) -> dict[str, Any]:
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "standard": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": "standard",
                    "level": self.LOG_LEVEL,
                },
                # "file": {
                #     "class": "logging.handlers.RotatingFileHandler",
                #     "filename": self.LOG_FILE,
                #     "maxBytes": self.LOG_FILE_MAX_BYTES,
                #     "backupCount": self.LOG_FILE_BACKUP_COUNT,
                #     "formatter": "standard",
                #     "level": self.LOG_LEVEL,
                # },
            },
            "root": {
                # "handlers": ["console", "file"],
                "handlers": ["console"],
                "level": self.LOG_LEVEL,
            },
        }


def setup_logging():
    """Setup logging configuration."""
    logging_settings = LoggingSettings()
    logging.config.dictConfig(logging_settings.config)


# Setup logging when this module is imported
setup_logging()

# Create a logger instance that can be imported by other modules
logger = logging.getLogger("main")
