"""Mapper for Patient Episode of Care Order"""

from uuid import UUID

from src.application.dtos.patient_episode_of_care_order_dto import (
    PatientEpisodeOfCareOrderCreateDTO,
    PatientEpisodeOfCareOrderDirectiveDTO,
    PatientEpisodeOfCareOrderResponseDTO,
    PatientEpisodeOfCareOrderUpdateDTO,
)
from src.domain.entities.patient_episode_of_care_order import PatientEpisodeOfCareOrder
from src.domain.enums import OrderStatus
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.value_objects.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)


class PatientEpisodeOfCareOrderMapper:
    """Mapper for Patient Episode of Care Order"""

    def to_dto(self, entity: PatientEpisodeOfCareOrder) -> PatientEpisodeOfCareOrderResponseDTO:
        """Convert entity to DTO"""
        if not entity:
            return None

        return PatientEpisodeOfCareOrderResponseDTO(
            id=entity.id,
            patient_episode_of_care_id=entity.patient_episode_of_care_id,
            order_status=entity.order_status.value,
            order_date=entity.order_date,
            order_notes=entity.order_notes,
            directives=(
                [
                    PatientEpisodeOfCareOrderDirectiveDTO(
                        id=directive.id, instructions=directive.instructions
                    )
                    for directive in entity.directives
                ]
                if entity.directives
                else []
            ),
            mod_at=entity.audit_stamp.mod_at,
            mod_by=entity.audit_stamp.mod_by,
            mod_service=entity.audit_stamp.mod_service,
            record_status=entity.audit_stamp.record_status.value,
        )

    def to_domain(
        self,
        dto: PatientEpisodeOfCareOrderCreateDTO | PatientEpisodeOfCareOrderUpdateDTO,
        audit_stamp: AuditStamp,
        existing_order: PatientEpisodeOfCareOrder = None,
    ) -> PatientEpisodeOfCareOrder:
        """Convert DTO to entity"""
        if not dto:
            return None

        # Convert order_status to enum
        try:
            order_status = OrderStatus(dto.order_status)
        except ValueError as e:
            raise ValueError(f"Invalid order status: {dto.order_status}") from e

        # Convert directives to value objects
        directives = []
        if dto.directives:
            for directive_dto in dto.directives:
                directives.append(
                    PatientEpisodeOfCareOrderDirective(
                        id=None,  # New directives don't need IDs
                        patient_episode_of_care_order_id=UUID(
                            "00000000-0000-0000-0000-000000000000"
                        ),  # Use temporary UUID for new directives
                        instructions=directive_dto.instructions,
                    )
                )

        # Create the entity with the provided audit_stamp
        if isinstance(dto, PatientEpisodeOfCareOrderCreateDTO):
            return PatientEpisodeOfCareOrder(
                id=None,  # ID will be generated when order is created
                patient_episode_of_care_id=dto.patient_episode_of_care_id,
                order_status=order_status,
                order_date=dto.order_date,
                order_notes=dto.order_notes,
                directives=directives,
                audit_stamp=audit_stamp,
            )
        else:  # UpdateDTO
            if not existing_order:
                raise ValueError(f"Order with ID {dto.id} not found")

            return PatientEpisodeOfCareOrder(
                id=dto.id,
                patient_episode_of_care_id=existing_order.patient_episode_of_care_id,  # Keep the existing episode ID
                order_status=order_status,
                order_date=dto.order_date,
                order_notes=dto.order_notes,
                directives=directives,
                audit_stamp=audit_stamp,
            )
