# Availability Edge Cases

**Purpose**: Test availability edge cases with various edge cases
**Best for**: Demonstrating robust availability edge cases handling
**Complexity**: High

## Edge Cases Covered

### Available_date
- available date scenarios
- Edge case handling
- Error recovery

### Unavailable_date
- unavailable date scenarios
- Edge case handling
- Error recovery

### Blackout_period
- blackout period scenarios
- Edge case handling
- Error recovery

### Weekend_coverage
- weekend coverage scenarios
- Edge case handling
- Error recovery

### Holiday_scheduling
- holiday scheduling scenarios
- Edge case handling
- Error recovery

### Invalid_date_format
- invalid date format scenarios
- Edge case handling
- Error recovery

### Timezone_issues
- timezone issues scenarios
- Edge case handling
- Error recovery

### Leap_year
- leap year scenarios
- Edge case handling
- Error recovery

### DST_changes
- dst changes scenarios
- Edge case handling
- Error recovery

## Data Overview
- **Providers**: 6-10 (with various configurations)
- **Patients**: 8-15 (with diverse requirements)
- **Appointments**: 12-20 (testing all edge cases)

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/availability_edge_cases/* data/

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Edge cases should be handled gracefully
- System should not crash on invalid data
- Appropriate error messages should be logged
- Valid cases should still work correctly
