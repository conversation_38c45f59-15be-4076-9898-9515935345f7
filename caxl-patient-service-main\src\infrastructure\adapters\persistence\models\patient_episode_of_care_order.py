"""Patient Episode of Care Order model"""

from uuid import UUID

from sqlalchemy import TIM<PERSON><PERSON>MP, Column, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientEpisodeOfCareOrder(BaseModel):
    """Patient Episode of Care Order model"""

    __tablename__ = "patient_episode_of_care_order"

    id = Column(PGUUID, primary_key=True, default=UUID)
    patient_episode_of_care_id = Column(
        PGUUID, ForeignKey("patient_episode_of_care.id"), nullable=False
    )
    order_status = Column(String(50), nullable=False)
    order_date = Column(TIMESTAMP(timezone=True), nullable=False)
    order_notes = Column(Text, nullable=True)

    # Relationships
    patient_episode_of_care = relationship(
        "PatientEpisodeOfCare", back_populates="patient_episode_of_care_orders"
    )
    directives = relationship(
        "PatientEpisodeOfCareOrderDirective", back_populates="order", cascade="all, delete-orphan"
    )
