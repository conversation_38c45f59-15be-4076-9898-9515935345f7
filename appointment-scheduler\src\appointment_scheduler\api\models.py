"""
API models for appointment scheduler REST endpoints.
"""

from datetime import date, datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from uuid import UUID


class LocationModel(BaseModel):
    """Location model for API."""
    latitude: float
    longitude: float
    city: Optional[str] = None
    state: Optional[str] = None
    address: Optional[str] = None


class PatientModel(BaseModel):
    """Patient/Consumer model for API."""
    id: str
    name: str
    location: LocationModel
    care_episode_id: Optional[str] = None
    consumer_preferences: Optional[Dict[str, Any]] = None


class CareStaffModel(BaseModel):
    """Care staff/Provider model for API."""
    id: str
    name: str
    role: Optional[str] = None
    skills: List[str] = []
    home_location: LocationModel
    availability: Optional[Dict[str, Any]] = None
    capacity: Optional[Dict[str, Any]] = None


class AppointmentModel(BaseModel):
    """Appointment model for API."""
    id: Optional[str] = None
    consumer_id: str
    appointment_date: Optional[date] = None
    required_skills: List[str] = []
    duration_min: int = 30
    urgent: bool = False
    location: Optional[LocationModel] = None
    priority: str = "normal"


class AssignmentRequest(BaseModel):
    """Request model for appointment assignment."""
    appointments: List[AppointmentModel]
    target_date: Optional[date] = None
    service_type: Optional[str] = None


class ReassignmentRequest(BaseModel):
    """Request model for appointment reassignment."""
    appointment_ids: List[str]
    reason: str
    force_unpin: bool = False


class DayPlanRequest(BaseModel):
    """Request model for day plan generation."""
    target_date: Optional[date] = None
    provider_ids: Optional[List[str]] = None


class AssignmentResult(BaseModel):
    """Result model for assignment operations."""
    success: bool
    message: str
    total_appointments: int
    assigned_appointments: int
    unassigned_appointments: int
    processing_time_seconds: float
    assignments: Optional[List[Dict[str, Any]]] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
