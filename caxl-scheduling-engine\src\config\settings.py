"""Application settings configuration"""

import yaml
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Application settings
    PROJECT_NAME: str = "caxl-scheduling-engine"
    VERSION: str = "0.1.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8080
    
    # API settings
    API_V1_STR: str = "/api/v1"
    
    # CORS settings
    CORS_ORIGINS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_HEADERS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_EXPOSE_HEADERS: List[str] = Field(default_factory=lambda: ["*"])
    
    # Database settings
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_USER: str = "admin"
    DB_PASSWORD: str = "schedulerDb"
    DB_NAME: str = "scheduler_dev"
    DB_ECHO: bool = False
    
    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Timefold settings
    TIMEFOLD_SOLVER_TIMEOUT: int = 120
    TIMEFOLD_LOG_LEVEL: str = "INFO"
    
    # Scheduler settings
    SCHEDULER_DAEMON_INTERVAL: int = 3600  # 1 hour
    SCHEDULER_DAILY_EXECUTION_TIME: str = "02:00"
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # External service URLs
    PATIENT_SERVICE_URL: str = "http://localhost:8081"
    PROVIDER_SERVICE_URL: str = "http://localhost:8082"
    APPOINTMENT_SERVICE_URL: str = "http://localhost:8083"

    # Feature flags
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = False
    ENABLE_CACHING: bool = True
    
    @property
    def database_url(self) -> str:
        """Get database URL"""
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    @property
    def redis_url(self) -> str:
        """Get Redis URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"


class SchedulerConfig:
    """Configuration loader for scheduler settings from YAML files."""

    def __init__(self, config_file: str = "config/scheduler.yml"):
        self.config_file = Path(config_file)
        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_file.exists():
            raise FileNotFoundError(f"Scheduler config file not found: {self.config_file}")

        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    @property
    def rolling_window_days(self) -> int:
        return self._config.get('rolling_window_days', 7)

    @property
    def max_solving_time_seconds(self) -> int:
        return self._config.get('max_solving_time_seconds', 300)

    @property
    def feature_toggles(self) -> Dict[str, bool]:
        """Get all feature toggle settings."""
        return {
            'enable_geographic_clustering': self._config.get('enable_geographic_clustering', True),
            'enable_continuity_of_care': self._config.get('enable_continuity_of_care', True),
            'enable_workload_balancing': self._config.get('enable_workload_balancing', True),
            'enable_patient_preferences': self._config.get('enable_patient_preferences', True),
            'enable_provider_capacity_management': self._config.get('enable_provider_capacity_management', True),
            'enable_healthcare_task_sequencing': self._config.get('enable_healthcare_task_sequencing', True),
            'enable_travel_time_optimization': self._config.get('enable_travel_time_optimization', True),
            'enable_break_time_management': self._config.get('enable_break_time_management', True),
            'enable_route_optimization': self._config.get('enable_route_optimization', True),
            'enable_pinned_appointments': self._config.get('enable_pinned_appointments', True),
            'respect_pinned_times': self._config.get('respect_pinned_times', True),
            'allow_pinned_provider_changes': self._config.get('allow_pinned_provider_changes', False),
        }

    @property
    def constraints(self) -> Dict[str, Any]:
        """Get constraint configuration."""
        return self._config.get('constraints', {})

    @property
    def external_services(self) -> Dict[str, Any]:
        """Get external service configuration."""
        return self._config.get('external_services', {})

    @property
    def solver_config(self) -> Dict[str, Any]:
        """Get solver configuration."""
        return self._config.get('solver', {})


class ServiceConfig:
    """Configuration loader for service-specific settings."""

    def __init__(self, config_folder: str = "config"):
        self.config_folder = Path(config_folder)
        self._service_configs = {}
        self._load_all_service_configs()

    def _load_all_service_configs(self):
        """Load all service configuration files."""
        if not self.config_folder.exists():
            raise FileNotFoundError(f"Config folder not found: {self.config_folder}")

        # Load known service types
        service_files = [
            'skilled_nursing.yml',
            'physical_therapy.yml',
            'home_health.yml'
        ]

        for service_file in service_files:
            service_path = self.config_folder / service_file
            if service_path.exists():
                with open(service_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    service_type = config.get('service_type')
                    if service_type:
                        self._service_configs[service_type] = config

    def get_service_config(self, service_type: str) -> Dict[str, Any]:
        """Get configuration for a specific service type."""
        return self._service_configs.get(service_type, {})

    def get_all_service_types(self) -> List[str]:
        """Get list of all configured service types."""
        return list(self._service_configs.keys())

    def get_required_skills(self, service_type: str) -> List[str]:
        """Get required skills for a service type."""
        config = self.get_service_config(service_type)
        return config.get('required_skills', [])

    def get_constraint_weights(self, service_type: str) -> Dict[str, float]:
        """Get constraint weights for a service type."""
        config = self.get_service_config(service_type)
        return {
            'continuity_weight': config.get('continuity_weight', 0.7),
            'workload_balance_weight': config.get('workload_balance_weight', 0.6),
            'geographic_clustering_weight': config.get('geographic_clustering_weight', 0.5),
            'patient_preference_weight': config.get('patient_preference_weight', 0.6),
            'capacity_threshold_percentage': config.get('capacity_threshold_percentage', 0.9)
        }

    def get_service_settings(self, service_type: str) -> Dict[str, Any]:
        """Get service-specific settings."""
        config = self.get_service_config(service_type)
        return {
            'visit_duration_minutes': config.get('visit_duration_minutes', 60),
            'requires_initial_assessment': config.get('requires_initial_assessment', True),
            'allows_weekend_visits': config.get('allows_weekend_visits', False),
            'emergency_response_time_hours': config.get('emergency_response_time_hours', 24),
            'geographic_radius_miles': config.get('geographic_radius_miles', 25.0),
            'max_daily_appointments_per_provider': config.get('max_daily_appointments_per_provider', 6),
            'max_weekly_hours_per_provider': config.get('max_weekly_hours_per_provider', 40)
        }


# Global settings instances
# Global settings instance
settings = Settings()

# Import the config manager for use throughout the application
try:
    from src.infrastructure.config.config_manager import config_manager
    logger = logging.getLogger(__name__)
    logger.info("✅ Configuration system initialized")
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ Configuration system not available: {e}")
    config_manager = None
scheduler_config = SchedulerConfig()
service_config = ServiceConfig()
