"""Application settings configuration"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Application settings
    PROJECT_NAME: str = "caxl-scheduling-engine"
    VERSION: str = "0.1.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8080
    
    # API settings
    API_V1_STR: str = "/api/v1"
    
    # CORS settings
    CORS_ORIGINS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_HEADERS: List[str] = Field(default_factory=lambda: ["*"])
    CORS_EXPOSE_HEADERS: List[str] = Field(default_factory=lambda: ["*"])
    
    # Database settings
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_USER: str = "admin"
    DB_PASSWORD: str = "schedulerDb"
    DB_NAME: str = "scheduler_dev"
    DB_ECHO: bool = False
    
    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Timefold settings
    TIMEFOLD_SOLVER_TIMEOUT: int = 120
    TIMEFOLD_LOG_LEVEL: str = "INFO"
    
    # Scheduler settings
    SCHEDULER_DAEMON_INTERVAL: int = 3600  # 1 hour
    SCHEDULER_DAILY_EXECUTION_TIME: str = "02:00"
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # External service URLs
    PATIENT_SERVICE_URL: str = "http://localhost:8081"
    PROVIDER_SERVICE_URL: str = "http://localhost:8082"
    APPOINTMENT_SERVICE_URL: str = "http://localhost:8083"

    # Feature flags
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = False
    ENABLE_CACHING: bool = True
    
    @property
    def database_url(self) -> str:
        """Get database URL"""
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    @property
    def redis_url(self) -> str:
        """Get Redis URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"


settings = Settings()
