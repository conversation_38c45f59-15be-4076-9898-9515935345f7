version: '3.8'

services:
  appointment_scheduler_service:
    build:
      context: .
      dockerfile: Dockerfile.dev
    network_mode: host
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - HOST=0.0.0.0
      - PORT=8080
      - PROJECT_NAME=appointment-scheduler-service
      - API_V1_STR=/api/v1
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=True
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      - CORS_EXPOSE_HEADERS=["*"]
      - DB_HOST=localhost
      - DB_PORT=5434
      - DB_USER=admin
      - DB_PASSWORD=schedulerDb
      - DB_ECHO=false
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - TIMEFOLD_SOLVER_TIMEOUT=120
      - SCHEDULER_DAEMON_INTERVAL=3600
    volumes:
      - .:/app
    command: uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=scheduler_dev
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=schedulerDb
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./resources/database:/docker-entrypoint-initdb.d
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
