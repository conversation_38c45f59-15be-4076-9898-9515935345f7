"""Dependency injection container"""

from dependency_injector import containers, providers
from dependency_injector.wiring import Provide

from src.config.settings import settings


class Container(containers.DeclarativeContainer):
    """Application dependency injection container"""
    
    # Configuration
    config = providers.Configuration()
    
    # Database
    # database = providers.Singleton(
    #     Database,
    #     url=settings.database_url,
    # )
    
    # Redis
    # redis = providers.Singleton(
    #     Redis,
    #     url=settings.redis_url,
    # )
    
    # Repositories
    # appointment_repository = providers.Factory(
    #     AppointmentRepository,
    #     database=database,
    # )
    
    # Services
    # scheduling_service = providers.Factory(
    #     SchedulingService,
    #     config=config,
    # )
    
    # Use cases
    # schedule_appointments_use_case = providers.Factory(
    #     ScheduleAppointmentsUseCase,
    #     appointment_repository=appointment_repository,
    #     scheduling_service=scheduling_service,
    # )


# Global container instance
container = Container()

# Configure container
container.config.from_dict({
    "database_url": settings.database_url,
    "redis_url": settings.redis_url,
    "timefold_timeout": settings.TIMEFOLD_SOLVER_TIMEOUT,
})
