"""Input port for patient use cases"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.application.dtos.patient import PatientDTO, PatientListResponse
from src.infrastructure.api.schemas.auth_schemas import AuthSession


class PatientUseCasePort(ABC):
    """Input port for patient use cases"""

    @abstractmethod
    async def get_patient(self, patient_id: UUID) -> PatientDTO | None:
        """Get patient by ID"""
        pass

    @abstractmethod
    async def list_patients(self, page: int = 1, page_size: int = 10) -> PatientListResponse:
        """List patients with pagination"""
        pass

    @abstractmethod
    async def create_patient(self, patient: PatientDTO, context: AuthSession) -> PatientDTO:
        """Create new patient"""
        pass

    @abstractmethod
    async def update_patient(self, patient: PatientDTO, context: AuthSession) -> PatientDTO:
        """Update existing patient"""
        pass

    @abstractmethod
    async def delete_patient(self, patient_id: UUID, context: AuthSession) -> bool:
        """Delete patient"""
        pass
