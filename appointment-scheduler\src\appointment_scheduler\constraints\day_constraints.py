"""
Day planning stage constraints for healthcare scheduling optimization.

This module contains active constraints for the second stage of optimization:
- Assigning time slots to appointments that already have providers and dates

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory
from ..config_manager import ConfigManager

# Import individual constraint modules
from .c010_schd_timeslot_availability_validation import time_slot_availability
from .c011_schd_appointment_overlap_prevention import no_double_booking
from .c012_schd_flexible_appointment_timing_optimization import appointment_duration_fit, preferred_hours
from .c013_schd_healthcare_task_sequencing import healthcare_task_sequencing
from .c014_schd_route_travel_time_optimization import travel_time_consideration
from .c015_schd_timed_appointment_pinning import provider_break_time
from .c016_schd_route_optimization import route_optimization_constraints


@constraint_provider
def define_day_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the day planning stage."""
    # Get configuration to check feature toggles
    config_manager = ConfigManager()
    scheduler_config = config_manager.get_scheduler_config()
    
    constraints = []
    
    # Hard constraints - must be satisfied (always enabled)
    constraints.append(time_slot_availability(constraint_factory))
    constraints.append(no_double_booking(constraint_factory))
    constraints.append(appointment_duration_fit(constraint_factory))
    
    # Soft constraints - optimization preferences (conditional based on feature toggles)
    # Note: preferred_hours is always enabled as it's part of the basic timing optimization
    constraints.append(preferred_hours(constraint_factory))
    
    if scheduler_config.enable_travel_time_optimization:
        constraints.append(travel_time_consideration(constraint_factory))
    
    if scheduler_config.enable_break_time_management:
        constraints.append(provider_break_time(constraint_factory))
    
    # Advanced features (Premium Plan)
    if scheduler_config.enable_healthcare_task_sequencing:
        constraints.append(healthcare_task_sequencing(constraint_factory))
    
    # Route optimization constraints (Enterprise Plan)
    if scheduler_config.enable_route_optimization:
        route_constraints = route_optimization_constraints(constraint_factory)
        constraints.extend(route_constraints)
    
    return constraints 