"""FastAPI application factory"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from src.config.settings import settings
from src.infrastructure.api.middleware.logging import LoggingMiddleware
from src.infrastructure.api.middleware.error_handler import ErrorHandlerMiddleware
from src.infrastructure.api.routes import health
from src.infrastructure.api.routes.v1 import api_router


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="Healthcare Appointment Scheduler Service",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
        debug=settings.DEBUG,
    )
    
    # Add middleware
    _add_middleware(app)
    
    # Add routes
    _add_routes(app)
    
    return app


def _add_middleware(app: FastAPI) -> None:
    """Add middleware to the application"""
    
    # Error handling middleware (should be first)
    app.add_middleware(ErrorHandlerMiddleware)
    
    # Logging middleware
    app.add_middleware(LoggingMiddleware)
    
    # CORS middleware
    if settings.CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.CORS_ORIGINS,
            allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
            allow_methods=settings.CORS_ALLOW_METHODS,
            allow_headers=settings.CORS_ALLOW_HEADERS,
            expose_headers=settings.CORS_EXPOSE_HEADERS,
        )
    
    # Trusted host middleware
    if not settings.DEBUG:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # Configure appropriately for production
        )


def _add_routes(app: FastAPI) -> None:
    """Add routes to the application"""
    
    # Health check route
    app.include_router(health.router, tags=["health"])
    
    # API v1 routes
    app.include_router(api_router, prefix=settings.API_V1_STR)
