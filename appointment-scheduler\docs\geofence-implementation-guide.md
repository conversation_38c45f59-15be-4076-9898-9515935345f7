# Geofence Implementation Guide

This document explains how polygon-based geofences are implemented in the healthcare appointment scheduling system.

## Overview

The system supports **polygon-based geofences** as the primary method for defining provider service areas, with **circular radius** as a fallback option.

## How Polygon Geofences Work

### 1. Geofence Definition
A geofence is defined as a polygon using a series of coordinate points (latitude, longitude):

```python
# Example: Provider service area polygon (Manhattan area)
geofence_polygon = [
    (40.7589, -73.9851),  # Times Square
    (40.7505, -73.9934),  # Penn Station
    (40.7484, -73.9857),  # Empire State Building
    (40.7589, -73.9851)   # Close the polygon (same as first point)
]
```

### 2. Point-in-Polygon Algorithm
The system uses the **Ray Casting Algorithm** (also known as the Point-in-Polygon test) to determine if a consumer location is inside a provider's geofence:

```python
def _is_point_in_polygon(point: Location, polygon: List[Tuple[float, float]]) -> bool:
    """
    Ray Casting Algorithm:
    1. Draw a horizontal ray from the point to the right
    2. Count how many polygon edges the ray intersects
    3. If count is odd: point is inside
    4. If count is even: point is outside
    """
```

### 3. Distance Calculation
For points outside the polygon, the system calculates the **minimum distance to the polygon edge**:

```python
def _distance_to_polygon_edge(point: Location, polygon: List[Tuple[float, float]]) -> float:
    """
    Calculate distance from point to nearest polygon edge:
    1. For each polygon edge (line segment)
    2. Calculate distance from point to that line segment
    3. Return the minimum distance
    """
```

## Implementation Details

### Provider Model Extension
To support polygon geofences, the Provider model should include:

```python
class Provider:
    # ... existing fields ...
    geofence_polygon: Optional[List[Tuple[float, float]]] = None
    # OR
    service_areas: List[ServiceArea] = []
    
class ServiceArea:
    polygon_coordinates: List[Tuple[float, float]]
    area_name: str
    priority: int
```

### Geofence Check Flow

```python
def _is_within_geofence(provider: Provider, appointment_location: Location) -> bool:
    # 1. Check if provider has polygon geofence
    if _has_polygon_geofence(provider):
        polygon = _get_provider_geofence_polygon(provider)
        return _is_point_in_polygon(appointment_location, polygon)
    
    # 2. Fallback to circular radius
    return _is_within_service_radius_enhanced(provider, appointment_location)
```

## Examples

### Example 1: Manhattan Provider
```python
# Provider based in Manhattan
provider.geofence_polygon = [
    (40.7589, -73.9851),  # Times Square
    (40.7505, -73.9934),  # Penn Station  
    (40.7484, -73.9857),  # Empire State Building
    (40.7589, -73.9851)   # Close polygon
]

# Consumer locations
consumer1 = Location(latitude=40.7589, longitude=-73.9851)  # Times Square ✅
consumer2 = Location(latitude=40.7128, longitude=-74.0060)  # Financial District ❌
consumer3 = Location(latitude=40.7505, longitude=-73.9934)  # Penn Station ✅
```

### Example 2: Multi-Area Provider
```python
# Provider with multiple service areas
provider.service_areas = [
    ServiceArea(
        polygon_coordinates=[...],  # Brooklyn area
        area_name="Brooklyn",
        priority=1
    ),
    ServiceArea(
        polygon_coordinates=[...],  # Queens area  
        area_name="Queens",
        priority=2
    )
]

# Check if consumer is in any service area
def is_in_any_service_area(provider, consumer_location):
    for area in provider.service_areas:
        if _is_point_in_polygon(consumer_location, area.polygon_coordinates):
            return True
    return False
```

## Penalty System

### Polygon-Based Penalties
```python
def _calculate_polygon_penalty(distance_to_edge: float) -> int:
    if distance_to_edge <= 0:
        return 0  # Inside polygon
    
    # Penalty increases with distance from polygon edge
    if distance_to_edge <= 5:
        return 1
    elif distance_to_edge <= 10:
        return 2
    elif distance_to_edge <= 20:
        return 3
    else:
        return int(distance_to_edge / 5) + 3
```

### Penalty Examples
- **Inside polygon**: 0 penalty
- **5 miles outside**: 1 penalty
- **10 miles outside**: 2 penalty  
- **20 miles outside**: 3 penalty
- **25 miles outside**: 8 penalty

## Advantages of Polygon Geofences

### 1. **Precise Boundaries**
- Define exact service area boundaries
- Follow natural geographic features (rivers, highways, city limits)
- Avoid serving areas outside intended coverage

### 2. **Complex Shapes**
- Support irregular service areas
- Handle multiple disconnected areas
- Account for geographic obstacles

### 3. **Realistic Coverage**
- Match actual provider service patterns
- Consider transportation routes
- Reflect administrative boundaries

### 4. **Flexibility**
- Easy to update service areas
- Support seasonal or temporary changes
- Handle special coverage areas

## Fallback to Circular Radius

When polygon geofence data is not available, the system falls back to circular radius calculation:

```python
# Circular radius with provider type adjustments
base_radius = 25.0  # miles
if provider.role == "RN":
    radius = base_radius * 1.2  # 30 miles
elif provider.role == "CNA":
    radius = base_radius * 0.8  # 20 miles
```

## Data Sources

Polygon geofence data can come from:

1. **GIS Systems**: Import from shapefiles or GeoJSON
2. **Mapping APIs**: Google Maps, Mapbox, OpenStreetMap
3. **Administrative Data**: City/county boundaries
4. **Custom Definitions**: Manually defined service areas

## Performance Considerations

### Optimization Strategies
1. **Bounding Box Pre-check**: Quick rectangular bounds check before polygon test
2. **Spatial Indexing**: Use R-trees or similar spatial data structures
3. **Caching**: Cache frequently accessed geofence data
4. **Simplification**: Reduce polygon complexity for performance

### Example Bounding Box Check
```python
def _quick_bounds_check(point: Location, polygon: List[Tuple[float, float]]) -> bool:
    """Quick check using bounding box before detailed polygon test."""
    if not polygon:
        return False
    
    lats = [p[0] for p in polygon]
    lons = [p[1] for p in polygon]
    
    min_lat, max_lat = min(lats), max(lats)
    min_lon, max_lon = min(lons), max(lons)
    
    return (min_lat <= point.latitude <= max_lat and 
            min_lon <= point.longitude <= max_lon)
```

## Integration with Constraint System

The polygon geofence check is integrated into **C003: Geographic Service Area** constraint:

```python
def geographic_service_area(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: not _is_within_geofence(
                assignment.provider, assignment.appointment_data.location))
            .penalize(HardSoftScore.ONE_SOFT, 
                     lambda assignment: _calculate_geographic_penalty(...))
            .as_constraint("Geographic service area"))
```

This ensures that the scheduling optimization considers precise geographic boundaries when assigning providers to appointments. 