# Healthcare Scheduling Constraint Checks Enumeration

This document provides a comprehensive enumeration of all checks implemented under each constraint in the healthcare appointment scheduling optimization system.

## Assignment Stage Constraints (C001-C009)

### C001: Provider Skill Validation (HARD)
**Purpose:** Ensures providers have the required skills for appointments.

**Checks performed:**
1. **Skill Presence Validation**
   - Check if provider has all required skills for the appointment
   - Normalize skills (lowercase, strip whitespace) for comparison
   - Calculate missing skills count

2. **Partial Skill Matching**
   - Check for similar skills that can substitute for missing ones:
     - `medication_management` ↔ `medication_administration`
     - `physical_therapy` ↔ `mobility_training`, `strength_training`
     - `personal_care` ↔ `mobility_assistance`, `housekeeping`
     - `wound_care` ↔ `basic_care`, `assessment`

3. **Penalty Calculation**
   - Base penalty for missing skills
   - Reduced penalty for partial matches (0.5x)
   - Severe penalty for providers with no skills

---

### C002: Date-Based Availability (HARD)
**Purpose:** Ensures providers are available on assigned dates.

**Checks performed:**
1. **Basic Availability**
   - Check if provider is available on the target date
   - Verify provider's working schedule

2. **Enhanced Date Validation**
   - Check if date is a working day for the provider
   - Verify date is not in a blackout period
   - Check if date is not a holiday
   - Validate weekend coverage requirements

3. **Date Format Validation**
   - Ensure valid date format
   - Handle timezone considerations
   - Account for leap years and DST changes

---

### C003: Geographic Service Area (SOFT)
**Purpose:** Ensures providers are within reasonable service area of patient location.

**Checks performed:**
1. **Distance Calculation**
   - Calculate Haversine distance between provider and patient locations
   - Handle invalid coordinates gracefully

2. **Service Radius Determination**
   - Base radius: 25 miles
   - Adjust based on provider type:
     - Doctors: 1.5x multiplier
     - Nurses: 1.2x multiplier
     - CNAs: 0.8x multiplier
     - PTs: 1.3x multiplier
     - LPNs: 1.0x multiplier

3. **Area Type Adjustments**
   - Rural areas: 1.5x multiplier
   - Urban areas: 0.8x multiplier
   - Cross-state boundaries: 0.7x multiplier

4. **Penalty Calculation**
   - Distance-based penalty for out-of-range assignments
   - Higher penalties for greater distances

---

### C004: Provider Role Match (HARD)
**Purpose:** Ensures provider roles match appointment requirements when specified.

**Checks performed:**
1. **Role Validation**
   - Check if appointment has a required role specified
   - Verify provider's role matches the required role
   - Apply hard penalty for role mismatches

---

### C005: Workload Balance Optimization (SOFT)
**Purpose:** Balances workload across providers to prevent overloading.

**Checks performed:**
1. **Daily Task Count**
   - Count appointments assigned to each provider per day
   - Check if count exceeds provider's daily task limit
   - Apply penalty proportional to excess count

---

### C006: Geographic Clustering Optimization (SOFT)
**Purpose:** Maintains capacity thresholds for service types per date.

**Checks performed:**
1. **Service Type Grouping**
   - Group appointments by date and required skills
   - Count appointments per service type per date

2. **Capacity Threshold**
   - Check if count exceeds maximum (3 appointments per date per service type)
   - Apply penalty for exceeding threshold

---

### C007: Patient Preference Matching (SOFT)
**Purpose:** Matches patient preferences for providers, times, and other factors.

**Checks performed:**
1. **Timing Preferences**
   - Check if appointment is a timed visit with preferred time
   - Morning preferences (before 12 PM) on weekdays vs weekends
   - Evening preferences (after 5 PM) on weekdays vs weekends
   - Appointment flexibility requirements

2. **Priority and Urgency**
   - Urgent appointments: prefer RNs and providers with 3+ skills
   - High priority appointments: prefer RNs or PTs
   - Penalty for mismatched priority levels

3. **Location Preferences**
   - City matching between appointment location and provider home location
   - Penalty for location mismatches

4. **Custom Preferences**
   - Preferred provider role matching
   - Preferred language matching
   - Cultural considerations

---

### C008: Provider Capacity Management (SOFT)
**Purpose:** Manages provider capacity and workload limits.

**Checks performed:**
1. **Daily Task Count Limits**
   - Check if provider exceeds daily task count limit
   - Penalty: 5 points for exceeding limit

2. **Daily Task Points Limits**
   - Check if adding appointment would exceed daily task points
   - Penalty: 4 points for exceeding limit

3. **Daily Hours Limits**
   - Check if adding appointment would exceed daily hours limit
   - Penalty: 6 points for exceeding limit

4. **Weekly Hours Limits**
   - Check if adding appointment would exceed weekly hours limit
   - Penalty: 5 points for exceeding limit

5. **Emergency Capacity Reserves**
   - For critical providers: check if utilization ≥ 90%
   - Penalty: 4 points for high utilization

6. **Specialization Capacity**
   - Skill match ratio calculation
   - Role matching validation
   - Penalty based on missing skills and role mismatches

---

### C009: Continuity of Care Optimization (SOFT)
**Purpose:** Prefers the same provider for the same patient when possible.

**Checks performed:**
1. **Patient Provider Consistency**
   - Check if same patient has different providers across appointments
   - Apply penalty for provider changes per patient

---

## Day Planning Stage Constraints (C010-C015)

### C010: Time Slot Availability Validation (HARD)
**Purpose:** Ensures time slots are available for assignment.

**Checks performed:**
1. **Slot Availability**
   - Verify time slot is not already assigned
   - Check slot availability status

---

### C011: Appointment Overlap Prevention (HARD)
**Purpose:** Prevents providers from being double-booked for the same time slot.

**Checks performed:**
1. **Double Booking Detection**
   - Check for multiple appointments assigned to same provider in same time slot
   - Apply hard penalty for overlaps

---

### C012: Flexible Appointment Timing Optimization (HARD/SOFT)
**Purpose:** Handles appointment duration fitting and preferred hours optimization.

**Checks performed:**
1. **Duration Fit (HARD)**
   - Check if appointment duration fits within assigned time slot
   - Apply hard penalty if duration exceeds slot duration

2. **Preferred Hours (SOFT)**
   - Check if time slot is within provider's preferred working hours (8 AM - 6 PM)
   - Apply soft penalty for assignments outside preferred hours

---

### C013: Healthcare Task Sequencing (SOFT)
**Purpose:** Optimizes the sequencing of healthcare tasks and procedures.

**Checks performed:**
1. **Procedure Dependencies**
   - Check if appointment has prerequisite dependencies
   - Verify care episode sequencing order
   - First in sequence: prefer early in day (before noon)
   - Later in sequence: prefer later in day (after 10 AM)

2. **Equipment Sequencing**
   - Identify equipment-intensive procedures (IV therapy, wound care, PT, rehab)
   - Prefer mid-morning (9-11 AM) or mid-afternoon (2-4 PM) for equipment procedures
   - Avoid very early times (before 9 AM)

3. **Patient Preparation**
   - Identify preparation-required procedures (wound care, IV therapy, PT, assessment)
   - Avoid very early times (before 8 AM)
   - Prefer morning appointments (before 2 PM)
   - Fasting procedures: prefer early in day (before noon)

4. **Medical Protocols**
   - Check medication timing requirements vs preferred times
   - Penalty for deviations > 2 hours from preferred time
   - Urgent procedures: prefer early in day (before noon)
   - Specific time window requirements

5. **Task Complexity Sequencing**
   - High complexity (task points > 8): prefer 9 AM - 3 PM
   - Low complexity (task points < 3): avoid peak hours (10 AM - 2 PM)
   - Multi-skill procedures: avoid very early/late times (before 8 AM or after 4 PM)

---

### C014: Route Travel Time Optimization (SOFT)
**Purpose:** Optimizes travel time between consecutive appointments for the same provider.

**Checks performed:**
1. **Travel Time Calculation**
   - Calculate travel time between consecutive appointments
   - Add 15-minute buffer to required travel time

2. **Time Gap Validation**
   - Check if time gap between appointments is sufficient for travel + buffer
   - Apply penalty for insufficient travel time

---

### C015: Timed Appointment Pinning (SOFT)
**Purpose:** Ensures providers have adequate break time between appointments.

**Checks performed:**
1. **Break Time Validation**
   - Check time difference between consecutive appointments for same provider
   - Ensure minimum 15-minute break between appointments
   - Apply penalty for insufficient break time

---

## Summary

The constraint system implements a comprehensive set of checks covering:

- **Hard Constraints (Must Satisfy):** Skills, availability, role matching, duration fit, overlap prevention
- **Soft Constraints (Optimization):** Preferences, capacity management, sequencing, travel time, break time

Total constraints: 15 (9 assignment + 6 day planning)
Total checks: 50+ individual validation points across all constraints

This multi-layered approach ensures both feasibility (hard constraints) and optimization (soft constraints) in healthcare appointment scheduling. 