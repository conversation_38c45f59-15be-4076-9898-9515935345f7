"""Location value object"""

import math
from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class Location:
    """Location value object with geographic coordinates"""
    
    latitude: float
    longitude: float
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    
    def __post_init__(self):
        """Validate coordinates"""
        if not (-90 <= self.latitude <= 90):
            raise ValueError("Latitude must be between -90 and 90")
        
        if not (-180 <= self.longitude <= 180):
            raise ValueError("Longitude must be between -180 and 180")
    
    def distance_to(self, other: "Location") -> float:
        """Calculate distance to another location in miles using Haversine formula"""
        if not isinstance(other, Location):
            raise TypeError("Other must be a Location instance")
        
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(
            math.radians, [self.latitude, self.longitude, other.latitude, other.longitude]
        )
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Radius of earth in miles
        r = 3956
        
        return c * r
    
    def is_within_radius(self, other: "Location", radius_miles: float) -> bool:
        """Check if location is within specified radius of another location"""
        return self.distance_to(other) <= radius_miles
    
    @property
    def coordinates(self) -> tuple[float, float]:
        """Get coordinates as tuple (latitude, longitude)"""
        return (self.latitude, self.longitude)
    
    @property
    def full_address(self) -> str:
        """Get full formatted address"""
        parts = []
        
        if self.address:
            parts.append(self.address)
        
        city_state = []
        if self.city:
            city_state.append(self.city)
        if self.state:
            city_state.append(self.state)
        
        if city_state:
            parts.append(", ".join(city_state))
        
        if self.zip_code:
            parts.append(self.zip_code)
        
        return ", ".join(parts) if parts else f"{self.latitude}, {self.longitude}"
    
    def __str__(self) -> str:
        """String representation"""
        return self.full_address
