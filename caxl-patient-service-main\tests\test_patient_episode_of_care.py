"""Test cases for patient_episode_of_care endpoints"""

from uuid import UUID

import pytest
from httpx import AsyncClient


@pytest.mark.asyncio
async def test_create_patient_episode_of_care(client: AsyncClient, test_patient_id: UUID):
    payload = {
        "patient_id": str(test_patient_id),
        "patient_mrn_id": None,
        "location_id": None,
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    response = await client.post("/api/v1/patient-eoc", json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["patient_id"] == str(test_patient_id)
    assert data["discharge_summary"] == "Test discharge summary"
    assert data["clinical_diagnosis"] == "Test clinical diagnosis"
    assert data["insurance_name"] == "Test Insurance"
    assert data["insurance_type"] == "Test Type"
    assert data["insurance_number"] == "123456"


@pytest.mark.asyncio
async def test_get_patient_episode_of_care(client: AsyncClient, test_patient_id: UUID):
    # First create an episode
    payload = {
        "patient_id": str(test_patient_id),
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    create_response = await client.post("/api/v1/patient-eoc", json=payload)
    assert create_response.status_code == 200
    episode_id = create_response.json()["id"]

    # Then retrieve it
    response = await client.get(f"/api/v1/patient-eoc/{episode_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == episode_id
    assert data["patient_id"] == str(test_patient_id)
    assert data["discharge_summary"] == "Test discharge summary"
    assert data["clinical_diagnosis"] == "Test clinical diagnosis"
    assert data["insurance_name"] == "Test Insurance"
    assert data["insurance_type"] == "Test Type"
    assert data["insurance_number"] == "123456"


@pytest.mark.asyncio
async def test_update_patient_episode_of_care(client: AsyncClient, test_patient_id: UUID):
    # First create an episode
    payload = {
        "patient_id": str(test_patient_id),
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    create_response = await client.post("/api/v1/patient-eoc", json=payload)
    assert create_response.status_code == 200
    episode_id = create_response.json()["id"]

    # Then update it
    update_payload = {
        "patient_id": str(test_patient_id),
        "discharge_summary": "Updated discharge summary",
        "clinical_diagnosis": "Updated clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Updated Insurance",
        "insurance_type": "Updated Type",
        "insurance_number": "654321",
    }
    response = await client.put(f"/api/v1/patient-eoc/{episode_id}", json=update_payload)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == episode_id
    assert data["patient_id"] == str(test_patient_id)
    assert data["discharge_summary"] == "Updated discharge summary"
    assert data["clinical_diagnosis"] == "Updated clinical diagnosis"
    assert data["insurance_name"] == "Updated Insurance"
    assert data["insurance_type"] == "Updated Type"
    assert data["insurance_number"] == "654321"


@pytest.mark.asyncio
async def test_delete_patient_episode_of_care(client: AsyncClient, test_patient_id: UUID):
    # First create an episode
    payload = {
        "patient_id": str(test_patient_id),
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    create_response = await client.post("/api/v1/patient-eoc", json=payload)
    assert create_response.status_code == 200
    episode_id = create_response.json()["id"]

    # Then delete it
    response = await client.delete(f"/api/v1/patient-eoc/{episode_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Patient episode deleted successfully"

    # Verify it's deleted
    get_response = await client.get(f"/api/v1/patient-eoc/{episode_id}")
    assert get_response.status_code == 404


@pytest.mark.asyncio
async def test_create_patient_episode_of_care_with_invalid_patient_id(client: AsyncClient):
    payload = {
        "patient_id": "invalid-uuid",
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    response = await client.post("/api/v1/patient-eoc", json=payload)
    assert response.status_code == 400
    assert "Invalid UUID" in response.json()["detail"]


@pytest.mark.asyncio
async def test_create_patient_episode_of_care_with_missing_required_fields(client: AsyncClient):
    payload = {
        "discharge_summary": "Test discharge summary",
        "clinical_diagnosis": "Test clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Test Insurance",
        "insurance_type": "Test Type",
        "insurance_number": "123456",
    }
    response = await client.post("/api/v1/patient-eoc", json=payload)
    assert response.status_code == 400
    assert "patient_id" in response.json()["detail"]


@pytest.mark.asyncio
async def test_get_patient_episode_of_care_with_nonexistent_id(client: AsyncClient):
    response = await client.get("/api/v1/patient-eoc/00000000-0000-0000-0000-000000000000")
    assert response.status_code == 404
    assert "Patient episode not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_update_patient_episode_of_care_with_nonexistent_id(
    client: AsyncClient, test_patient_id: UUID
):
    update_payload = {
        "patient_id": str(test_patient_id),
        "discharge_summary": "Updated discharge summary",
        "clinical_diagnosis": "Updated clinical diagnosis",
        "date_of_start_of_care": "2023-01-01T00:00:00",
        "insurance_name": "Updated Insurance",
        "insurance_type": "Updated Type",
        "insurance_number": "654321",
    }
    response = await client.put(
        "/api/v1/patient-eoc/00000000-0000-0000-0000-000000000000", json=update_payload
    )
    assert response.status_code == 404
    assert "Patient episode not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_delete_patient_episode_of_care_with_nonexistent_id(client: AsyncClient):
    response = await client.delete("/api/v1/patient-eoc/00000000-0000-0000-0000-000000000000")
    assert response.status_code == 404
    assert "Patient episode not found" in response.json()["detail"]
