"""Entity for Patient Episode of Care Order"""

from datetime import datetime
from uuid import UUID

from src.domain.enums import OrderStatus
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.value_objects.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)


class PatientEpisodeOfCareOrder(EntityBase):
    """Entity for Patient Episode of Care Order"""

    def __init__(
        self,
        patient_episode_of_care_id: UUID,
        order_status: OrderStatus,
        order_date: datetime,
        audit_stamp: AuditStamp,
        order_notes: str | None = None,
        directives: list[PatientEpisodeOfCareOrderDirective] | None = None,
        id: UUID | None = None,
    ):
        """Initialize entity"""
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._patient_episode_of_care_id = patient_episode_of_care_id
        self._order_status = order_status
        self._order_date = order_date
        self._order_notes = order_notes
        self._directives = directives or []

    @property
    def patient_episode_of_care_id(self) -> UUID:
        """Get patient episode of care ID"""
        return self._patient_episode_of_care_id

    @patient_episode_of_care_id.setter
    def patient_episode_of_care_id(self, value: UUID) -> None:
        """Set patient episode of care ID"""
        if not value:
            raise ValueError("Patient episode of care ID is required")
        self._patient_episode_of_care_id = value

    @property
    def order_status(self) -> OrderStatus:
        """Get order status"""
        return self._order_status

    @order_status.setter
    def order_status(self, value: OrderStatus) -> None:
        """Set order status"""
        if not value:
            raise ValueError("Order status is required")
        if not isinstance(value, OrderStatus):
            raise ValueError("Invalid order status")
        self._order_status = value

    @property
    def order_date(self) -> datetime:
        """Get order date"""
        return self._order_date

    @order_date.setter
    def order_date(self, value: datetime) -> None:
        """Set order date"""
        if not value:
            raise ValueError("Order date is required")
        self._order_date = value

    @property
    def order_notes(self) -> str | None:
        """Get order notes"""
        return self._order_notes

    @order_notes.setter
    def order_notes(self, value: str | None) -> None:
        """Set order notes"""
        self._order_notes = value.strip() if value else None

    @property
    def directives(self) -> list[PatientEpisodeOfCareOrderDirective]:
        """Get order directives"""
        return self._directives

    @directives.setter
    def directives(self, value: list[PatientEpisodeOfCareOrderDirective]) -> None:
        """Set order directives"""
        if not isinstance(value, list):
            raise ValueError("Directives must be a list")
        self._directives = value

    def add_directive(self, directive: PatientEpisodeOfCareOrderDirective) -> None:
        """Add a directive to the order"""
        if not isinstance(directive, PatientEpisodeOfCareOrderDirective):
            raise ValueError("Invalid directive type")
        self._directives.append(directive)

    def remove_directive(self, directive: PatientEpisodeOfCareOrderDirective) -> None:
        """Remove a directive from the order"""
        if directive in self._directives:
            self._directives.remove(directive)
