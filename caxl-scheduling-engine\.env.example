# Environment Configuration Example
# Copy this file to .env and update the values

# Application Settings
PROJECT_NAME=appointment-scheduler-service
VERSION=0.1.0
DEBUG=true
ENVIRONMENT=development

# Server Settings
HOST=0.0.0.0
PORT=8080

# API Settings
API_V1_STR=/api/v1

# CORS Settings
CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]
CORS_EXPOSE_HEADERS=["*"]

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=admin
DB_PASSWORD=schedulerDb
DB_NAME=scheduler_dev
DB_ECHO=false

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# Timefold Settings
TIMEFOLD_SOLVER_TIMEOUT=120
TIMEFOLD_LOG_LEVEL=INFO

# Scheduler Settings
SCHEDULER_DAEMON_INTERVAL=3600
SCHEDULER_DAILY_EXECUTION_TIME=02:00

# Security Settings
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Feature Flags
ENABLE_METRICS=true
ENABLE_TRACING=false
ENABLE_CACHING=true
