# Time Matching Edge Cases

**Purpose**: Test various time matching scenarios including exact matches, windowed matches, and timezone handling
**Best for**: Testing temporal constraint handling and flexibility
**Complexity**: Medium

## Features Demonstrated
- Exact time matching requirements
- Windowed time matching with flexibility
- Strict vs flexible timing constraints
- Timezone handling across different regions
- Edge cases with time boundaries

## Data Overview
- **Providers**: 4 (different availability patterns)
- **Patients**: 6 (various time preferences)
- **Appointments**: 8 (different timing requirements)
- **Geographic Coverage**: Multi-timezone (EST, CST, PST)

## Test Dimensions Covered
- **time_matching**: exact_match, windowed_match, flexible_timing, strict_timing, timezone_handling
- **working_hours**: within_hours, outside_hours, break_periods
- **punctuality**: on_time, early_arrival, late_arrival

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/time_matching_edge_cases/* data/

# Run assignment job
python -m appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m appointment_scheduler.jobs.day_plan
```

## Expected Outcomes
- Appointments with exact time requirements should be matched precisely
- Windowed appointments should find optimal slots within flexibility range
- Timezone conflicts should be handled appropriately
- Some appointments may remain unassigned due to strict timing constraints
