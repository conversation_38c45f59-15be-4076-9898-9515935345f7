"""
Bridge module for integrating CareAxl Scheduling Engine with the original scheduler.

This module provides the interface between our scenario-based test data and the 
original scheduler's constraint-driven optimization system.
"""

import os
import yaml
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class SchedulerDataBridge:
    """Bridge for passing scenario data to the original scheduler."""
    
    def __init__(self):
        self.temp_data_dir = None
        self.original_data_dir = None
    
    @contextmanager
    def scenario_data_context(self, scenario_data: Dict[str, Any]):
        """Context manager that temporarily provides scenario data to the original scheduler."""
        
        # Create temporary data directory
        self.temp_data_dir = tempfile.mkdtemp(prefix="caxl_scenario_")
        temp_data_path = Path(self.temp_data_dir)

        # Store original working directory
        original_cwd = os.getcwd()

        try:
            # Write scenario data to temporary YAML files
            self._write_scenario_files(temp_data_path, scenario_data)

            # Change to temp directory so original scheduler finds the data files
            os.chdir(self.temp_data_dir)

            logger.info(f"✅ Created temporary data files in: {self.temp_data_dir}")
            logger.info(f"📁 Changed working directory to: {self.temp_data_dir}")

            yield self.temp_data_dir

        finally:
            # Restore original working directory
            os.chdir(original_cwd)

            # Clean up temporary files
            self._cleanup_temp_files()

            logger.info(f"🧹 Cleaned up temporary data files")
            logger.info(f"📁 Restored working directory to: {original_cwd}")
    
    def _write_scenario_files(self, temp_path: Path, scenario_data: Dict[str, Any]):
        """Write scenario data to YAML files expected by the original scheduler."""
        
        # Create data directory
        data_dir = temp_path / "data"
        data_dir.mkdir(exist_ok=True)
        
        # Write providers.yml
        if "providers" in scenario_data:
            providers_file = data_dir / "providers.yml"
            with open(providers_file, 'w', encoding='utf-8') as f:
                yaml.dump({"providers": scenario_data["providers"]}, f, default_flow_style=False)
            logger.info(f"📝 Created providers.yml with {len(scenario_data['providers'])} providers")
        
        # Write consumers.yml
        if "consumers" in scenario_data:
            consumers_file = data_dir / "consumers.yml"
            with open(consumers_file, 'w', encoding='utf-8') as f:
                yaml.dump({"consumers": scenario_data["consumers"]}, f, default_flow_style=False)
            logger.info(f"📝 Created consumers.yml with {len(scenario_data['consumers'])} consumers")
        
        # Write appointments.yml
        if "appointments" in scenario_data:
            appointments_file = data_dir / "appointments.yml"
            with open(appointments_file, 'w', encoding='utf-8') as f:
                yaml.dump({"appointments": scenario_data["appointments"]}, f, default_flow_style=False)
            logger.info(f"📝 Created appointments.yml with {len(scenario_data['appointments'])} appointments")
        
        # Copy configuration files from the original scheduler
        self._copy_config_files(temp_path)
    
    def _copy_config_files(self, temp_path: Path):
        """Copy configuration files from the original scheduler."""
        
        # Source config directory (original scheduler)
        source_config_dir = Path(__file__).parent.parent.parent.parent.parent / "appointment-scheduler" / "config"
        
        # Target config directory
        target_config_dir = temp_path / "config"
        target_config_dir.mkdir(exist_ok=True)
        
        # Copy essential config files
        config_files = [
            "scheduler.yml",
            "skilled_nursing.yml", 
            "physical_therapy.yml",
            "home_health.yml",
            "logger.yaml"
        ]
        
        for config_file in config_files:
            source_file = source_config_dir / config_file
            target_file = target_config_dir / config_file
            
            if source_file.exists():
                import shutil
                shutil.copy2(source_file, target_file)
                logger.info(f"📋 Copied config file: {config_file}")
            else:
                logger.warning(f"⚠️ Config file not found: {source_file}")
    
    def _cleanup_temp_files(self):
        """Clean up temporary files and directories."""
        if self.temp_data_dir and os.path.exists(self.temp_data_dir):
            import shutil
            shutil.rmtree(self.temp_data_dir)
            self.temp_data_dir = None


def convert_scenario_to_scheduler_format(
    appointments: List[Dict[str, Any]], 
    providers: List[Dict[str, Any]], 
    patients: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Convert CareAxl scenario data to the format expected by the original scheduler."""
    
    # The original scheduler expects the data in the same format as our scenarios
    # So we can pass it through directly, but we might need some field mapping
    
    converted_data = {
        "providers": [],
        "consumers": [],  # Original scheduler uses "consumers" not "patients"
        "appointments": []
    }
    
    # Convert providers with proper format conversion
    for provider in providers:
        converted_provider = provider.copy()

        # Ensure required fields are present
        if "skills" not in converted_provider:
            converted_provider["skills"] = converted_provider.get("required_skills", [])

        # Fix availability format if present
        if "availability" in converted_provider:
            availability = converted_provider["availability"]

            # Convert break_periods from object format to list format
            if "break_periods" in availability:
                break_periods = []
                for period in availability["break_periods"]:
                    if isinstance(period, dict) and "start" in period and "end" in period:
                        # Convert from {"start": "12:00", "end": "13:00"} to ["12:00", "13:00"]
                        break_periods.append([period["start"], period["end"]])
                    elif isinstance(period, list) and len(period) == 2:
                        # Already in correct format
                        break_periods.append(period)
                availability["break_periods"] = break_periods

            # Convert working_hours from object to list if needed
            if "working_hours" in availability and isinstance(availability["working_hours"], dict):
                working_hours = availability["working_hours"]
                if "start" in working_hours and "end" in working_hours:
                    availability["working_hours"] = [working_hours["start"], working_hours["end"]]

        converted_data["providers"].append(converted_provider)
    
    # Convert patients to consumers
    for patient in patients:
        converted_consumer = patient.copy()
        # Map patient fields to consumer fields if needed
        if "patient_id" in converted_consumer:
            converted_consumer["id"] = converted_consumer.pop("patient_id")
        converted_data["consumers"].append(converted_consumer)
    
    # Convert appointments (minimal conversion needed)
    for appointment in appointments:
        converted_appointment = appointment.copy()
        
        # Ensure required fields are present
        if "consumer_id" not in converted_appointment and "patient_id" in converted_appointment:
            converted_appointment["consumer_id"] = converted_appointment["patient_id"]
        
        # Convert datetime objects to strings if needed
        if "appointment_date" in converted_appointment and hasattr(converted_appointment["appointment_date"], "isoformat"):
            converted_appointment["appointment_date"] = converted_appointment["appointment_date"].isoformat()
        
        if "scheduled_start_time" in converted_appointment and hasattr(converted_appointment["scheduled_start_time"], "isoformat"):
            converted_appointment["scheduled_start_time"] = converted_appointment["scheduled_start_time"].isoformat()
        
        if "scheduled_end_time" in converted_appointment and hasattr(converted_appointment["scheduled_end_time"], "isoformat"):
            converted_appointment["scheduled_end_time"] = converted_appointment["scheduled_end_time"].isoformat()
        
        converted_data["appointments"].append(converted_appointment)
    
    logger.info(f"🔄 Converted scenario data: {len(converted_data['providers'])} providers, "
               f"{len(converted_data['consumers'])} consumers, {len(converted_data['appointments'])} appointments")
    
    return converted_data


# Global bridge instance
scheduler_bridge = SchedulerDataBridge()
