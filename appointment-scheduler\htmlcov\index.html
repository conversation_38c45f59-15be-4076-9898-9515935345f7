<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f___init___py.html">src\appointment_scheduler\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4___init___py.html">src\appointment_scheduler\api\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_app_py.html">src\appointment_scheduler\api\app.py</a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_models_py.html">src\appointment_scheduler\api\models.py</a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6572ad792a708ab4_routes_py.html">src\appointment_scheduler\api\routes.py</a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_config_manager_py.html">src\appointment_scheduler\config_manager.py</a></td>
                <td>128</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="22 128">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd___init___py.html">src\appointment_scheduler\constraints\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_assignment_constraints_py.html">src\appointment_scheduler\constraints\assignment_constraints.py</a></td>
                <td>31</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="13 31">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_base_constraints_py.html">src\appointment_scheduler\constraints\base_constraints.py</a></td>
                <td>246</td>
                <td>215</td>
                <td>0</td>
                <td class="right" data-ratio="31 246">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html">src\appointment_scheduler\constraints\c001_asgn_provider_skill_validation.py</a></td>
                <td>43</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="7 43">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html">src\appointment_scheduler\constraints\c002_asgn_date_based_availability.py</a></td>
                <td>61</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="13 61">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html">src\appointment_scheduler\constraints\c003_asgn_geographic_service_area.py</a></td>
                <td>150</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="21 150">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html">src\appointment_scheduler\constraints\c004_asgn_time_based_availability.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html">src\appointment_scheduler\constraints\c004_asgn_timed_visit_date_assignment.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html">src\appointment_scheduler\constraints\c005_asgn_workload_balance_optimization.py</a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html">src\appointment_scheduler\constraints\c006_asgn_geographic_clustering_optimization.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html">src\appointment_scheduler\constraints\c007_asgn_patient_preference_matching.py</a></td>
                <td>67</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="12 67">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html">src\appointment_scheduler\constraints\c008_asgn_provider_capacity_management.py</a></td>
                <td>66</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="14 66">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html">src\appointment_scheduler\constraints\c009_asgn_continuity_of_care_optimization.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html">src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py</a></td>
                <td>9</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="4 9">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html">src\appointment_scheduler\constraints\c011_schd_appointment_overlap_prevention.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html">src\appointment_scheduler\constraints\c012_schd_flexible_appointment_timing_optimization.py</a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html">src\appointment_scheduler\constraints\c013_schd_healthcare_task_sequencing.py</a></td>
                <td>127</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="14 127">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html">src\appointment_scheduler\constraints\c014_schd_route_travel_time_optimization.py</a></td>
                <td>16</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="5 16">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html">src\appointment_scheduler\constraints\c015_schd_timed_appointment_pinning.py</a></td>
                <td>15</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="4 15">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html">src\appointment_scheduler\constraints\c016_schd_route_optimization.py</a></td>
                <td>78</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="20 78">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f5dfb1d826f8cdcd_day_constraints_py.html">src\appointment_scheduler\constraints\day_constraints.py</a></td>
                <td>28</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="11 28">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_data_loader_py.html">src\appointment_scheduler\data_loader.py</a></td>
                <td>257</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="144 257">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_domain_py.html">src\appointment_scheduler\domain.py</a></td>
                <td>405</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="283 405">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7___init___py.html">src\appointment_scheduler\jobs\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_assign_appointments_py.html">src\appointment_scheduler\jobs\assign_appointments.py</a></td>
                <td>291</td>
                <td>255</td>
                <td>0</td>
                <td class="right" data-ratio="36 291">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c9ee4c2c0763ed7_day_plan_py.html">src\appointment_scheduler\jobs\day_plan.py</a></td>
                <td>275</td>
                <td>244</td>
                <td>0</td>
                <td class="right" data-ratio="31 275">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_logging_config_py.html">src\appointment_scheduler\logging_config.py</a></td>
                <td>53</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="46 53">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_planning_models_py.html">src\appointment_scheduler\planning_models.py</a></td>
                <td>69</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="52 69">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a3aadb4c065132f_scheduler_py.html">src\appointment_scheduler\scheduler.py</a></td>
                <td>118</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="25 118">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b___init___py.html">src\appointment_scheduler\utils\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_scenario_validator_py.html">src\appointment_scheduler\utils\scenario_validator.py</a></td>
                <td>176</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="120 176">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4681ee4fa776c52b_test_helpers_py.html">src\appointment_scheduler\utils\test_helpers.py</a></td>
                <td>101</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="69 101">68%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>3097</td>
                <td>2062</td>
                <td>0</td>
                <td class="right" data-ratio="1035 3097">33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_4681ee4fa776c52b_test_helpers_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_2a3aadb4c065132f___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
