"""Patient DTOs for application layer"""

from datetime import date, datetime
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field


class BasicPersonalInfoDTO(BaseModel):
    """Basic personal information DTO"""

    first_name: str
    last_name: str
    gender: str | None = None
    dob: date | None = None


class PersonalInfoDTO(BaseModel):
    """Personal information DTO"""

    basic_info: BasicPersonalInfoDTO
    ssn: str | None = None
    ethnicity: str | None = None
    race: str | None = None


class EmailInfoDTO(BaseModel):
    id: UUID | None = None
    email: str
    email_type: str | None = None
    is_verified: bool = False


class PhoneTypeDTO(str, Enum):
    HOME = "home"
    MOBILE = "mobile"
    WORK = "work"
    EMERGENCY = "911"


class PhoneInfoDTO(BaseModel):
    id: UUID | None = None
    phone_number: str
    phone_type: PhoneTypeDTO | None = None
    phone_country_code: str | None = None
    is_primary: bool = False
    is_verified: bool = False
    preferred_for_sms: bool = False


class LocationNameDTO(str, Enum):
    HOME = "home"
    WORK = "work"
    FACILITY = "facility"
    FAMILY = "family"


class LocationInfoDTO(BaseModel):
    id: UUID | None = None
    location_name: LocationNameDTO
    location_id: UUID
    is_primary: bool = False


class EmergencyContactRelationshipDTO(str, Enum):
    SPOUSE = "spouse"
    DAUGHTER = "daughter"
    SON = "son"
    FRIEND = "friend"
    PARENT = "parent"
    SIBLING = "sibling"
    OTHER = "other"


class EmergencyContactDTO(BaseModel):
    id: UUID | None = None
    relationship: EmergencyContactRelationshipDTO
    first_name: str
    last_name: str
    phone_number: str
    phone_country_code: str
    email: str


class ContactInfoDTO(BaseModel):
    emails: list[EmailInfoDTO]
    phones: list[PhoneInfoDTO]
    locations: list[LocationInfoDTO]
    emergency_contacts: list[EmergencyContactDTO]


class PatientPIIDTO(BaseModel):
    """Patient PII DTO"""

    id: UUID | None = None
    personal_info: PersonalInfoDTO
    contact_info: ContactInfoDTO


class LabPreferenceDTO(BaseModel):
    """Lab preference DTO"""

    name: str
    location_id: UUID
    phone: str | None = None
    phone_country_code: str | None = None
    email: str | None = None


class PharmacyPreferenceDTO(BaseModel):
    """Pharmacy preference DTO"""

    name: str
    location_id: UUID
    phone: str | None = None
    email: str | None = None


class PatientPreferencesDTO(BaseModel):
    """Patient preferences DTO"""

    id: UUID | None = None
    pref_language: str | None = None
    lab_preference: LabPreferenceDTO | None = None
    pharmacy_preference: PharmacyPreferenceDTO | None = None
    time_pref: str | None = None


class InsuranceDTO(BaseModel):
    """Insurance DTO"""

    name: str
    type: str
    number: str


class MedicalProfileDTO(BaseModel):
    """Medical profile DTO"""

    id: UUID | None = None
    insurance: InsuranceDTO
    clinical_diagnosis: str | None = None
    medical_history: str | None = None
    social_history: str | None = None
    allergies: str | None = None
    notes: str | None = None


class CaregiverDTO(BaseModel):
    """Caregiver DTO"""

    id: UUID | None = None
    patient_id: UUID
    email: str
    email_verified: bool
    phone_number: str
    phone_country_code: str
    is_phone_verified: bool
    personal_info: BasicPersonalInfoDTO
    location_id: UUID
    relationship: str


class ReferringMRNDTO(BaseModel):
    """Referring MRN DTO"""

    id: UUID | None = None
    referring_mrn: str
    referring_name: str | None = None
    referring_state: str | None = None
    referring_hospital: str | None = None
    referring_npi: str | None = None
    inpatient_discharge_date: datetime | None = None


class PatientDTO(BaseModel):
    """Patient response DTO"""

    id: UUID | None = None
    pii: PatientPIIDTO | None = None
    preferences: PatientPreferencesDTO | None = None
    profile: MedicalProfileDTO | None = None
    caregivers: list[CaregiverDTO] = Field(default_factory=list)
    referring_mrns: list[ReferringMRNDTO] = Field(default_factory=list)
    mpu_id: str | None = None
    nhid: str | None = None


class PatientListResponse(BaseModel):
    """Patient list response DTO"""

    patients: list[PatientDTO]
    page: int
    page_size: int
    total_records: int
    total_pages: int
