from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientPIIEmail(BaseModel):
    __tablename__ = "patient_pii_email"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_pii_email_id", UUID(as_uuid=True), primary_key=True)
    patient_pii_id = Column(UUID(as_uuid=True), ForeignKey("patient_pii.patient_pii_id"))
    email = Column(String)
    email_type = Column(String)
    is_verified = Column(Boolean)
    patient_pii = relationship("PatientPII", back_populates="emails", lazy="joined")
