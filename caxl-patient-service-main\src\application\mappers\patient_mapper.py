"""Patient mapper for application layer"""

from src.application.dtos.patient import (
    BasicPersonalInfoDTO,
    CaregiverDTO,
    ContactInfoDTO,
    EmailInfoDTO,
    EmergencyContactDTO,
    InsuranceDTO,
    LabPreferenceDTO,
    LocationInfoDTO,
    LocationNameDTO,
    MedicalProfileDTO,
    PatientDTO,
    PatientListResponse,
    PatientPIIDTO,
    PatientPreferencesDTO,
    PersonalInfoDTO,
    PharmacyPreferenceDTO,
    PhoneInfoDTO,
    ReferringMRNDTO,
)
from src.application.mappers.base_mapper import BaseMapper
from src.domain.aggregates.patient import Patient
from src.domain.entities.caregiver import Caregiver
from src.domain.entities.email import EmailInfo
from src.domain.entities.emergency_contact import EmergencyContact
from src.domain.entities.location import LocationInfo
from src.domain.entities.medical_profile import MedicalProfile
from src.domain.entities.patient_pii import PatientPII
from src.domain.entities.patient_preferences import PatientPreferences
from src.domain.entities.phone import PhoneInfo
from src.domain.entities.referring_info import ReferringInformation
from src.domain.enums import (
    CaregiverRelationship,
    EmergencyContactRelationship,
    Gender,
    InsuranceType,
    LocationName,
    PhoneType,
)
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.value_objects.insurance import Insurance
from src.domain.value_objects.lab_preference import LabPreference
from src.domain.value_objects.patient_contact_info import PatientContactInfo
from src.domain.value_objects.personal_info import BasicPersonalInfo, PersonalInfo
from src.domain.value_objects.pharmacy_preference import PharmacyPreference


class PatientMapper(BaseMapper[Patient, PatientDTO, PatientListResponse]):
    """Mapper for converting between patient domain entities and DTOs"""

    def to_response(self, entity: Patient | None) -> PatientDTO | None:
        """Convert domain entity to response DTO"""
        if not entity:
            return None

        # Map PII
        pii_dto = self.map_pii_to_dto(entity.pii)

        # Map preferences
        preferences_dto = (
            self.map_preferences_to_dto(entity.preferences) if entity.preferences else None
        )

        # Map profile
        profile_dto = self.map_medical_profile_to_dto(entity.profile) if entity.profile else None

        # Map referring MRNs
        referring_mrns_dto = [self.map_referring_mrn_to_dto(mrn) for mrn in entity.referring_mrns]

        return PatientDTO(
            id=entity.id,
            pii=pii_dto,
            preferences=preferences_dto,
            profile=profile_dto,
            referring_mrns=referring_mrns_dto,
            mpu_id=entity.mpu_id,
            nhid=entity.nhid,
        )

    def to_list_response(
        self, entities: list[Patient], total: int, page: int, page_size: int
    ) -> PatientListResponse:
        """Convert list of domain entities to list response DTO"""
        return PatientListResponse(
            patients=self.to_response_list(entities),
            page=page,
            page_size=page_size,
            total_records=total,
            total_pages=(total + page_size - 1) // page_size,
        )

    def to_domain(
        self, dto: PatientDTO | None, audit_stamp: AuditStamp | None = None
    ) -> Patient | None:
        """Convert DTO to domain entity"""
        if not dto:
            return None

        pii = self.map_pii_to_domain(dto=dto.pii, audit_stamp=audit_stamp)

        preferences = (
            self.map_preferences_to_domain(dto=dto.preferences, audit_stamp=audit_stamp)
            if dto.preferences
            else None
        )

        profile = (
            self.map_medical_profile_to_domain(dto=dto.profile, audit_stamp=audit_stamp)
            if dto.profile
            else None
        )

        caregivers = self.map_caregivers_to_domain(dto=dto.caregivers, audit_stamp=audit_stamp)

        referring_mrns = self.map_referring_mrns_to_domain(
            dto=dto.referring_mrns, audit_stamp=audit_stamp
        )

        return Patient(
            id=dto.id,
            audit_stamp=audit_stamp,
            pii=pii,
            preferences=preferences,
            profile=profile,
            caregivers=caregivers,
            referring_mrns=referring_mrns,
            mpu_id=dto.mpu_id,
            nhid=dto.nhid,
        )

    def map_basic_personal_info_to_dto(self, vo: BasicPersonalInfo) -> BasicPersonalInfoDTO:
        """Map basic personal info value object to DTO"""
        return BasicPersonalInfoDTO(
            first_name=vo.first_name,
            last_name=vo.last_name,
            gender=vo.gender.value if vo.gender else None,
            dob=vo.dob,
        )

    def map_personal_info_to_dto(self, vo: PersonalInfo) -> PersonalInfoDTO:
        """Map personal info value object to DTO"""
        return PersonalInfoDTO(
            basic_info=self.map_basic_personal_info_to_dto(vo.basic_info),
            ssn=vo.ssn,
            ethnicity=vo.ethnicity,
            race=vo.race,
        )

    def map_contact_info_to_dto(self, vo: PatientContactInfo):
        return ContactInfoDTO(
            emergency_contacts=[
                self.map_emergency_contact_to_dto(contact) for contact in vo.emergency_contacts
            ],
            phones=[self.map_phone_to_dto(phone=phone) for phone in vo.phones],
            emails=[self.map_email_to_dto(email=email) for email in vo.emails],
            locations=[self.map_location_to_dto(location=location) for location in vo.locations],
        )

    def map_pii_to_dto(self, vo: PatientPII) -> PatientPIIDTO:
        """Map PII value object to DTO"""
        return PatientPIIDTO(
            id=vo.id,
            personal_info=self.map_personal_info_to_dto(vo.personal_info),
            contact_info=self.map_contact_info_to_dto(vo.contact_info),
        )

    def map_lab_preference_to_dto(self, vo: LabPreference) -> LabPreferenceDTO:
        """Map lab preference value object to DTO"""
        return LabPreferenceDTO(
            name=vo.name,
            location_id=vo.location_id if vo.location_id else None,
            phone=vo.phone,
            phone_country_code=vo.phone_country_code,
            email=vo.email,
        )

    def map_pharmacy_preference_to_dto(self, vo: PharmacyPreference) -> PharmacyPreferenceDTO:
        """Map pharmacy preference value object to DTO"""
        return PharmacyPreferenceDTO(
            name=vo.name,
            location_id=vo.location_id if vo.location_id else None,
            phone=vo.phone,
            email=vo.email,
        )

    def map_preferences_to_dto(self, vo: PatientPreferences) -> PatientPreferencesDTO:
        """Map preferences value object to DTO"""
        return PatientPreferencesDTO(
            id=vo.id,
            pref_language=vo.pref_language,
            lab_preference=(
                self.map_lab_preference_to_dto(vo.lab_preference) if vo.lab_preference else None
            ),
            pharmacy_preference=(
                self.map_pharmacy_preference_to_dto(vo.pharmacy_preference)
                if vo.pharmacy_preference
                else None
            ),
            time_pref=vo.time_pref,
        )

    def map_medical_profile_to_dto(self, vo: MedicalProfile) -> MedicalProfileDTO:
        """Map medical profile value object to DTO"""
        return MedicalProfileDTO(
            id=vo.id,
            insurance=self.map_insurance_to_dto(vo.insurance) if vo.insurance else None,
            clinical_diagnosis=vo.clinical_diagnosis,
            medical_history=vo.medical_history,
            social_history=vo.social_history,
            allergies=vo.allergies,
            notes=vo.notes,
        )

    def map_caregiver_to_dto(self, vo: Caregiver) -> CaregiverDTO:
        """Map caregiver value object to DTO"""
        return CaregiverDTO(
            id=vo.id,
            personal_info=self.map_basic_personal_info_to_dto(vo.personal_info),
            relationship=vo.relationship if vo.relationship else None,
            patient_id=vo.patient_id,
            email=vo.email,
            email_verified=vo.email_verified,
            phone_country_code=vo.phone_country_code,
            phone_number=vo.phone_number,
            is_phone_verified=vo.is_phone_verified,
            location_id=vo.location_id,
        )

    def map_referring_mrn_to_dto(self, vo: ReferringInformation) -> ReferringMRNDTO:
        """Map referring MRN value object to DTO"""
        return ReferringMRNDTO(
            id=vo.id,
            referring_mrn=vo.referring_mrn,
            referring_name=vo.referring_name,
            referring_state=vo.referring_state,
            referring_hospital=vo.referring_hospital,
            referring_npi=vo.referring_npi,
            inpatient_discharge_date=vo.inpatient_discharge_date,
        )

    def map_basic_personal_info_to_domain(self, dto: BasicPersonalInfoDTO) -> BasicPersonalInfo:
        """Map basic personal info DTO to domain value object"""
        return BasicPersonalInfo(
            first_name=dto.first_name,
            last_name=dto.last_name,
            gender=Gender(dto.gender) if dto.gender else None,
            dob=dto.dob,
        )

    def map_personal_info_to_domain(self, dto: PersonalInfoDTO) -> PersonalInfo:
        """Map personal info DTO to domain value object"""
        return PersonalInfo(
            basic_info=self.map_basic_personal_info_to_domain(dto.basic_info),
            ssn=dto.ssn,
            ethnicity=dto.ethnicity,
            race=dto.race,
        )

    def map_contact_info_to_domain(
        self, dto: ContactInfoDTO, audit_stamp: AuditStamp
    ) -> PatientContactInfo:
        """Map contact info DTO to domain value object"""
        return PatientContactInfo(
            emergency_contacts=[
                self.map_emergency_contact_to_domain(contact=contact, audit_stamp=audit_stamp)
                for contact in dto.emergency_contacts
            ],
            phones=[
                self.map_phone_to_domain(phone=phone, audit_stamp=audit_stamp)
                for phone in dto.phones
            ],
            emails=[
                self.map_email_to_domain(email=email, audit_stamp=audit_stamp)
                for email in dto.emails
            ],
            locations=[
                self.map_location_to_domain(location=location, audit_stamp=audit_stamp)
                for location in dto.locations
            ],
        )

    def map_pii_to_domain(
        self, dto: PatientPIIDTO, audit_stamp: AuditStamp | None = None
    ) -> PatientPII:
        """Map PII DTO to domain value object"""
        return PatientPII(
            id=dto.id,
            audit_stamp=audit_stamp,
            personal_info=self.map_personal_info_to_domain(dto=dto.personal_info),
            contact_info=self.map_contact_info_to_domain(
                dto=dto.contact_info, audit_stamp=audit_stamp
            ),
        )

    def map_lab_preference_to_domain(self, dto: LabPreferenceDTO) -> LabPreference:
        """Map lab preference DTO to domain value object"""
        return LabPreference(
            name=dto.name,
            location_id=dto.location_id,
            phone=dto.phone,
            phone_country_code=dto.phone_country_code,
            email=dto.email,
        )

    def map_pharmacy_preference_to_domain(self, dto: PharmacyPreferenceDTO) -> PharmacyPreference:
        """Map pharmacy preference DTO to domain value object"""
        return PharmacyPreference(
            name=dto.name, location_id=dto.location_id, phone=dto.phone, email=dto.email
        )

    def map_preferences_to_domain(
        self, dto: PatientPreferencesDTO, audit_stamp: AuditStamp | None = None
    ) -> PatientPreferences:
        """Map preferences DTO to domain value object"""
        return PatientPreferences(
            id=dto.id,
            audit_stamp=audit_stamp,
            pref_language=dto.pref_language,
            lab_preference=(
                self.map_lab_preference_to_domain(dto.lab_preference)
                if dto.lab_preference
                else None
            ),
            pharmacy_preference=(
                self.map_pharmacy_preference_to_domain(dto.pharmacy_preference)
                if dto.pharmacy_preference
                else None
            ),
            time_pref=dto.time_pref,
        )

    def map_medical_profile_to_domain(
        self, dto: MedicalProfileDTO, audit_stamp: AuditStamp | None = None
    ) -> MedicalProfile:
        """Map medical profile DTO to domain value object"""
        return MedicalProfile(
            id=dto.id,
            audit_stamp=audit_stamp,
            insurance=self.map_insurance_to_domain(dto.insurance) if dto.insurance else None,
            clinical_diagnosis=dto.clinical_diagnosis,
            medical_history=dto.medical_history,
            social_history=dto.social_history,
            allergies=dto.allergies,
            notes=dto.notes,
        )

    def map_referring_mrn_to_domain(
        self, dto: ReferringMRNDTO, audit_stamp: AuditStamp | None = None
    ) -> ReferringInformation:
        """Map referring MRN DTO to domain value object"""
        return ReferringInformation(
            id=dto.id,
            audit_stamp=audit_stamp,
            referring_mrn=dto.referring_mrn,
            referring_name=dto.referring_name,
            referring_state=dto.referring_state,
            referring_hospital=dto.referring_hospital,
            referring_npi=dto.referring_npi,
            inpatient_discharge_date=dto.inpatient_discharge_date.date(),
        )

    def map_caregivers_to_domain(
        self, dto: list[CaregiverDTO], audit_stamp: AuditStamp | None = None
    ):
        return [
            self.map_caregiver_to_domain(dto=caregiver, audit_stamp=audit_stamp)
            for caregiver in dto
        ]

    def map_referring_mrns_to_domain(
        self, dto: list[ReferringMRNDTO], audit_stamp: AuditStamp | None = None
    ):
        return [self.map_referring_mrn_to_domain(dto=mrn, audit_stamp=audit_stamp) for mrn in dto]

    def map_insurance_to_domain(self, dto: InsuranceDTO) -> Insurance:
        """Map insurance DTO to domain value object"""
        return Insurance(
            name=dto.name,
            type=InsuranceType(dto.type).value,
            number=dto.number,
        )

    def map_caregiver_to_domain(
        self, dto: CaregiverDTO, audit_stamp: AuditStamp | None = None
    ) -> Caregiver:
        """Map caregiver DTO to domain value object"""
        return Caregiver(
            id=dto.id,
            audit_stamp=audit_stamp,
            relationship=CaregiverRelationship(dto.relationship) if dto.relationship else None,
            personal_info=self.map_basic_personal_info_to_domain(dto.personal_info),
            phone_number=dto.phone_number,
            phone_country_code=dto.phone_country_code,
            is_phone_verified=dto.is_phone_verified,
            email=dto.email,
            email_verified=dto.email_verified,
            location_id=dto.location_id,
            patient_id=dto.patient_id,
        )

    def map_phone_to_dto(self, phone: PhoneInfo) -> PhoneInfoDTO:
        """Map phone info to DTO"""
        return PhoneInfoDTO(
            id=phone.id,
            phone_number=phone.phone_number,
            phone_country_code=phone.phone_country_code,
            phone_type=phone.phone_type.value if phone.phone_type else None,
            is_primary=phone.is_primary,
            is_verified=phone.is_verified,
            preferred_for_sms=phone.preferred_for_sms,
        )

    def map_email_to_dto(self, email: EmailInfo) -> EmailInfoDTO:
        """Map email info to DTO"""
        return EmailInfoDTO(
            id=email.id,
            email=email.email,
            email_type=email.email_type,
            is_verified=email.is_verified,
        )

    def map_location_to_dto(self, location: LocationInfo) -> LocationInfoDTO:
        """Map location info to DTO"""
        return LocationInfoDTO(
            id=location.id,
            location_id=location.location_id,
            location_name=(
                LocationNameDTO(location.location_name.value) if location.location_name else None
            ),
            is_primary=location.is_primary,
        )

    def map_emergency_contact_to_dto(self, contact: EmergencyContact) -> EmergencyContactDTO:
        """Map emergency contact to DTO"""
        return EmergencyContactDTO(
            id=contact.id,
            first_name=contact.first_name,
            last_name=contact.last_name,
            relationship=contact.relationship.value if contact.relationship else None,
            phone_number=contact.phone_number,
            phone_country_code=contact.phone_country_code,
            email=contact.email,
        )

    def map_phone_to_domain(self, phone: PhoneInfoDTO, audit_stamp: AuditStamp) -> PhoneInfo:
        """Map phone info DTO to domain"""
        return PhoneInfo(
            id=phone.id,
            audit_stamp=audit_stamp,
            phone_number=phone.phone_number,
            phone_country_code=phone.phone_country_code,
            phone_type=PhoneType(phone.phone_type) if phone.phone_type else None,
            is_primary=phone.is_primary,
            is_verified=phone.is_verified,
            preferred_for_sms=phone.preferred_for_sms,
        )

    def map_email_to_domain(self, email: EmailInfoDTO, audit_stamp: AuditStamp) -> EmailInfo:
        """Map email info DTO to domain"""
        return EmailInfo(
            id=email.id,
            audit_stamp=audit_stamp,
            email=email.email,
            email_type=email.email_type,
            is_verified=email.is_verified,
        )

    def map_location_to_domain(
        self, location: LocationInfoDTO, audit_stamp: AuditStamp
    ) -> LocationInfo:
        """Map location info DTO to domain"""
        return LocationInfo(
            id=location.id,
            audit_stamp=audit_stamp,
            location_id=location.location_id,
            location_name=LocationName(location.location_name) if location.location_name else None,
            is_primary=location.is_primary,
        )

    def map_emergency_contact_to_domain(
        self, contact: EmergencyContactDTO, audit_stamp: AuditStamp
    ) -> EmergencyContact:
        """Map emergency contact DTO to domain"""
        return EmergencyContact(
            id=contact.id,
            audit_stamp=audit_stamp,
            first_name=contact.first_name,
            last_name=contact.last_name,
            relationship=(
                EmergencyContactRelationship(contact.relationship) if contact.relationship else None
            ),
            phone_number=contact.phone_number,
            phone_country_code=contact.phone_country_code,
            email=contact.email,
        )

    def map_insurance_to_dto(self, vo: Insurance) -> InsuranceDTO:
        """Map insurance value object to DTO"""
        return InsuranceDTO(
            name=vo.name,
            type=vo.type.value,
            number=vo.number,
        )
