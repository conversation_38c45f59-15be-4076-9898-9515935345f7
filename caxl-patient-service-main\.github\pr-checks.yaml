name: 'Patient Service - PR Checks'

on:
  pull_request:
    branches:
      - main
    paths:
      - '.github/workflows/**'
      - 'docker-compose.yml'
      - 'Dockerfile'
      - 'Dockerfile.dev'
      - 'pyproject.toml'
      - 'src/**/*'
      - 'tests/**/*'
      - 'setup.py'
      - 'Makefile'
      - 'openapi.json'

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: '.'
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install .[dev]

      - name: Run Black formatting check
        run: |
          black --check src tests

      - name: Run Ruff linting
        run: |
          ruff check src tests

      - name: Run Security Checks with Bandit
        run: |
          pip install bandit
          bandit --ini=.bandit.ini -r src tests

      - name: Run Unit Tests with Coverage
        run: |
          pytest --cov=src --cov-report=xml --cov-report=term-missing tests/ -v

      - name: Generate OpenAPI Documentation
        run: |
          uvicorn src.main:app --reload --port 8000 --host 0.0.0.0 &
          sleep 5
          curl -o openapi.json http://localhost:8000/openapi.json
          kill %1
