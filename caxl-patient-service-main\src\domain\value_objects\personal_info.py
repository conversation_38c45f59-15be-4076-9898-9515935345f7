from dataclasses import dataclass
from datetime import date
from typing import Optional

from src.domain.enums import Gender
from src.domain.foundations.base.value_object_base import ValueObjectBase


@dataclass(frozen=True)
class BasicPersonalInfo(ValueObjectBase):
    """Basic personal information value object"""
    first_name: str
    last_name: str
    gender: Optional[Gender] = None
    dob: Optional[date] = None

    def __post_init__(self):
        if not self.first_name or not self.first_name.strip():
            raise ValueError("First name cannot be empty")
        if not self.last_name or not self.last_name.strip():
            raise ValueError("Last name cannot be empty")


@dataclass(frozen=True)
class PersonalInfo(ValueObjectBase):
    """Patient personal information value object"""
    basic_info: BasicPersonalInfo
    ssn: Optional[str] = None
    ethnicity: Optional[str] = None
    race: Optional[str] = None

    def __post_init__(self):
        if not self.basic_info:
            raise ValueError("Basic info is required")
        if self.ssn and not self._is_valid_ssn(self.ssn):
            raise ValueError(f"Invalid SSN format: {self.ssn}")

    @staticmethod
    def _is_valid_ssn(ssn: str) -> bool:
        # Remove any non-digit characters
        digits = ''.join(filter(str.isdigit, ssn))
        if len(digits) != 9:
            return False
        # Check for invalid SSN patterns
        invalid_patterns = [
            '000000000', '111111111', '222222222', '333333333',
            '444444444', '555555555', '666666666', '777777777',
            '888888888', '999999999', '000000000'
        ]
        return digits not in invalid_patterns
