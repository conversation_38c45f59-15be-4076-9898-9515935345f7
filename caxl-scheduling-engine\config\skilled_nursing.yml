service_type: skilled_nursing
required_skills:
  - "Registered Nurse (RN)"
  - "Skilled Nursing Care"
  - "Wound Care"
  - "Medication Management"
  - "Patient Assessment"
  - "wound_care"
  - "medication_administration"
  - "patient_assessment"
  - "vital_signs"
  - "chronic_care_management"

geographic_radius_miles: 30.0
max_daily_appointments_per_provider: 6
max_weekly_hours_per_provider: 40

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.9
workload_balance_weight: 0.7
geographic_clustering_weight: 0.6
patient_preference_weight: 0.8
capacity_threshold_percentage: 0.85

# Service-specific settings
visit_duration_minutes: 60
requires_initial_assessment: true
allows_weekend_visits: false
emergency_response_time_hours: 4

# Geographic clustering settings
cluster_radius_miles: 15.0
max_cluster_size: 8
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 100
continuity_threshold_days: 30  # Days to consider for continuity
existing_relationship_bonus: 50

# Workload balancing settings
target_daily_appointments: 5
workload_variance_tolerance: 2
overtime_penalty_multiplier: 1.5

# Patient preference settings
preferred_provider_bonus: 30
preferred_time_bonus: 20
preferred_location_bonus: 25

# Skill matching settings
strict_skill_matching: true
allow_skill_hierarchy: true
skill_mismatch_penalty: 200

# Time constraints
min_visit_duration_minutes: 30
max_visit_duration_minutes: 120
travel_buffer_minutes: 15
setup_time_minutes: 5

# Quality metrics
quality_score_weight: 0.3
patient_satisfaction_weight: 0.4
provider_efficiency_weight: 0.3
