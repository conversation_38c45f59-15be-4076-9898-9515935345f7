"""Base mapper class for transforming between domain entities and persistence models"""

from abc import ABC, abstractmethod
from typing import Generic, TypeVar

# Define type variables for domain and model types
DomainT = TypeVar("DomainT")
ModelT = TypeVar("ModelT")


class BaseMapper(Generic[DomainT, ModelT], ABC):
    """Base mapper class with generic type parameters for domain and model types"""

    @abstractmethod
    def to_domain(self, model: ModelT) -> DomainT:
        """
        Convert a persistence model to a domain entity

        Args:
            model: The persistence model to convert

        Returns:
            The converted domain entity
        """
        pass

    @abstractmethod
    def to_model(self, domain: DomainT) -> ModelT:
        """
        Convert a domain entity to a persistence model

        Args:
            domain: The domain entity to convert
            tenant_id: The tenant ID for the model

        Returns:
            The converted persistence model
        """
        pass

    def to_domain_list(self, models: list[ModelT]) -> list[DomainT]:
        """
        Convert a list of persistence models to domain entities

        Args:
            models: List of persistence models to convert

        Returns:
            List of converted domain entities
        """
        return [self.to_domain(model) for model in models]

    def to_model_list(self, domains: list[DomainT], tenant_id: str) -> list[ModelT]:
        """
        Convert a list of domain entities to persistence models

        Args:
            domains: List of domain entities to convert
            tenant_id: The tenant ID for the models

        Returns:
            List of converted persistence models
        """
        return [self.to_model(domain, tenant_id) for domain in domains]

    def to_domain_optional(self, model: ModelT | None) -> DomainT | None:
        """
        Convert an optional persistence model to an optional domain entity

        Args:
            model: Optional persistence model to convert

        Returns:
            Optional converted domain entity
        """
        return self.to_domain(model) if model is not None else None

    def to_model_optional(self, domain: DomainT | None, tenant_id: str) -> ModelT | None:
        """
        Convert an optional domain entity to an optional persistence model

        Args:
            domain: Optional domain entity to convert
            tenant_id: The tenant ID for the model

        Returns:
            Optional converted persistence model
        """
        return self.to_model(domain, tenant_id) if domain is not None else None
