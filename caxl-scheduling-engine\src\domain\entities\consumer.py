"""Consumer (Patient) domain entity"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import dataclass, field

from src.domain.value_objects.location import Location
from src.domain.value_objects.preferences import ConsumerPreferences
from src.domain.enums.consumer_status import ConsumerStatus


@dataclass
class Consumer:
    """Consumer (Patient) domain entity"""
    
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    
    # Location
    location: Optional[Location] = None
    
    # Care information
    care_episode_id: Optional[UUID] = None
    medical_record_number: Optional[str] = None
    
    # Preferences
    preferences: Optional[ConsumerPreferences] = None
    
    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    emergency_contact: Optional[str] = None
    
    # Status
    status: ConsumerStatus = ConsumerStatus.ACTIVE
    
    # Insurance and billing
    insurance_provider: Optional[str] = None
    insurance_id: Optional[str] = None
    
    # Special needs and notes
    special_instructions: Optional[str] = None
    accessibility_needs: List[str] = field(default_factory=list)
    language_preference: str = "English"
    
    # Audit fields
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization validation"""
        if not self.name:
            raise ValueError("Consumer name is required")
    
    def update_location(self, location: Location) -> None:
        """Update consumer location"""
        self.location = location
        self.updated_at = datetime.utcnow()
    
    def update_preferences(self, preferences: ConsumerPreferences) -> None:
        """Update consumer preferences"""
        self.preferences = preferences
        self.updated_at = datetime.utcnow()
    
    def add_accessibility_need(self, need: str) -> None:
        """Add accessibility need"""
        if need not in self.accessibility_needs:
            self.accessibility_needs.append(need)
            self.updated_at = datetime.utcnow()
    
    def remove_accessibility_need(self, need: str) -> None:
        """Remove accessibility need"""
        if need in self.accessibility_needs:
            self.accessibility_needs.remove(need)
            self.updated_at = datetime.utcnow()
    
    def activate(self) -> None:
        """Activate consumer"""
        self.status = ConsumerStatus.ACTIVE
        self.updated_at = datetime.utcnow()
    
    def deactivate(self) -> None:
        """Deactivate consumer"""
        self.status = ConsumerStatus.INACTIVE
        self.updated_at = datetime.utcnow()
    
    def discharge(self) -> None:
        """Discharge consumer from care"""
        self.status = ConsumerStatus.DISCHARGED
        self.updated_at = datetime.utcnow()
    
    def get_preferred_providers(self) -> List[UUID]:
        """Get list of preferred provider IDs"""
        if not self.preferences:
            return []
        return self.preferences.preferred_provider_ids
    
    def get_preferred_days(self) -> List[str]:
        """Get preferred days of week"""
        if not self.preferences:
            return []
        return self.preferences.preferred_days
    
    def get_preferred_times(self) -> Optional[tuple[str, str]]:
        """Get preferred time range"""
        if not self.preferences:
            return None
        return self.preferences.preferred_time_range
    
    def requires_cultural_considerations(self) -> bool:
        """Check if consumer has cultural considerations"""
        if not self.preferences:
            return False
        return bool(self.preferences.cultural_considerations)
    
    @property
    def is_active(self) -> bool:
        """Check if consumer is active"""
        return self.status == ConsumerStatus.ACTIVE
    
    @property
    def has_location(self) -> bool:
        """Check if consumer has location set"""
        return self.location is not None
    
    @property
    def display_name(self) -> str:
        """Get display name"""
        return self.name
