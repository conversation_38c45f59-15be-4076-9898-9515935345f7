"""
Base constraint functions for healthcare scheduling optimization.

This module provides common constraint functions used across different constraint modules.
These functions are designed to be reusable and configurable.
"""

import logging
import requests
from datetime import date, datetime, time
from math import radians, sin, cos, sqrt, asin
from typing import Dict, List, Any, Optional

from ..domain import Provider, Location, AppointmentData, weekday_from_int
from ..config_manager import ConfigManager

logger = logging.getLogger(__name__)

def _provider_serves_location(provider: Provider, location: Location) -> bool:
    """Check if provider serves the given location."""
    # Simplified implementation - always return True
    # In a real implementation, this would check geographic distance
    return True


def _provider_available_on_date(provider: Provider, target_date) -> bool:
    """Check if provider is available on the given date."""
    # Simplified implementation - always return True
    # In a real implementation, this would check provider's schedule
    return True


def _calculate_distance(loc1: Location, loc2: Location) -> float:
    """Calculate distance between two locations in miles using Haversine formula."""
    if loc1 is None or loc2 is None:
        return 0.0
    
    # Convert decimal degrees to radians
    lat1, lon1 = radians(loc1.latitude), radians(loc1.longitude)
    lat2, lon2 = radians(loc2.latitude), radians(loc2.longitude)
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    
    # Radius of Earth in miles
    r = 3956
    
    return c * r


def _is_within_service_radius(provider: Provider, appointment_location: Location, max_radius_miles: float = 25.0) -> bool:
    """Check if appointment location is within provider's service radius."""
    if provider.home_location is None or appointment_location is None:
        return True  # Default to True if location data is missing
    
    distance = _calculate_distance(provider.home_location, appointment_location)
    return distance <= max_radius_miles


def _has_required_skills(provider: Provider, required_skills: List[str]) -> bool:
    """Check if provider has all required skills."""
    if required_skills is None or len(required_skills) == 0:
        return True
    return all(skill in provider.skills for skill in required_skills)


def _is_working_day(provider: Provider, target_date: date) -> bool:
    """Check if the target date is a working day for the provider."""
    weekday = target_date.weekday()
    weekday_enum = weekday_from_int(weekday)
    
    # Check if provider has availability configuration
    if provider.availability is not None:
        return weekday_enum in provider.availability.working_days
    
    # Default: Monday-Friday (0=Monday, 6=Sunday)
    return weekday < 5


def _get_provider_workload_for_date(provider: Provider, target_date: date, assignments) -> int:
    """Get the number of appointments assigned to a provider on a specific date."""
    count = 0
    for assignment in assignments:
        if (assignment.provider == provider and 
            assignment.assigned_date == target_date):
            count += 1
    return count


def _get_traffic_config() -> Dict[str, Any]:
    """Get traffic configuration from config manager."""
    try:
        config_manager = ConfigManager()
        scheduler_config = config_manager.get_scheduler_config()
        return scheduler_config.traffic_model or {}
    except Exception:
        # Return default configuration if config manager fails
        return {}


def _is_urban_area(location: Location) -> bool:
    """Determine if a location is in an urban area based on configurable city list."""
    if location.city is None:
        return False
    
    # Get configurable urban cities from configuration
    traffic_config = _get_traffic_config()
    urban_cities = traffic_config.get('urban_cities', [
        "New York", "Los Angeles", "Chicago", "Houston", "Phoenix", 
        "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"
    ])
    
    return location.city in urban_cities


def _get_speed_factor(location: Location, target_time: Optional[time] = None, target_date: Optional[date] = None) -> float:
    """Get speed factor for a location considering time, date, and weather adjustments."""
    traffic_config = _get_traffic_config()
    speed_factors = traffic_config.get('speed_factors', {})
    time_adjustments = traffic_config.get('time_adjustments', {})
    
    # Base speed based on area type
    if _is_urban_area(location):
        base_speed = speed_factors.get('urban_speed_mph', 25)
    else:
        base_speed = speed_factors.get('rural_speed_mph', 45)
    
    # Apply time-based adjustments if time is provided
    if target_time is not None:
        hour = target_time.hour
        
        # Morning rush hour (7-9 AM)
        if 7 <= hour <= 9:
            multiplier = time_adjustments.get('rush_hour_morning_multiplier', 0.6)
            base_speed *= multiplier
        
        # Evening rush hour (5-7 PM)
        elif 17 <= hour <= 19:
            multiplier = time_adjustments.get('rush_hour_evening_multiplier', 0.7)
            base_speed *= multiplier
        
        # Night time (10 PM - 6 AM)
        elif hour >= 22 or hour <= 6:
            multiplier = time_adjustments.get('night_multiplier', 1.4)
            base_speed *= multiplier
    
    # Apply date-based adjustments if date is provided
    if target_date is not None:
        weekday = target_date.weekday()
        
        # Weekend (Saturday=5, Sunday=6)
        if weekday >= 5:
            multiplier = time_adjustments.get('weekend_multiplier', 1.2)
            base_speed *= multiplier
        
        # TODO: Add holiday detection logic here
        # For now, we'll skip holiday adjustments
    
    # Apply weather adjustments
    weather_adjustment = _get_weather_adjustment(location, target_date)
    base_speed *= weather_adjustment
    
    return base_speed


def _get_google_maps_route_time(origin: Location, destination: Location, target_time: Optional[time] = None, target_date: Optional[date] = None) -> Optional[int]:
    """Get travel time in minutes using Google Maps Routes API (new API)."""
    try:
        config_manager = ConfigManager()
        scheduler_config = config_manager.get_scheduler_config()
        traffic_config = scheduler_config.traffic_integration or {}
        google_config = traffic_config.get('google_maps', {})
        
        if not google_config.get('enabled', False) or not google_config.get('use_routes_api', False):
            return None
            
        api_key = google_config.get('api_key', '')
        base_url = google_config.get('base_url', 'https://routes.googleapis.com/directions/v2')
        
        if not api_key or not origin or not destination:
            return None
        
        # Build Routes API request (POST request with JSON body)
        url = f"{base_url}:computeRoutes"
        headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': api_key,
            'X-Goog-FieldMask': 'routes.duration,routes.durationInTraffic,routes.staticDuration'
        }
        
        # Create request body for Routes API
        request_body = {
            "origin": {
                "location": {
                    "latLng": {
                        "latitude": origin.latitude,
                        "longitude": origin.longitude
                    }
                }
            },
            "destination": {
                "location": {
                    "latLng": {
                        "latitude": destination.latitude,
                        "longitude": destination.longitude
                    }
                }
            },
            "travelMode": "DRIVE",
            "routingPreference": "TRAFFIC_AWARE_OPTIMAL"
        }
        
        # Add departure time if provided
        if target_time and target_date:
            dt = datetime.combine(target_date, target_time)
            request_body["departureTime"] = dt.isoformat() + "Z"
        
        response = requests.post(url, headers=headers, json=request_body, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('routes') and len(data['routes']) > 0:
            route = data['routes'][0]
            # Prefer durationInTraffic if available, otherwise use duration
            if 'durationInTraffic' in route:
                return int(route['durationInTraffic'].replace('s', '')) // 60
            elif 'duration' in route:
                return int(route['duration'].replace('s', '')) // 60
        
        return None
        
    except Exception:
        return None


def _get_google_weather_data(location: Location, target_time: Optional[time] = None, target_date: Optional[date] = None) -> Optional[Dict[str, Any]]:
    """Get weather data using Google Weather API."""
    try:
        config_manager = ConfigManager()
        scheduler_config = config_manager.get_scheduler_config()
        traffic_config = scheduler_config.traffic_integration or {}
        weather_config = traffic_config.get('google_weather', {})
        
        if not weather_config.get('enabled', False):
            return None
            
        api_key = weather_config.get('api_key', '')
        base_url = weather_config.get('base_url', 'https://weather.googleapis.com/v1')
        
        if not api_key or not location.latitude or not location.longitude:
            return None
        
        # Build Weather API request
        url = f"{base_url}/forecast:lookup"
        headers = {
            'X-Goog-Api-Key': api_key,
            'X-Goog-FieldMask': 'current,forecast'
        }
        
        params = {
            'location': f"{location.latitude},{location.longitude}",
            'units': 'imperial'
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        return data
        
    except Exception:
        return None


def _get_weather_adjustment(location: Location, target_date: Optional[date] = None) -> float:
    """Get weather-based speed adjustment factor using Google Weather API first, then fallback to OpenWeatherMap."""
    try:
        # Try Google Weather API first
        weather_data = _get_google_weather_data(location, target_date=target_date)
        if weather_data:
            # Parse Google Weather API response
            current = weather_data.get('current', {})
            if current:
                # Extract weather conditions from Google Weather API
                condition = current.get('condition', '').lower()
                temp_f = current.get('temperature', 70)
                
                # Calculate weather adjustment factor
                adjustment = 1.0
                
                # Weather condition adjustments
                if 'rain' in condition or 'drizzle' in condition:
                    adjustment *= 0.8  # 20% slower in rain
                elif 'snow' in condition or 'sleet' in condition:
                    adjustment *= 0.6  # 40% slower in snow
                elif 'storm' in condition or 'thunder' in condition:
                    adjustment *= 0.7  # 30% slower in storms
                
                # Temperature adjustments
                if temp_f < 32:  # Below freezing
                    adjustment *= 0.9  # 10% slower
                elif temp_f > 90:  # Very hot
                    adjustment *= 0.95  # 5% slower
                
                return adjustment
        
        # Fallback to OpenWeatherMap if Google Weather API fails or is disabled
        config_manager = ConfigManager()
        scheduler_config = config_manager.get_scheduler_config()
        traffic_config = scheduler_config.traffic_integration or {}
        weather_config = traffic_config.get('weather', {})
        
        if not weather_config.get('enabled', False):
            return 1.0
        
        api_key = weather_config.get('api_key', '')
        base_url = weather_config.get('base_url', 'https://api.openweathermap.org/data/2.5')
        
        if not api_key or not location.latitude or not location.longitude:
            return 1.0
        
        # Get current weather for the location
        params = {
            'lat': location.latitude,
            'lon': location.longitude,
            'appid': api_key,
            'units': 'imperial'
        }
        
        response = requests.get(f"{base_url}/weather", params=params, timeout=5)
        response.raise_for_status()
        weather_data = response.json()
        
        # Extract weather conditions
        weather_main = weather_data.get('weather', [{}])[0].get('main', '').lower()
        weather_description = weather_data.get('weather', [{}])[0].get('description', '').lower()
        temp_f = weather_data.get('main', {}).get('temp', 70)
        
        # Calculate weather adjustment factor
        adjustment = 1.0
        
        # Rain/snow adjustments
        if 'rain' in weather_main or 'rain' in weather_description:
            adjustment *= 0.8  # 20% slower in rain
        elif 'snow' in weather_main or 'snow' in weather_description:
            adjustment *= 0.6  # 40% slower in snow
        elif 'storm' in weather_description or 'thunderstorm' in weather_main:
            adjustment *= 0.7  # 30% slower in storms
        
        # Temperature adjustments
        if temp_f < 32:  # Below freezing
            adjustment *= 0.9  # 10% slower
        elif temp_f > 90:  # Very hot
            adjustment *= 0.95  # 5% slower
        
        return adjustment
        
    except Exception:
        # Silently return default adjustment if weather API fails
        return 1.0


def _calculate_travel_time_between_appointments(appointment1: AppointmentData, appointment2: AppointmentData, 
                                               target_time: Optional[time] = None, target_date: Optional[date] = None) -> int:
    """Calculate travel time between two appointments in minutes."""
    
    # Get locations
    origin = appointment1.location
    destination = appointment2.location
    
    # Check if locations are available
    if origin is None or destination is None:
        return 0
    
    # Type assertion since we've already checked for None
    origin_loc: Location = origin
    dest_loc: Location = destination
    
    # Try Google Routes API first (most accurate)
    route_time = _get_google_maps_route_time(origin_loc, dest_loc, target_time, target_date)
    if route_time is not None:
        return route_time
    
    # Fallback to simplified model with weather and traffic patterns
    distance_km = _calculate_distance_km(origin_loc, dest_loc)
    
    # Get speed factor based on location and time
    speed_factor = _get_speed_factor(origin_loc, target_time, target_date)
    
    # Calculate base travel time (assuming 50 km/h average speed)
    base_time_minutes = (distance_km / 50) * 60
    
    # Apply speed factor
    adjusted_time = base_time_minutes / speed_factor
    
    return int(adjusted_time)


def _is_sufficient_travel_time(appointment1: AppointmentData, appointment2: AppointmentData, 
                              min_travel_time_minutes: int = 30) -> bool:
    """Check if there's sufficient travel time between appointments."""
    travel_time = _calculate_travel_time_between_appointments(appointment1, appointment2)
    return travel_time >= min_travel_time_minutes


def _get_appointment_duration_minutes(appointment: AppointmentData) -> int:
    """Get the duration of an appointment in minutes."""
    return appointment.duration_min


def _is_urgent_appointment(appointment: AppointmentData) -> bool:
    """Check if an appointment is marked as urgent."""
    return appointment.urgent


def _get_service_type_from_skills(required_skills: List[str]) -> str:
    """Determine service type based on required skills."""
    skill_mapping = {
        "medication_management": "skilled_nursing",
        "wound_care": "skilled_nursing", 
        "assessment": "skilled_nursing",
        "iv_therapy": "skilled_nursing",
        "diabetes_management": "skilled_nursing",
        "physical_therapy": "physical_therapy",
        "mobility_training": "physical_therapy",
        "strength_training": "physical_therapy",
        "rehabilitation": "physical_therapy",
        "personal_care": "personal_care",
        "mobility_assistance": "personal_care",
        "housekeeping": "personal_care",
        "meal_assistance": "personal_care",
        "companionship": "personal_care",
        "medication_administration": "general",
        "vital_signs": "general",
        "basic_care": "general",
        "catheter_care": "general",
        "ostomy_care": "general"
    }
    
    for skill in required_skills:
        if skill in skill_mapping:
            return skill_mapping[skill]
    
    return "general"


def _calculate_provider_utilization(provider: Provider, assignments, target_date: date) -> float:
    """Calculate provider utilization percentage for a given date."""
    total_assigned_minutes = 0
    max_available_minutes = provider.capacity.max_hours_per_day * 60
    
    for assignment in assignments:
        if (assignment.provider == provider and 
            assignment.assigned_date == target_date):
            total_assigned_minutes += assignment.appointment_data.duration_min
    
    return (total_assigned_minutes / max_available_minutes * 100) if max_available_minutes > 0 else 0


def _is_provider_overbooked(provider: Provider, assignments, target_date: date) -> bool:
    """Check if provider is overbooked for a given date."""
    utilization = _calculate_provider_utilization(provider, assignments, target_date)
    return utilization > 100.0


def _get_consecutive_appointments_count(provider: Provider, assignments, target_date: date) -> int:
    """Get the maximum number of consecutive appointments for a provider on a given date."""
    date_assignments = [a for a in assignments if a.provider == provider and a.assigned_date == target_date]
    
    if not date_assignments:
        return 0
    
    # Sort by time (if available) or by appointment ID for consistency
    date_assignments.sort(key=lambda x: x.appointment_data.id)
    
    max_consecutive = 1
    current_consecutive = 1
    
    for i in range(1, len(date_assignments)):
        # Check if appointments are consecutive (simplified logic)
        current_consecutive += 1
        max_consecutive = max(max_consecutive, current_consecutive)
    
    return max_consecutive


def _validate_appointment_constraints(appointment: AppointmentData, provider: Provider, 
                                    target_date: date) -> Dict[str, bool]:
    """Validate all constraints for a specific appointment assignment."""
    return {
        "has_required_skills": _has_required_skills(provider, appointment.required_skills),
        "is_working_day": _is_working_day(provider, target_date),
        "within_service_radius": _is_within_service_radius(provider, appointment.location) if appointment.location else True,
        "role_matches": provider.role == appointment.required_role if appointment.required_role else True,
        "duration_appropriate": appointment.duration_min <= provider.capacity.max_hours_per_day * 60
    }


def _calculate_distance_km(location1: Location, location2: Location) -> float:
    """Calculate distance between two locations in kilometers using Haversine formula."""
    import math
    
    lat1, lon1 = math.radians(location1.latitude), math.radians(location1.longitude)
    lat2, lon2 = math.radians(location2.latitude), math.radians(location2.longitude)
    
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # Earth's radius in kilometers
    r = 6371
    
    return c * r 