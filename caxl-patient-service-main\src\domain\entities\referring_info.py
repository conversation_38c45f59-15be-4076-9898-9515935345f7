"""Value Objects for Patient Domain"""

from datetime import date
from uuid import UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class ReferringInformation(EntityBase):
    """Referring information value object"""

    REFERRING_STATE_LENGTH = 2
    NPI_ID_LENGTH = 10

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        referring_mrn: str | None = None,
        referring_name: str | None = None,
        referring_hospital: str | None = None,
        referring_state: str | None = None,
        referring_npi: str | None = None,
        inpatient_discharge_date: date | None = None,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._referring_mrn = referring_mrn
        self._referring_name = referring_name
        self._referring_hospital = referring_hospital
        self._referring_state = referring_state
        self._referring_npi = referring_npi
        self._inpatient_discharge_date = inpatient_discharge_date

    @property
    def referring_mrn(self) -> str | None:
        return self._referring_mrn

    @referring_mrn.setter
    def referring_mrn(self, value: str | None) -> None:
        self._referring_mrn = value

    @property
    def referring_name(self) -> str | None:
        return self._referring_name

    @referring_name.setter
    def referring_name(self, value: str | None) -> None:
        self._referring_name = value

    @property
    def referring_hospital(self) -> str | None:
        return self._referring_hospital

    @referring_hospital.setter
    def referring_hospital(self, value: str | None) -> None:
        self._referring_hospital = value

    @property
    def referring_state(self) -> str | None:
        return self._referring_state

    @referring_state.setter
    def referring_state(self, value: str | None) -> None:
        if value:
            if not value.strip():
                raise ValueError("Referring state cannot be empty if provided")
            if len(value.strip()) != ReferringInformation.REFERRING_STATE_LENGTH:
                raise ValueError("Referring state must be a 2-letter code")
            self._referring_state = value.strip().upper()
        else:
            self._referring_state = None

    @property
    def referring_npi(self) -> str | None:
        return self._referring_npi

    @referring_npi.setter
    def referring_npi(self, value: str | None) -> None:
        if value:
            if not value.strip():
                raise ValueError("Referring NPI cannot be empty if provided")
            if not value.isdigit() or len(value) != ReferringInformation.NPI_ID_LENGTH:
                raise ValueError("NPI must be a 10-digit number")
            self._referring_npi = value.strip()
        else:
            self._referring_npi = None

    @property
    def inpatient_discharge_date(self) -> date | None:
        return self._inpatient_discharge_date

    @inpatient_discharge_date.setter
    def inpatient_discharge_date(self, value: date | None) -> None:
        if value and value > date.today():
            raise ValueError("Inpatient discharge date cannot be in the future")
        self._inpatient_discharge_date = value
