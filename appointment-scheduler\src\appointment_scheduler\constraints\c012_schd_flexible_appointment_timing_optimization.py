"""
Flexible Appointment Timing Optimization Constraint (C012)

This constraint handles appointment duration fitting and preferred hours optimization.
This includes both HARD constraints (duration fit) and SOFT constraints (preferred hours).
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import TimeSlotAssignment
from ..domain import Provider
from datetime import time

def appointment_duration_fit(constraint_factory: ConstraintFactory) -> Constraint:
    """Appointment duration must fit within the assigned time slot."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: (assignment.time_slot is not None and
                                      assignment.scheduled_appointment.appointment_data.duration_min > 60))  # Assuming 1 hour slots
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Appointment duration fit"))


def preferred_hours(constraint_factory: ConstraintFactory) -> Constraint:
    """Prefer assignments within provider's preferred working hours."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: (assignment.time_slot is not None and
                                      not _is_within_preferred_hours(assignment.scheduled_appointment.provider, 
                                                                   assignment.time_slot)))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 1)
            .as_constraint("Preferred hours"))


def _is_within_preferred_hours(provider: Provider, time_slot: time) -> bool:
    """Check if time slot is within provider's preferred working hours."""
    # Standard healthcare hours: 8 AM to 6 PM
    preferred_start = 8  # 8 AM
    preferred_end = 18   # 6 PM
    
    slot_hour = time_slot.hour
    return preferred_start <= slot_hour < preferred_end 