"""SQLAlchemy model for Patient Order Directive"""

from typing import TYPE_CHECKING
from uuid import uuid4

from sqlalchemy import Column, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel

if TYPE_CHECKING:
    pass

# ──────────────────────────────────────────────────────────────────────────────
#  ORM Model
# ──────────────────────────────────────────────────────────────────────────────


class PatientEpisodeOfCareOrderDirective(BaseModel):
    """SQLAlchemy model for Patient Order Directive"""

    __tablename__ = "patient_episode_of_care_order_directive"

    id = Column(PGUUID, primary_key=True, default=uuid4)
    patient_episode_of_care_order_id = Column(
        PGUUID, ForeignKey("patient_episode_of_care_order.id"), nullable=False
    )
    instructions = Column(Text, nullable=False)

    # Relationships
    order = relationship("PatientEpisodeOfCareOrder", back_populates="directives")
