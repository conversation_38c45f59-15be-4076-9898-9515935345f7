"""Schedule appointments use case"""

from datetime import date, datetime
from typing import List, Optional
from uuid import UUID

from src.domain.entities.appointment import Appointment
from src.domain.entities.provider import Provider
from src.domain.entities.consumer import Consumer
from src.application.ports.repositories.appointment_repository import AppointmentRepository
from src.application.ports.repositories.provider_repository import ProviderRepository
from src.application.ports.repositories.consumer_repository import ConsumerRepository
from src.application.ports.services.scheduling_service import SchedulingService
from src.application.dtos.scheduling_request import SchedulingRequest
from src.application.dtos.scheduling_result import SchedulingResult


class ScheduleAppointmentsUseCase:
    """Use case for scheduling appointments"""
    
    def __init__(
        self,
        appointment_repository: AppointmentRepository,
        provider_repository: ProviderRepository,
        consumer_repository: ConsumerRepository,
        scheduling_service: SchedulingService,
    ):
        self._appointment_repository = appointment_repository
        self._provider_repository = provider_repository
        self._consumer_repository = consumer_repository
        self._scheduling_service = scheduling_service
    
    async def execute(self, request: SchedulingRequest) -> SchedulingResult:
        """Execute appointment scheduling"""
        
        # Load appointments to schedule
        appointments = await self._appointment_repository.get_pending_appointments(
            request.appointment_ids
        )
        
        if not appointments:
            return SchedulingResult(
                success=False,
                message="No pending appointments found",
                scheduled_appointments=[],
                failed_appointments=[]
            )
        
        # Load available providers
        providers = await self._provider_repository.get_active_providers()
        
        if not providers:
            return SchedulingResult(
                success=False,
                message="No active providers available",
                scheduled_appointments=[],
                failed_appointments=appointments
            )
        
        # Load consumers
        consumer_ids = [apt.consumer_id for apt in appointments]
        consumers = await self._consumer_repository.get_by_ids(consumer_ids)
        
        # Run scheduling optimization
        result = await self._scheduling_service.schedule_appointments(
            appointments=appointments,
            providers=providers,
            consumers=consumers,
            target_date=request.target_date,
            constraints=request.constraints
        )
        
        # Save scheduled appointments
        if result.scheduled_appointments:
            await self._appointment_repository.update_many(result.scheduled_appointments)
        
        return result


class RunDayPlanUseCase:
    """Use case for running day plan optimization"""
    
    def __init__(
        self,
        appointment_repository: AppointmentRepository,
        provider_repository: ProviderRepository,
        scheduling_service: SchedulingService,
    ):
        self._appointment_repository = appointment_repository
        self._provider_repository = provider_repository
        self._scheduling_service = scheduling_service
    
    async def execute(self, target_date: date) -> SchedulingResult:
        """Execute day plan optimization"""
        
        # Load scheduled appointments for the target date
        appointments = await self._appointment_repository.get_scheduled_for_date(target_date)
        
        if not appointments:
            return SchedulingResult(
                success=True,
                message="No appointments scheduled for this date",
                scheduled_appointments=[],
                failed_appointments=[]
            )
        
        # Load providers for these appointments
        provider_ids = [apt.provider_id for apt in appointments if apt.provider_id]
        providers = await self._provider_repository.get_by_ids(provider_ids)
        
        # Run day plan optimization
        result = await self._scheduling_service.optimize_day_plan(
            appointments=appointments,
            providers=providers,
            target_date=target_date
        )
        
        # Save optimized appointments
        if result.scheduled_appointments:
            await self._appointment_repository.update_many(result.scheduled_appointments)
        
        return result
