-- Database initialization script
-- This script sets up the basic database structure for the appointment scheduler

-- <PERSON>reate database if it doesn't exist
-- CREATE DATABASE IF NOT EXISTS scheduler_dev;

-- Use the database
-- USE scheduler_dev;

-- Create basic tables (simplified for demo)
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY,
    consumer_id UUID NOT NULL,
    provider_id UUID,
    appointment_date DATE,
    scheduled_start_time TIMESTAMP,
    scheduled_end_time TIMESTAMP,
    duration_minutes INTEGER NOT NULL DEFAULT 30,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    location_address TEXT,
    required_skills TEXT[], -- PostgreSQL array
    priority VARCHAR(20) DEFAULT 'normal',
    urgent BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS providers (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    skills TEXT[], -- PostgreSQL array
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    location_address TEXT,
    service_radius_miles DECIMAL(5, 2) DEFAULT 25.0,
    email VARCHAR(255),
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS consumers (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    location_address TEXT,
    email VARCHAR(255),
    phone VARCHAR(50),
    care_episode_id UUID,
    medical_record_number VARCHAR(100),
    special_instructions TEXT,
    accessibility_needs TEXT[], -- PostgreSQL array
    language_preference VARCHAR(50) DEFAULT 'English',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_consumer_id ON appointments(consumer_id);
CREATE INDEX IF NOT EXISTS idx_appointments_provider_id ON appointments(provider_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);

CREATE INDEX IF NOT EXISTS idx_providers_status ON providers(status);
CREATE INDEX IF NOT EXISTS idx_providers_role ON providers(role);

CREATE INDEX IF NOT EXISTS idx_consumers_status ON consumers(status);
CREATE INDEX IF NOT EXISTS idx_consumers_care_episode ON consumers(care_episode_id);
