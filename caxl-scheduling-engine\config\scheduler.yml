# CareAxl Scheduling Engine Configuration
# Configuration-driven scheduling with all constraints and features

# Rolling window configuration
rolling_window_days: 7
max_solving_time_seconds: 300

# Configuration paths
config_folder: "config"
log_level: "INFO"

# External service configuration
external_services:
  patient_service:
    base_url: "http://localhost:8081"
    timeout_seconds: 30
    retry_attempts: 3
    circuit_breaker_threshold: 5
  
  provider_service:
    base_url: "http://localhost:8082"
    timeout_seconds: 30
    retry_attempts: 3
    circuit_breaker_threshold: 5
  
  appointment_service:
    base_url: "http://localhost:8083"
    timeout_seconds: 30
    retry_attempts: 3
    circuit_breaker_threshold: 5

# =============================================================================
# FEATURE TOGGLES
# =============================================================================

# Assignment Stage Feature Toggles
# (Affect provider/date assignment)
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true

# Schedule/Day Planning Stage Feature Toggles
# (Affect time slot and daily schedule optimization)
enable_healthcare_task_sequencing: true
enable_travel_time_optimization: true
enable_break_time_management: true
enable_route_optimization: true

# Pinned appointment handling
enable_pinned_appointments: true
respect_pinned_times: true
allow_pinned_provider_changes: false

# =============================================================================
# CONSTRAINT CONFIGURATION
# =============================================================================

constraints:
  # Skill matching constraints
  skill_matching:
    enabled: true
    strict_mode: true  # Require exact skill match vs. allow skill hierarchy
    
  # Geographic constraints
  geographic:
    enabled: true
    max_travel_distance_miles: 50
    prefer_local_assignments: true
    clustering_weight: 0.3
    
  # Workload balancing
  workload:
    enabled: true
    max_appointments_per_day: 8
    max_hours_per_day: 8
    balance_across_providers: true
    
  # Continuity of care
  continuity:
    enabled: true
    prefer_same_provider: true
    same_provider_weight: 0.4
    
  # Time constraints
  timing:
    enabled: true
    respect_provider_availability: true
    respect_patient_preferences: true
    allow_overtime: false

# =============================================================================
# TRAFFIC MODEL CONFIGURATION
# =============================================================================

traffic_model:
  # Speed factors for different area types (miles per hour)
  speed_factors:
    urban_speed_mph: 25      # Average speed in urban areas with traffic
    suburban_speed_mph: 35   # Average speed in suburban areas
    rural_speed_mph: 45      # Average speed in rural areas
    highway_speed_mph: 65    # Average speed on highways
    
  # Time-based speed adjustments (multipliers)
  time_adjustments:
    rush_hour_morning_multiplier: 0.6   # 60% of normal speed during morning rush (7-9 AM)
    rush_hour_evening_multiplier: 0.7   # 70% of normal speed during evening rush (5-7 PM)
    weekend_multiplier: 1.2             # 20% faster on weekends
    holiday_multiplier: 1.3             # 30% faster on holidays
    night_multiplier: 1.4               # 40% faster at night (10 PM - 6 AM)

# =============================================================================
# SOLVER CONFIGURATION
# =============================================================================

solver:
  engine: "timefold"
  max_solving_time_seconds: 300
  termination_conditions:
    - type: "time_limit"
      value: 300
    - type: "best_score_feasible"
      value: true
  
  # Optimization weights
  weights:
    hard_constraints: 1000
    geographic_clustering: 100
    workload_balancing: 80
    continuity_of_care: 60
    travel_time: 40
    patient_preferences: 30

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/scheduling_engine.log"
  max_file_size_mb: 100
  backup_count: 5
