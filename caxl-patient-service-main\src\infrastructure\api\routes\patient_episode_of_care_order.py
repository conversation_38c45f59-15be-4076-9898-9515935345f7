"""Routes for Patient Episode of Care Order"""

import logging
import traceback
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query

from src.application.dtos.patient_episode_of_care_order_dto import (
    PatientEpisodeOfCareOrderResponseDTO,
    RemoveDirectivesDTO,
)
from src.application.usecases.patient_episode_of_care_order_usecase import (
    PatientEpisodeOfCareOrderUseCase,
)
from src.container import Container
from src.infrastructure.api.dependencies.headers import get_tenant_id, get_user_id
from src.infrastructure.api.dependencies.session import get_user_context
from src.infrastructure.api.request_mappers.patient_episode_of_care_order_mapper import (
    map_add_directives_request_to_dto,
    map_create_request_to_dto,
    map_update_request_to_dto,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient_episode_of_care_order_schema import (
    AddDirectivesSchema,
    PatientEpisodeOfCareOrderCreateSchema,
    PatientEpisodeOfCareOrderListResponseSchema,
    PatientEpisodeOfCareOrderResponseSchema,
    PatientEpisodeOfCareOrderUpdateSchema,
    RemoveDirectivesSchema,
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/patient-eoc-orders",
    tags=["Patient Episode of Care Orders"],
    dependencies=[
        Depends(get_tenant_id),
        Depends(get_user_id),
    ],
)


def get_patient_episode_of_care_order_use_case() -> PatientEpisodeOfCareOrderUseCase:
    """Get Patient Episode of Care Order use case"""
    return Container().patient_episode_order_use_case()


@router.post("", response_model=PatientEpisodeOfCareOrderResponseSchema)
async def create_patient_episode_of_care_order(
    request: PatientEpisodeOfCareOrderCreateSchema,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareOrderResponseDTO:
    """Create a new patient episode of care order"""
    try:
        dto = map_create_request_to_dto(request, context)
        return await use_case.create(dto, context)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error creating patient episode order: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("/{order_id}", response_model=PatientEpisodeOfCareOrderResponseSchema)
async def get_patient_episode_of_care_order(
    order_id: UUID,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
) -> PatientEpisodeOfCareOrderResponseDTO:
    """Get a patient episode of care order by ID"""
    try:
        result = await use_case.get_by_id(order_id)
        if not result:
            raise HTTPException(status_code=404, detail="Patient episode order not found")
        return result
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error getting patient episode order: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("", response_model=PatientEpisodeOfCareOrderListResponseSchema)
async def get_patient_episode_of_care_orders(
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
) -> PatientEpisodeOfCareOrderListResponseSchema:
    """Get all patient episode of care orders with pagination"""
    try:
        orders = await use_case.get_all(page=page, page_size=page_size)
        total = await use_case.count()
        total_pages = (total + page_size - 1) // page_size
        return PatientEpisodeOfCareOrderListResponseSchema(
            orders=orders,
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error listing patient episode orders: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.put("/{order_id}", response_model=PatientEpisodeOfCareOrderResponseSchema)
async def update_patient_episode_of_care_order(
    order_id: UUID,
    request: PatientEpisodeOfCareOrderUpdateSchema,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareOrderResponseDTO:
    """Update a patient episode of care order"""
    try:
        if order_id != request.id:
            raise HTTPException(status_code=400, detail="Order ID mismatch")
        dto = map_update_request_to_dto(order_id, request, context)
        result = await use_case.update(dto, context)
        if not result:
            raise HTTPException(status_code=404, detail="Patient episode order not found")
        return result
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error updating patient episode order: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.delete("/{order_id}")
async def delete_patient_episode_of_care_order(
    order_id: UUID,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    context: AuthSession = Depends(get_user_context),
) -> dict:
    """Delete a patient episode of care order"""
    try:
        success = await use_case.delete(order_id, context)
        if not success:
            raise HTTPException(status_code=404, detail="Patient episode order not found")
        return {"message": "Patient episode order deleted successfully"}
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error deleting patient episode order: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.post("/{order_id}/directives", response_model=PatientEpisodeOfCareOrderResponseSchema)
async def add_directives(
    order_id: UUID,
    directives: AddDirectivesSchema,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareOrderResponseDTO:
    """Add directives to an order"""
    try:
        dto = map_add_directives_request_to_dto(directives, context)
        updated_order = await use_case.add_directives(order_id, dto, context)
        if not updated_order:
            raise HTTPException(status_code=404, detail="Order not found")
        return updated_order
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error adding directives: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.delete("/{order_id}/directives", response_model=PatientEpisodeOfCareOrderResponseSchema)
async def remove_directives(
    order_id: UUID,
    directives: RemoveDirectivesSchema,
    use_case: PatientEpisodeOfCareOrderUseCase = Depends(
        get_patient_episode_of_care_order_use_case
    ),
    context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareOrderResponseDTO:
    """Remove directives from an order"""
    try:
        dto = RemoveDirectivesDTO(directive_ids=directives.directive_ids)
        updated_order = await use_case.remove_directives(order_id, dto, context)
        if not updated_order:
            raise HTTPException(status_code=404, detail="Order not found")
        return updated_order
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error removing directives: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e
