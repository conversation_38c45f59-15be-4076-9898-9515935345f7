"""Test configuration and fixtures"""

import pytest
from fastapi.testclient import TestClient

from src.main import app


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
def mock_appointment():
    """Mock appointment fixture"""
    return {
        "id": "test-appointment-001",
        "consumer_id": "test-consumer-001",
        "provider_id": "test-provider-001",
        "appointment_date": "2024-01-15",
        "scheduled_start_time": "08:00:00",
        "scheduled_end_time": "09:00:00",
        "duration_minutes": 60,
        "status": "scheduled",
        "location": {
            "latitude": 40.758,
            "longitude": -73.9855,
            "address": "123 Test Street, Test City, NY 10001"
        },
        "required_skills": ["test_skill"]
    }


@pytest.fixture
def mock_provider():
    """Mock provider fixture"""
    return {
        "id": "test-provider-001",
        "name": "Test Provider",
        "role": "RN",
        "skills": ["test_skill", "wound_care"],
        "status": "active",
        "location": {
            "latitude": 40.758,
            "longitude": -73.9855,
            "address": "Test Provider Location"
        },
        "service_radius_miles": 25.0,
        "email": "<EMAIL>",
        "phone": "(*************"
    }


@pytest.fixture
def mock_consumer():
    """Mock consumer fixture"""
    return {
        "id": "test-consumer-001",
        "name": "Test Consumer",
        "status": "active",
        "location": {
            "latitude": 40.758,
            "longitude": -73.9855,
            "address": "123 Test Street, Test City, NY 10001"
        },
        "email": "<EMAIL>",
        "phone": "(*************",
        "care_episode_id": "test-episode-001",
        "medical_record_number": "TEST-MRN-001"
    }
