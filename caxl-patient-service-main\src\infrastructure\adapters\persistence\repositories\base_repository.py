"""Base repository implementation for persistence layer"""

from typing import Generic, TypeVar
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.sql import Select

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.foundations.base.repository_base import BaseRepository
from src.infrastructure.adapters.persistence.mappers.base_mapper import BaseMapper
from src.infrastructure.adapters.persistence.session import SessionManager

T = TypeVar("T", bound=EntityBase)
M = TypeVar("M")  # Model type


class BaseRepositoryImpl(Generic[T, M], BaseRepository[T]):
    """Base repository implementation for persistence layer"""

    def __init__(self, session_manager: SessionManager, mapper: BaseMapper[T, M]):
        """Initialize repository"""
        self._session_manager = session_manager
        self._mapper = mapper

    async def find_by_id(self, entity_id: UUID) -> T | None:
        """Find entity by ID"""
        async for session in self._session_manager.get_session():
            query = select(self._mapper.model).where(
                self._mapper.model.id == entity_id,
                self._mapper.model.record_status == RecordStatusEnum.ACTIVE,
            )
            result = await session.execute(query)
            model = result.unique().scalar_one_or_none()
            return self._mapper.to_domain(model) if model else None

    async def find_all(self, page: int = 1, page_size: int = 10) -> list[T]:
        """Find all entities with pagination"""
        async for session in self._session_manager.get_session():
            query = select(self._mapper.model).where(
                self._mapper.model.record_status == RecordStatusEnum.ACTIVE
            )
            query = self._apply_pagination(query, page, page_size)
            result = await session.execute(query)
            models = result.unique().scalars().all()
            return [self._mapper.to_domain(model) for model in models]

    async def save(self, entity: T, audit_stamp: AuditStamp) -> T:
        """Save a new entity"""
        model = self._mapper.to_model(entity)
        model.apply_audit_stamp(audit_stamp=audit_stamp)

        async for session in self._session_manager.get_session():
            async with session.begin():
                session.add(model)
                await session.flush()
                await session.refresh(model)
                await session.commit()
                return self._mapper.to_domain(model)

    async def update(self, entity: T, audit_stamp: AuditStamp) -> T:
        """Update an existing entity"""
        model = self._mapper.to_model(entity)
        model.apply_audit_stamp(audit_stamp=audit_stamp)

        async for session in self._session_manager.get_session():
            async with session.begin():
                # Merge returns the managed (attached) model
                managed_model = await session.merge(model)
                await session.commit()
                return self._mapper.to_domain(managed_model)

    async def delete(self, entity_id: UUID, audit_stamp: AuditStamp) -> bool:
        """Delete entity by ID (hard delete assumed)"""
        async for session in self._session_manager.get_session():
            async with session.begin():
                query = select(self._mapper.model).where(self._mapper.model.id == entity_id)
                result = await session.execute(query)
                model = result.unique().scalar_one_or_none()

                if not model:
                    return False

                model.apply_audit_stamp(audit_stamp=audit_stamp)
                await session.merge(model)
                await session.commit()
                return True

    async def count(self) -> int:
        async for session in self._session_manager.get_session():
            stmt = (
                select(func.count())
                .select_from(self._mapper.model)
                .where(self._mapper.model.record_status == RecordStatusEnum.ACTIVE)
            )
            result = await session.execute(stmt)
            return result.scalar_one()

    def _apply_pagination(self, query: Select, page: int, page_size: int) -> Select:
        """Apply pagination to query"""
        offset = (page - 1) * page_size
        return query.offset(offset).limit(page_size)
