from typing import Any

# GenericFilters can be used for simple key-value filtering.
# Keys would typically be field names, and values the criteria.
GenericFilters = dict[str, Any]

# More advanced filter structures could be defined here if needed, for example:
# class FilterCondition:
#     field: str
#     operator: str # e.g., "eq", "neq", "gt", "lt", "in", "like"
#     value: Any

# AdvancedFilters = List[FilterCondition]

# Or a more comprehensive filter model:
# from pydantic import BaseModel, Field
# class SortParams(BaseModel):
#     field: str
#     direction: str = "asc" # "asc" or "desc"

# class PaginationParams(BaseModel):
#     limit: Optional[int] = None
#     offset: Optional[int] = None

# class QueryParams(BaseModel):
#     filters: Optional[GenericFilters] = None
#     # advanced_filters: Optional[AdvancedFilters] = None
#     sort: Optional[List[SortParams]] = None
#     pagination: Optional[PaginationParams] = None
