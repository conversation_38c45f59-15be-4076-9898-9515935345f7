<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\appointment_scheduler\constraints\c006_asgn_geographic_clustering_optimization.py: 75%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\appointment_scheduler\constraints\c006_asgn_geographic_clustering_optimization.py</b>:
            <span class="pc_cov">75%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">4 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">3<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">1<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Geographic Clustering Optimization Constraint (C006)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">This constraint maintains capacity thresholds for service types per date.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">This is a SOFT constraint for optimization preferences.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">timefold</span><span class="op">.</span><span class="nam">solver</span><span class="op">.</span><span class="nam">score</span> <span class="key">import</span> <span class="nam">HardSoftScore</span><span class="op">,</span> <span class="nam">ConstraintFactory</span><span class="op">,</span> <span class="nam">Constraint</span><span class="op">,</span> <span class="nam">Joiners</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">planning_models</span> <span class="key">import</span> <span class="nam">AppointmentAssignment</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">def</span> <span class="nam">capacity_thresholds</span><span class="op">(</span><span class="nam">constraint_factory</span><span class="op">:</span> <span class="nam">ConstraintFactory</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Constraint</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">    <span class="str">"""Maintain capacity thresholds for service types per date."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="key">return</span> <span class="op">(</span><span class="nam">constraint_factory</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">            <span class="op">.</span><span class="nam">for_each</span><span class="op">(</span><span class="nam">AppointmentAssignment</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">            <span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">AppointmentAssignment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">                  <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">assignment</span><span class="op">:</span> <span class="nam">assignment</span><span class="op">.</span><span class="nam">assigned_date</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">                  <span class="nam">Joiners</span><span class="op">.</span><span class="nam">equal</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">assignment</span><span class="op">:</span> <span class="nam">assignment</span><span class="op">.</span><span class="nam">appointment_data</span><span class="op">.</span><span class="nam">required_skills</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">            <span class="op">.</span><span class="nam">group_by</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">assignment1</span><span class="op">,</span> <span class="nam">assignment2</span><span class="op">:</span> <span class="nam">assignment1</span><span class="op">.</span><span class="nam">assigned_date</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">                     <span class="key">lambda</span> <span class="nam">assignment1</span><span class="op">,</span> <span class="nam">assignment2</span><span class="op">:</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">            <span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="key">lambda</span> <span class="nam">assigned_date</span><span class="op">,</span> <span class="nam">count</span><span class="op">:</span> <span class="nam">count</span> <span class="op">></span> <span class="num">3</span><span class="op">)</span>  <span class="com"># Max 3 appointments per date per service type</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">            <span class="op">.</span><span class="nam">penalize</span><span class="op">(</span><span class="nam">HardSoftScore</span><span class="op">.</span><span class="nam">ONE_SOFT</span><span class="op">,</span> <span class="key">lambda</span> <span class="nam">assigned_date</span><span class="op">,</span> <span class="nam">count</span><span class="op">:</span> <span class="nam">count</span> <span class="op">-</span> <span class="num">3</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">            <span class="op">.</span><span class="nam">as_constraint</span><span class="op">(</span><span class="str">"Capacity thresholds"</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-25 07:59 +0530
        </p>
    </div>
</footer>
</body>
</html>
