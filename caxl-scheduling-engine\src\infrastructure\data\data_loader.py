"""
Data loader for CareAxl Scheduling Engine test scenarios.

This module loads test data from YAML files for different scheduling scenarios,
making it easy to test various edge cases and configurations.
"""

import yaml
from datetime import date, datetime, time
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class SchedulingDataLoader:
    """Loads scheduling test data from YAML files."""
    
    def __init__(self, scenario_folder: str):
        """Initialize data loader for a specific scenario.
        
        Args:
            scenario_folder: Path to the scenario folder (e.g., 'data/scenarios/basic_demo')
        """
        self.scenario_folder = Path(scenario_folder)
        if not self.scenario_folder.exists():
            raise FileNotFoundError(f"Scenario folder not found: {self.scenario_folder}")
        
        logger.info(f"Initialized data loader for scenario: {self.scenario_folder.name}")
    
    def load_providers(self) -> List[Dict[str, Any]]:
        """Load providers from YAML file."""
        logger.info("Loading provider data...")
        providers_file = self.scenario_folder / "providers.yml"
        
        if not providers_file.exists():
            raise FileNotFoundError(f"Providers file not found: {providers_file}")
        
        with open(providers_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        providers = data.get('providers', [])
        logger.info(f"✅ Loaded {len(providers)} providers")
        return providers
    
    def load_consumers(self) -> List[Dict[str, Any]]:
        """Load consumers/patients from YAML file."""
        logger.info("Loading consumer/patient data...")
        consumers_file = self.scenario_folder / "consumers.yml"
        
        if not consumers_file.exists():
            raise FileNotFoundError(f"Consumers file not found: {consumers_file}")
        
        with open(consumers_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        consumers = data.get('consumers', [])
        logger.info(f"✅ Loaded {len(consumers)} consumers/patients")
        return consumers
    
    def load_appointments(self) -> List[Dict[str, Any]]:
        """Load appointments from YAML file."""
        logger.info("Loading appointment data...")
        appointments_file = self.scenario_folder / "appointments.yml"
        
        if not appointments_file.exists():
            raise FileNotFoundError(f"Appointments file not found: {appointments_file}")
        
        with open(appointments_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        appointments = data.get('appointments', [])
        
        # Process datetime strings
        for apt in appointments:
            if apt.get('scheduled_start_time'):
                apt['scheduled_start_time'] = self._parse_datetime(apt['scheduled_start_time'])
            if apt.get('scheduled_end_time'):
                apt['scheduled_end_time'] = self._parse_datetime(apt['scheduled_end_time'])
            if apt.get('appointment_date'):
                apt['appointment_date'] = self._parse_date(apt['appointment_date'])
        
        logger.info(f"✅ Loaded {len(appointments)} appointments")
        return appointments
    
    def load_all_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load all data for the scenario."""
        logger.info(f"Loading all data for scenario: {self.scenario_folder.name}")
        
        data = {
            'providers': self.load_providers(),
            'consumers': self.load_consumers(),
            'appointments': self.load_appointments()
        }
        
        logger.info(f"✅ Loaded complete scenario data: {len(data['providers'])} providers, "
                   f"{len(data['consumers'])} consumers, {len(data['appointments'])} appointments")
        
        return data
    
    def get_scenario_info(self) -> Dict[str, Any]:
        """Get information about the scenario."""
        readme_file = self.scenario_folder / "README.md"
        
        info = {
            'name': self.scenario_folder.name,
            'path': str(self.scenario_folder),
            'description': 'No description available'
        }
        
        if readme_file.exists():
            with open(readme_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Extract first paragraph as description
                lines = content.split('\n')
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        info['description'] = line.strip()
                        break
        
        return info
    
    def _parse_datetime(self, dt_str: str) -> Optional[datetime]:
        """Parse datetime string to datetime object."""
        if not dt_str:
            return None
        
        try:
            # Try ISO format first
            return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        except ValueError:
            try:
                # Try common formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        return datetime.strptime(dt_str, fmt)
                    except ValueError:
                        continue
                raise ValueError(f"Unable to parse datetime: {dt_str}")
            except ValueError as e:
                logger.warning(f"Failed to parse datetime '{dt_str}': {e}")
                return None
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """Parse date string to date object."""
        if not date_str:
            return None
        
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError as e:
            logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None


def list_available_scenarios(scenarios_root: str = "data/scenarios") -> List[Dict[str, Any]]:
    """List all available test scenarios."""
    scenarios_path = Path(scenarios_root)
    
    if not scenarios_path.exists():
        logger.warning(f"Scenarios directory not found: {scenarios_path}")
        return []
    
    scenarios = []
    for scenario_dir in scenarios_path.iterdir():
        if scenario_dir.is_dir():
            try:
                loader = SchedulingDataLoader(str(scenario_dir))
                info = loader.get_scenario_info()
                scenarios.append(info)
            except Exception as e:
                logger.warning(f"Failed to load scenario {scenario_dir.name}: {e}")
    
    return scenarios


def load_scenario_data(scenario_name: str, scenarios_root: str = "data/scenarios") -> Dict[str, Any]:
    """Load data for a specific scenario by name."""
    scenario_path = Path(scenarios_root) / scenario_name
    
    if not scenario_path.exists():
        available = [s['name'] for s in list_available_scenarios(scenarios_root)]
        raise ValueError(f"Scenario '{scenario_name}' not found. Available scenarios: {available}")
    
    loader = SchedulingDataLoader(str(scenario_path))
    return loader.load_all_data()
