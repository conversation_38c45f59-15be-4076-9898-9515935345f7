# Geographic Clustering Scenario

**Purpose**: Demonstrate geographic clustering optimization
**Best for**: Showing how the system groups nearby appointments
**Complexity**: Medium

## Features Demonstrated
- Geographic clustering optimization
- Service area constraints
- Provider location optimization
- Travel time minimization

## Data Overview
- **Providers**: 4 (spread across different areas)
- **Patients**: 12 (clustered in 3 geographic areas)
- **Appointments**: 15
- **Geographic Coverage**: 3 distinct clusters in NYC

## Geographic Clusters
1. **Downtown Manhattan** (Financial District)
2. **Midtown Manhattan** (Times Square area)
3. **Upper Manhattan** (Central Park area)

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/geographic_clustering/* data/

# Enable geographic clustering
# Edit config/scheduler.yml: enable_geographic_clustering: true

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Providers should be assigned to patients in their nearest cluster
- Travel times should be minimized
- Geographic service areas should be respected
- Clustering should be evident in the final schedule 