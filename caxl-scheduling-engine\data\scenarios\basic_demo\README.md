# Basic Demo Scenario

## Overview
This scenario demonstrates basic appointment assignment and day planning functionality with a simple set of providers, patients, and appointments.

## Test Data
- **Providers**: 3 healthcare providers with different skills
- **Patients**: 5 patients in different locations
- **Appointments**: 8 appointments with varying requirements

## Expected Outcomes
- All appointments should be assigned to appropriate providers
- Geographic clustering should minimize travel time
- Skill matching should be respected
- Day plan should optimize visit order and timing

## Test Cases
1. **Assignment Test**: Verify all appointments get assigned
2. **Skill Matching**: Verify providers are matched to required skills
3. **Geographic Optimization**: Verify nearby appointments are clustered
4. **Day Plan**: Verify optimal visit timing and routing
