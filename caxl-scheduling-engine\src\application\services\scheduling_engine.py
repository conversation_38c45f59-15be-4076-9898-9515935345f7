"""Core scheduling engine service"""

import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

# Add the original scheduler to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../appointment-scheduler/src'))

from src.infrastructure.external.patient_service_client import PatientServiceClient
from src.infrastructure.external.provider_service_client import ProviderServiceClient
from src.infrastructure.external.appointment_service_client import AppointmentServiceClient

# Import the scheduler jobs
try:
    from appointment_scheduler.jobs.assign_appointments import AssignAppointmentJob
    from appointment_scheduler.jobs.day_plan import DayPlanJob
    SCHEDULER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import scheduler jobs: {e}")
    SCHEDULER_AVAILABLE = False

# For demo purposes, force mock mode until configuration is set up
SCHEDULER_AVAILABLE = False


class SchedulingEngine:
    """Core scheduling engine that orchestrates appointment assignment and day planning"""
    
    def __init__(self):
        self.patient_client = PatientServiceClient()
        self.provider_client = ProviderServiceClient()
        self.appointment_client = AppointmentServiceClient()
    
    async def assign_appointments(
        self,
        appointment_ids: Optional[List[str]] = None,
        target_date: Optional[str] = None,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run appointment assignment optimization

        Process:
        1. Fetch existing appointments (pending) as batch
        2. Fetch available providers
        3. Fetch consumers/patients for those appointments
        4. Run assignment optimization with rolling window scheduling
        5. Return assignment results with provider assignments and dates
        """
        
        try:
            # Get appointments to schedule
            if appointment_ids:
                appointments = await self.appointment_client.get_appointments(appointment_ids)
            else:
                appointments = await self.appointment_client.get_pending_appointments()

            if not appointments:
                return {
                    "success": False,
                    "message": "No appointments found for scheduling",
                    "scheduled_count": 0,
                    "failed_count": 0,
                    "processing_time_seconds": 0.0,
                    "details": {}
                }

            # Get available providers
            providers = await self.provider_client.get_providers()

            if not providers:
                return {
                    "success": False,
                    "message": "No providers available",
                    "scheduled_count": 0,
                    "failed_count": len(appointments),
                    "processing_time_seconds": 0.0,
                    "details": {}
                }

            # Get patients for the appointments
            patient_ids = list(set(apt["patient_id"] for apt in appointments))
            patients = await self.patient_client.get_patients(patient_ids)

            # Check if scheduler is available
            if not SCHEDULER_AVAILABLE:
                # Return mock success with assignment details
                mock_assignments = self._generate_mock_assignments(appointments, providers)
                return {
                    "success": True,
                    "message": "Assignment completed (mock mode - scheduler not available)",
                    "scheduled_count": len(appointments),
                    "failed_count": 0,
                    "processing_time_seconds": 2.5,
                    "assignments": mock_assignments,
                    "details": {
                        "algorithm": "mock_assignment",
                        "constraints_applied": ["skill_matching", "geographic_optimization", "availability"],
                        "total_appointments": len(appointments),
                        "total_providers": len(providers),
                        "mode": "mock"
                    }
                }

            # Convert to the format expected by the original scheduler
            scheduler_data = self._convert_to_scheduler_format(appointments, providers, patients)

            # Run the assignment job
            assignment_job = AssignAppointmentJob(daemon_mode=False)
            
            # Create a temporary data file for the scheduler
            import tempfile
            import yaml
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(scheduler_data, f)
                temp_file = f.name
            
            try:
                # Run the assignment
                start_time = datetime.now()
                result = assignment_job.run()
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                
                # Update appointments with results (stub for now)
                if result.get("success", False):
                    await self.appointment_client.update_appointments(appointments)
                
                return {
                    "success": result.get("success", False),
                    "message": result.get("message", "Assignment completed"),
                    "scheduled_count": result.get("assigned_count", 0),
                    "failed_count": result.get("failed_count", 0),
                    "processing_time_seconds": processing_time,
                    "details": {
                        "algorithm": "timefold_optimization",
                        "constraints_applied": ["skill_matching", "geographic_optimization", "availability"],
                        "total_appointments": len(appointments),
                        "total_providers": len(providers)
                    }
                }
                
            finally:
                # Clean up temp file
                os.unlink(temp_file)
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Assignment failed: {str(e)}",
                "scheduled_count": 0,
                "failed_count": len(appointments) if 'appointments' in locals() else 0,
                "processing_time_seconds": 0.0,
                "details": {"error": str(e)}
            }
    
    async def run_day_plan(
        self, 
        target_date: str,
        provider_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Run day plan optimization"""
        
        try:
            # Get scheduled appointments for the target date
            appointments = await self.appointment_client.get_appointments()
            # Filter for scheduled appointments (in a real implementation, this would be done by the API)
            # For demo purposes, treat some pending appointments as scheduled
            scheduled_appointments = [apt for apt in appointments if apt.get("status") in ["scheduled", "pending"]][:4]  # Take first 4 for demo

            if not scheduled_appointments:
                return {
                    "success": True,
                    "message": "No scheduled appointments found for this date",
                    "assigned_count": 0,
                    "processing_time_seconds": 0.0,
                    "visit_order": []
                }
            
            # Get providers
            if provider_ids:
                providers = await self.provider_client.get_providers(provider_ids)
            else:
                provider_ids_from_appointments = list(set(
                    apt["provider_id"] for apt in scheduled_appointments 
                    if apt.get("provider_id")
                ))
                providers = await self.provider_client.get_providers(provider_ids_from_appointments)
            
            # Get patients
            patient_ids = list(set(apt["patient_id"] for apt in scheduled_appointments))
            patients = await self.patient_client.get_patients(patient_ids)

            # Check if scheduler is available
            if not SCHEDULER_AVAILABLE:
                # Return mock success for demo purposes
                mock_schedules = self._generate_mock_visit_schedules(providers, scheduled_appointments)
                return {
                    "success": True,
                    "message": "Day plan completed (mock mode - scheduler not available)",
                    "assigned_count": len(scheduled_appointments),
                    "processing_time_seconds": 3.0,
                    "visit_schedules": mock_schedules
                }

            # Convert to scheduler format
            scheduler_data = self._convert_to_scheduler_format(scheduled_appointments, providers, patients)

            # Run the day plan job
            day_plan_job = DayPlanJob(daemon_mode=False)
            
            # Create temporary data file
            import tempfile
            import yaml
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(scheduler_data, f)
                temp_file = f.name
            
            try:
                # Run the day plan
                start_time = datetime.now()
                result = day_plan_job.run()
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                
                # Convert result to API format
                visit_order = self._convert_day_plan_result(result, providers, patients)
                
                return {
                    "success": result.get("success", False),
                    "message": result.get("message", "Day plan completed"),
                    "assigned_count": result.get("assigned_count", 0),
                    "processing_time_seconds": processing_time,
                    "visit_order": visit_order
                }
                
            finally:
                # Clean up temp file
                os.unlink(temp_file)
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Day plan failed: {str(e)}",
                "assigned_count": 0,
                "processing_time_seconds": 0.0,
                "visit_order": [],
                "details": {"error": str(e)}
            }
    
    def _convert_to_scheduler_format(
        self, 
        appointments: List[Dict[str, Any]], 
        providers: List[Dict[str, Any]], 
        patients: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Convert API data to the format expected by the original scheduler"""
        
        # Create patient lookup
        patient_lookup = {p["id"]: p for p in patients}
        
        # Convert appointments
        scheduler_appointments = []
        for apt in appointments:
            patient = patient_lookup.get(apt["patient_id"], {})
            scheduler_apt = {
                "id": apt["id"],
                "consumer_id": apt["patient_id"],
                "duration_minutes": apt["duration_minutes"],
                "required_skills": apt["required_skills"],
                "priority": apt.get("priority", "normal"),
                "urgent": apt.get("urgent", False),
                "location": apt.get("location", {}),
                "service_type": apt.get("service_type", "home_health")
            }
            scheduler_appointments.append(scheduler_apt)
        
        # Convert providers
        scheduler_providers = []
        for provider in providers:
            scheduler_provider = {
                "id": provider["id"],
                "name": provider["name"],
                "role": provider["role"],
                "skills": provider["skills"],
                "location": provider.get("location", {}),
                "service_radius_miles": provider.get("service_radius_miles", 25.0),
                "availability": provider.get("availability", {})
            }
            scheduler_providers.append(scheduler_provider)
        
        # Convert patients
        scheduler_patients = []
        for patient in patients:
            scheduler_patient = {
                "id": patient["id"],
                "name": patient["name"],
                "location": patient.get("location", {}),
                "care_episode_id": patient.get("care_episode_id"),
                "special_instructions": patient.get("special_instructions")
            }
            scheduler_patients.append(scheduler_patient)
        
        return {
            "appointments": scheduler_appointments,
            "providers": scheduler_providers,
            "patients": scheduler_patients
        }
    
    def _convert_day_plan_result(
        self, 
        result: Dict[str, Any], 
        providers: List[Dict[str, Any]], 
        patients: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert day plan result to API format"""
        
        # Create lookups
        provider_lookup = {p["id"]: p for p in providers}
        patient_lookup = {p["id"]: p for p in patients}
        
        visit_order = []
        
        # Mock visit order for now (in real implementation, extract from result)
        for provider in providers[:2]:  # Limit to first 2 providers for demo
            provider_visits = {
                "provider_id": provider["id"],
                "provider_name": f"{provider['name']}, {provider['role']}",
                "visits": [
                    {
                        "time": "08:00-09:00",
                        "patient": "Margaret Smith",
                        "location": "New York",
                        "address": "123 Park Avenue, Apt 4B"
                    },
                    {
                        "time": "09:30-10:45",
                        "patient": "David Kim",
                        "location": "New York",
                        "address": "321 Broadway, Apt 15A"
                    }
                ]
            }
            visit_order.append(provider_visits)
        
        return visit_order

    async def replan_appointments(
        self,
        appointment_ids: List[str],
        reason: str,
        constraints: Optional[Dict[str, Any]] = None,
        preferred_date: Optional[str] = None,
        unavailable_provider_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Replan appointments due to patient requests or provider unavailability"""

        try:
            start_time = datetime.now()

            # Get appointments that need replanning
            appointments = await self.appointment_client.get_appointments(appointment_ids)

            if not appointments:
                return {
                    "success": False,
                    "message": "No appointments found for replanning",
                    "replanned_count": 0,
                    "failed_count": 0,
                    "processing_time_seconds": 0.0,
                    "new_assignments": [],
                    "affected_appointments": []
                }

            # Get available providers (excluding unavailable one if specified)
            providers = await self.provider_client.get_providers()
            if unavailable_provider_id:
                providers = [p for p in providers if p["id"] != unavailable_provider_id]

            # Get patients for the appointments
            patient_ids = list(set(apt["patient_id"] for apt in appointments))
            patients = await self.patient_client.get_patients(patient_ids)

            # Mock replanning logic for demo
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Generate mock new assignments
            new_assignments = []
            affected_appointments = []

            for apt in appointments:
                # Find a suitable provider (not the unavailable one)
                suitable_providers = [p for p in providers if p["id"] != unavailable_provider_id]
                if suitable_providers:
                    assigned_provider = suitable_providers[0]  # Simple assignment for demo
                    new_date = preferred_date or "2025-06-26"  # Use preferred date or default

                    new_assignments.append({
                        "appointment_id": apt["id"],
                        "patient_name": next((p["name"] for p in patients if p["id"] == apt["patient_id"]), "Unknown"),
                        "provider_id": assigned_provider["id"],
                        "provider_name": f"{assigned_provider['name']}, {assigned_provider['role']}",
                        "assigned_date": new_date,
                        "reason": reason,
                        "is_pinned": apt.get("is_pinned", False)
                    })

            return {
                "success": True,
                "message": f"Successfully replanned {len(new_assignments)} appointments due to {reason}",
                "replanned_count": len(new_assignments),
                "failed_count": len(appointments) - len(new_assignments),
                "processing_time_seconds": processing_time,
                "new_assignments": new_assignments,
                "affected_appointments": affected_appointments
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Replanning failed: {str(e)}",
                "replanned_count": 0,
                "failed_count": len(appointment_ids),
                "processing_time_seconds": 0.0,
                "new_assignments": [],
                "affected_appointments": []
            }

    def _generate_mock_assignments(
        self,
        appointments: List[Dict[str, Any]],
        providers: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate mock assignment results for demo purposes"""
        assignments = []

        for i, apt in enumerate(appointments):
            # Assign provider in round-robin fashion
            provider = providers[i % len(providers)]

            # Assign date (some appointments might be pinned)
            if apt.get("is_pinned", False) and apt.get("pinned_date"):
                assigned_date = apt["pinned_date"]
                assigned_time = apt.get("pinned_time", "09:00")
            else:
                assigned_date = "2025-06-25" if i % 2 == 0 else "2025-06-26"
                assigned_time = None  # Will be assigned during day plan

            assignments.append({
                "appointment_id": apt["id"],
                "patient_id": apt["patient_id"],
                "provider_id": provider["id"],
                "provider_name": f"{provider['name']}, {provider['role']}",
                "assigned_date": assigned_date,
                "assigned_time": assigned_time,
                "duration_minutes": apt["duration_minutes"],
                "is_pinned": apt.get("is_pinned", False),
                "required_skills": apt.get("required_skills", [])
            })

        return assignments

    def _generate_mock_visit_schedules(
        self,
        providers: List[Dict[str, Any]],
        appointments: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate mock visit schedules with start times for demo purposes"""
        schedules = []

        # Mock visit schedules for first 2 providers
        for i, provider in enumerate(providers[:2]):
            visits = []
            start_hour = 8 + i  # Stagger start times

            # Add some mock visits for this provider
            for j in range(2):  # 2 visits per provider for demo
                visit_time = f"{start_hour + j*2:02d}:00"
                end_time = f"{start_hour + j*2 + 1:02d}:00"

                visits.append({
                    "appointment_id": f"apt-{i*2 + j + 1:03d}",
                    "start_time": visit_time,
                    "end_time": end_time,
                    "patient_name": f"Patient {i*2 + j + 1}",
                    "location": "New York",
                    "address": f"{123 + j} Main Street, Apt {i+1}A",
                    "duration_minutes": 60,
                    "is_pinned": False,
                    "travel_time_minutes": 15 if j > 0 else 0
                })

            schedules.append({
                "provider_id": provider["id"],
                "provider_name": f"{provider['name']}, {provider['role']}",
                "date": "2025-06-25",
                "total_visits": len(visits),
                "total_duration_minutes": sum(v["duration_minutes"] for v in visits),
                "visits": visits
            })

        return schedules
