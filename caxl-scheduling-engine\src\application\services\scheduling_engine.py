"""Core scheduling engine service"""

from datetime import datetime
from typing import List, Dict, Any, Optional

from src.infrastructure.external.patient_service_client import PatientServiceClient
from src.infrastructure.external.provider_service_client import ProviderServiceClient
from src.infrastructure.external.appointment_service_client import AppointmentServiceClient


class SchedulingEngine:
    """Core scheduling engine that orchestrates appointment assignment and day planning"""
    
    def __init__(self):
        self.patient_client = PatientServiceClient()
        self.provider_client = ProviderServiceClient()
        self.appointment_client = AppointmentServiceClient()
    
    async def assign_appointments(
        self, 
        appointment_ids: Optional[List[str]] = None,
        target_date: Optional[str] = None,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run appointment assignment optimization"""
        
        try:
            # Get appointments to schedule
            if appointment_ids:
                appointments = await self.appointment_client.get_appointments(appointment_ids)
            else:
                appointments = await self.appointment_client.get_pending_appointments()
            
            if not appointments:
                return {
                    "success": False,
                    "message": "No appointments found for scheduling",
                    "scheduled_count": 0,
                    "failed_count": 0,
                    "processing_time_seconds": 0.0,
                    "details": {}
                }
            
            # Get available providers
            providers = await self.provider_client.get_providers()
            
            if not providers:
                return {
                    "success": False,
                    "message": "No providers available",
                    "scheduled_count": 0,
                    "failed_count": len(appointments),
                    "processing_time_seconds": 0.0,
                    "details": {}
                }
            
            # Get patients for the appointments
            patient_ids = list(set(apt["patient_id"] for apt in appointments))
            patients = await self.patient_client.get_patients(patient_ids)
            
            # Convert to the format expected by the original scheduler
            scheduler_data = self._convert_to_scheduler_format(appointments, providers, patients)
            
            # Run the assignment job
            assignment_job = AssignAppointmentJob(daemon_mode=False)
            
            # Create a temporary data file for the scheduler
            import tempfile
            import yaml
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(scheduler_data, f)
                temp_file = f.name
            
            try:
                # Run the assignment
                start_time = datetime.now()
                result = assignment_job.run_assignment(temp_file)
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                
                # Update appointments with results (stub for now)
                if result.get("success", False):
                    await self.appointment_client.update_appointments(appointments)
                
                return {
                    "success": result.get("success", False),
                    "message": result.get("message", "Assignment completed"),
                    "scheduled_count": result.get("assigned_count", 0),
                    "failed_count": result.get("failed_count", 0),
                    "processing_time_seconds": processing_time,
                    "details": {
                        "algorithm": "timefold_optimization",
                        "constraints_applied": ["skill_matching", "geographic_optimization", "availability"],
                        "total_appointments": len(appointments),
                        "total_providers": len(providers)
                    }
                }
                
            finally:
                # Clean up temp file
                os.unlink(temp_file)
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Assignment failed: {str(e)}",
                "scheduled_count": 0,
                "failed_count": len(appointments) if 'appointments' in locals() else 0,
                "processing_time_seconds": 0.0,
                "details": {"error": str(e)}
            }
    
    async def run_day_plan(
        self, 
        target_date: str,
        provider_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Run day plan optimization"""
        
        try:
            # Get scheduled appointments for the target date
            appointments = await self.appointment_client.get_appointments()
            # Filter for scheduled appointments (in a real implementation, this would be done by the API)
            scheduled_appointments = [apt for apt in appointments if apt.get("status") == "scheduled"]
            
            if not scheduled_appointments:
                return {
                    "success": True,
                    "message": "No scheduled appointments found for this date",
                    "assigned_count": 0,
                    "processing_time_seconds": 0.0,
                    "visit_order": []
                }
            
            # Get providers
            if provider_ids:
                providers = await self.provider_client.get_providers(provider_ids)
            else:
                provider_ids_from_appointments = list(set(
                    apt["provider_id"] for apt in scheduled_appointments 
                    if apt.get("provider_id")
                ))
                providers = await self.provider_client.get_providers(provider_ids_from_appointments)
            
            # Get patients
            patient_ids = list(set(apt["patient_id"] for apt in scheduled_appointments))
            patients = await self.patient_client.get_patients(patient_ids)
            
            # Convert to scheduler format
            scheduler_data = self._convert_to_scheduler_format(scheduled_appointments, providers, patients)
            
            # Run the day plan job
            day_plan_job = DayPlanJob(daemon_mode=False)
            
            # Create temporary data file
            import tempfile
            import yaml
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(scheduler_data, f)
                temp_file = f.name
            
            try:
                # Run the day plan
                start_time = datetime.now()
                result = day_plan_job.run_day_plan(temp_file, target_date)
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                
                # Convert result to API format
                visit_order = self._convert_day_plan_result(result, providers, patients)
                
                return {
                    "success": result.get("success", False),
                    "message": result.get("message", "Day plan completed"),
                    "assigned_count": result.get("assigned_count", 0),
                    "processing_time_seconds": processing_time,
                    "visit_order": visit_order
                }
                
            finally:
                # Clean up temp file
                os.unlink(temp_file)
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Day plan failed: {str(e)}",
                "assigned_count": 0,
                "processing_time_seconds": 0.0,
                "visit_order": [],
                "details": {"error": str(e)}
            }
    
    def _convert_to_scheduler_format(
        self, 
        appointments: List[Dict[str, Any]], 
        providers: List[Dict[str, Any]], 
        patients: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Convert API data to the format expected by the original scheduler"""
        
        # Create patient lookup
        patient_lookup = {p["id"]: p for p in patients}
        
        # Convert appointments
        scheduler_appointments = []
        for apt in appointments:
            patient = patient_lookup.get(apt["patient_id"], {})
            scheduler_apt = {
                "id": apt["id"],
                "consumer_id": apt["patient_id"],
                "duration_minutes": apt["duration_minutes"],
                "required_skills": apt["required_skills"],
                "priority": apt.get("priority", "normal"),
                "urgent": apt.get("urgent", False),
                "location": apt.get("location", {}),
                "service_type": apt.get("service_type", "home_health")
            }
            scheduler_appointments.append(scheduler_apt)
        
        # Convert providers
        scheduler_providers = []
        for provider in providers:
            scheduler_provider = {
                "id": provider["id"],
                "name": provider["name"],
                "role": provider["role"],
                "skills": provider["skills"],
                "location": provider.get("location", {}),
                "service_radius_miles": provider.get("service_radius_miles", 25.0),
                "availability": provider.get("availability", {})
            }
            scheduler_providers.append(scheduler_provider)
        
        # Convert patients
        scheduler_patients = []
        for patient in patients:
            scheduler_patient = {
                "id": patient["id"],
                "name": patient["name"],
                "location": patient.get("location", {}),
                "care_episode_id": patient.get("care_episode_id"),
                "special_instructions": patient.get("special_instructions")
            }
            scheduler_patients.append(scheduler_patient)
        
        return {
            "appointments": scheduler_appointments,
            "providers": scheduler_providers,
            "patients": scheduler_patients
        }
    
    def _convert_day_plan_result(
        self, 
        result: Dict[str, Any], 
        providers: List[Dict[str, Any]], 
        patients: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert day plan result to API format"""
        
        # Create lookups
        provider_lookup = {p["id"]: p for p in providers}
        patient_lookup = {p["id"]: p for p in patients}
        
        visit_order = []
        
        # Mock visit order for now (in real implementation, extract from result)
        for provider in providers[:2]:  # Limit to first 2 providers for demo
            provider_visits = {
                "provider_id": provider["id"],
                "provider_name": f"{provider['name']}, {provider['role']}",
                "visits": [
                    {
                        "time": "08:00-09:00",
                        "patient": "Margaret Smith",
                        "location": "New York",
                        "address": "123 Park Avenue, Apt 4B"
                    },
                    {
                        "time": "09:30-10:45",
                        "patient": "David Kim",
                        "location": "New York",
                        "address": "321 Broadway, Apt 15A"
                    }
                ]
            }
            visit_order.append(provider_visits)
        
        return visit_order
