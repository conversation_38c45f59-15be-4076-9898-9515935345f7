from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>, UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientPreferences(BaseModel):
    __tablename__ = "patient_pref"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_pref_id", UUID(as_uuid=True), primary_key=True)
    pref_language = Column(String)
    lab_name = Column(String)
    lab_location_id = Column(UUID(as_uuid=True))
    lab_phone = Column(String)
    lab_phone_country_code = Column(String)
    lab_email = Column(String)
    pharmacy_name = Column(String)
    pharmacy_location_id = Column(UUID(as_uuid=True))
    pharmacy_phone = Column(String)
    pharmacy_email = Column(String)
    time_pref = Column(JSON)

    patient = relationship("Patient", back_populates="preferences", lazy="joined")
