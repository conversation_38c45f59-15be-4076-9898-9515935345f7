from sqlalchemy import Column, Date, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientPII(BaseModel):
    __tablename__ = "patient_pii"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_pii_id", UUID(as_uuid=True), primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    gender = Column(String)
    dob = Column(Date)
    ssn = Column(String)
    ethnicity = Column(String)
    race = Column(String)

    patient = relationship("Patient", back_populates="pii")

    emails = relationship("PatientPIIEmail", back_populates="patient_pii", lazy="joined")
    phones = relationship("PatientPIIPhone", back_populates="patient_pii", lazy="joined")
    locations = relationship("PatientPIILocation", back_populates="patient_pii", lazy="joined")
    emergency_contacts = relationship(
        "PatientEmergencyContactPII", back_populates="patient_pii", lazy="joined"
    )
