from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientPIIPhone(BaseModel):
    __tablename__ = "patient_pii_phone"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_pii_phone_id", UUID(as_uuid=True), primary_key=True)
    patient_pii_id = Column(UUID(as_uuid=True), ForeignKey("patient_pii.patient_pii_id"))
    phone_number = Column(String)
    phone_country_code = Column(String)
    phone_type = Column(String)
    is_primary = Column(Boolean)
    is_verified = Column(Boolean)
    preferred_for_sms = Column(Boolean)

    patient_pii = relationship("PatientPII", back_populates="phones", lazy="joined")
