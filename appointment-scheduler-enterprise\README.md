# Healthcare Appointment Scheduler Service

A production-ready healthcare appointment scheduling microservice built with FastAPI, Timefold optimization engine, and enterprise-grade architecture.

## 🏗️ Architecture

This service provides intelligent appointment scheduling for healthcare providers using constraint satisfaction optimization. It supports multiple service types, provider skill matching, geographic optimization, and real-time rescheduling.

### Key Features

- **Intelligent Scheduling**: Timefold-powered optimization engine
- **Multi-Service Support**: Physical therapy, skilled nursing, and more
- **Real-time API**: FastAPI with async support
- **Geographic Optimization**: Location-based provider assignment
- **Constraint Handling**: Complex business rules and preferences
- **Scalable Architecture**: Microservice-ready with Docker support

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Git

### Local Development

```bash
# Clone the repository
git clone <repository-url>
cd appointment-scheduler-enterprise

# Setup development environment
make setup-dev

# Run the service
make run-dev

# Run tests
make test

# View API documentation
open http://localhost:8000/docs
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up --build

# Production deployment
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 Project Structure

```
appointment-scheduler-enterprise/
├── app/                          # Application source code
│   ├── api/                      # FastAPI routes and models
│   ├── core/                     # Core business logic
│   ├── models/                   # Data models and schemas
│   ├── services/                 # Business services
│   └── utils/                    # Utility functions
├── tests/                        # Test suite
├── scripts/                      # Deployment and utility scripts
├── docker/                       # Docker configurations
├── docs/                         # Documentation
├── config/                       # Configuration files
├── data/                         # Sample data and scenarios
├── .github/                      # GitHub Actions workflows
├── docker-compose.yml            # Development compose
├── docker-compose.prod.yml       # Production compose
├── Dockerfile                    # Application container
├── Makefile                      # Development commands
└── pyproject.toml               # Python project configuration
```

## 🔧 Development

### Environment Setup

```bash
# Install dependencies
pip install -e ".[dev]"

# Setup pre-commit hooks
pre-commit install

# Run linting
make lint

# Run type checking
make type-check

# Run security scan
make security-check
```

### Testing

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific test categories
make test-unit
make test-integration
make test-api
```

### API Development

The service exposes a RESTful API built with FastAPI:

- **OpenAPI Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Metrics**: http://localhost:8000/metrics

## 🐳 Docker

### Development

```bash
# Build development image
docker build -f docker/Dockerfile.dev -t appointment-scheduler:dev .

# Run development container
docker run -p 8000:8000 appointment-scheduler:dev
```

### Production

```bash
# Build production image
docker build -f Dockerfile -t appointment-scheduler:latest .

# Run with environment variables
docker run -p 8000:8000 \
  -e DATABASE_URL=postgresql://... \
  -e REDIS_URL=redis://... \
  appointment-scheduler:latest
```

## 📊 Monitoring

The service includes comprehensive monitoring:

- **Health Checks**: Kubernetes-ready liveness/readiness probes
- **Metrics**: Prometheus-compatible metrics endpoint
- **Logging**: Structured JSON logging with correlation IDs
- **Tracing**: OpenTelemetry integration ready

## 🔒 Security

- **Input Validation**: Pydantic models with strict validation
- **Rate Limiting**: Configurable rate limits per endpoint
- **Authentication**: JWT token support (configurable)
- **CORS**: Configurable cross-origin resource sharing
- **Security Headers**: Comprehensive security headers

## 🌍 Environment Configuration

The service supports multiple environments through configuration:

- **Development**: Local development with hot reload
- **Testing**: Isolated test environment
- **Staging**: Pre-production environment
- **Production**: Production-ready configuration

## 📈 Performance

- **Async Support**: FastAPI with async/await
- **Connection Pooling**: Database connection management
- **Caching**: Redis-based caching layer
- **Optimization**: Timefold solver with configurable timeouts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **API Reference**: http://localhost:8000/docs
- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
