"""Consumer (Patient) API routes"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel

from src.infrastructure.external.patient_service_client import PatientServiceClient


class ConsumerResponse(BaseModel):
    """Consumer response model"""
    id: str
    name: str
    status: str
    location: Optional[dict] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    care_episode_id: Optional[str] = None
    medical_record_number: Optional[str] = None
    special_instructions: Optional[str] = None
    accessibility_needs: List[str] = []
    language_preference: str = "English"


router = APIRouter()


@router.get(
    "/",
    response_model=List[ConsumerResponse],
    status_code=status.HTTP_200_OK,
    summary="List Consumers",
    description="Get list of healthcare consumers (patients)",
)
async def list_consumers(
    status_filter: Optional[str] = Query(None, description="Filter by consumer status"),
    care_episode_id: Optional[str] = Query(None, description="Filter by care episode ID"),
) -> List[ConsumerResponse]:
    """List healthcare consumers with optional filtering"""

    # Get patients from external service
    client = PatientServiceClient()
    patients_data = await client.get_patients()

    # Convert to response format
    consumers = [
        ConsumerResponse(
            id=patient["id"],
            name=patient["name"],
            status=patient["status"],
            location=patient.get("location"),
            email=patient.get("email"),
            phone=patient.get("phone"),
            care_episode_id=patient.get("care_episode_id"),
            medical_record_number=patient.get("medical_record_number"),
            special_instructions=patient.get("special_instructions"),
            accessibility_needs=patient.get("accessibility_needs", []),
            language_preference=patient.get("language_preference", "English")
        )
        for patient in patients_data
    ]

    # Apply filters
    if status_filter:
        consumers = [c for c in consumers if c.status == status_filter]

    if care_episode_id:
        consumers = [c for c in consumers if c.care_episode_id == care_episode_id]

    return consumers


@router.get(
    "/{consumer_id}",
    response_model=ConsumerResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Consumer",
    description="Get consumer by ID",
)
async def get_consumer(consumer_id: str) -> ConsumerResponse:
    """Get consumer by ID"""
    
    # Mock response for demonstration
    return ConsumerResponse(
        id=consumer_id,
        name="Margaret Smith",
        status="active",
        location={
            "latitude": 40.758,
            "longitude": -73.9855,
            "address": "123 Park Avenue, Apt 4B, New York, NY 10016",
            "city": "New York",
            "state": "NY"
        },
        email="<EMAIL>",
        phone="(*************",
        care_episode_id="episode-001",
        medical_record_number="MRN-001",
        special_instructions="Requires assistance with mobility",
        accessibility_needs=["wheelchair_accessible"],
        language_preference="English"
    )
