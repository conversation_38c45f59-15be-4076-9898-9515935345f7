from enum import Enum


class ErrorCode(str, Enum):
    """Enum for error codes."""

    AUTHENTICATION_FAILED = "AUTH_001"
    TENANT_NOT_FOUND = "TENANT_001"
    SESSION_EXPIRED = "SESSION_001"
    SESSION_NOT_FOUND = "SESSION_002"
    INVALID_TOKEN = "INVALID_TOKEN"  # nosec
    TOKEN_EXPIRED = "TOKEN_EXPIRED"  # nosec
    USER_NOT_FOUND = "USER_001"
    INTERNAL_ERROR = "SYS_001"
    FORBIDDEN = "SYS_002"
    MISSING_REQUIRED_HEADER = "HDR_001"
    INVALID_HEADER_VALUE = "HDR_002"
    TENANT_INACTIVE = "TENANT_002"
    INVALID_RESOURCE_STATUS = "INVALID_RESOURCE_STATUS"


class ValidationError(Exception):
    """Base class for validation errors"""

    def __init__(self, message: str, field: str | None = None):
        self.message = message
        self.field = field
        super().__init__(message)


class BaseError(Exception):
    """Base exception class for all custom exceptions."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode,
        status_code: int = 400,
        details: dict | None = None,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)


class RecordStatusInvalidError(BaseError):
    def __init__(
        self,
        message: str,
        details: dict | None = None,
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_RESOURCE_STATUS,
            status_code=400,
            details=details,
        )
