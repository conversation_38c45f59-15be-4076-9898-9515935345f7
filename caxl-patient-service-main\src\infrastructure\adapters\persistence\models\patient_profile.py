from sqlalchemy import Column, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientProfile(BaseModel):
    __tablename__ = "patient_profile"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_profile_id", UUID(as_uuid=True), primary_key=True)
    insurance_name = Column(String)
    insurance_type = Column(String)
    insurance_number = Column(String)
    clinical_diagnosis = Column(Text)
    medical_history = Column(Text)
    social_history = Column(Text)
    allergies = Column(Text)
    notes = Column(Text) 

    patient = relationship("Patient", back_populates="profile", lazy="joined")