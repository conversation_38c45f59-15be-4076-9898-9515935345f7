# Appointment Scheduler Testing Guide

This guide covers how to run all the tests, tools, and validation scripts for the appointment scheduler project.

## Quick Start

1. **Setup Basic Data** (if needed):
   ```bash
   python tools/setup_basic_data.py
   ```

2. **Run All Tests**:
   ```bash
   python tests/test_runner.py
   ```

3. **Start API Server**:
   ```bash
   python -m appointment_scheduler.api.app
   # Or using the script entry point:
   api-server
   ```

## Project Structure

```
appointment-scheduler/
├── src/appointment_scheduler/     # Core application code
│   ├── jobs/                     # Scheduling jobs
│   ├── constraints/              # Optimization constraints
│   ├── api/                      # REST API
│   └── utils/                    # Utility modules
├── data/                         # Data files
│   └── scenarios/                # Test scenarios
├── tests/                        # Test suite
├── tools/                        # Analysis and setup tools
├── config/                       # Configuration files
└── docs/                         # Documentation
```

## Testing Components

### 1. Scenario Testing

**Comprehensive Scenario Runner**:
```bash
# Run all scenarios
python tests/test_runner.py

# Run specific scenario
python tests/test_runner.py --scenario basic_demo

# Run without log files
python tests/test_runner.py --no-logs
```

**Scenario Coverage Analysis**:
```bash
# Analyze scenario coverage
python tools/scenario_coverage_analyzer.py

# Save detailed report
python tools/scenario_coverage_analyzer.py --output coverage_report.json
```

### 2. Special Test Cases

**Provider Unavailability & Pinned Appointments**:
```bash
# Run all special tests
python tests/special_test_cases.py

# Run specific test
python tests/special_test_cases.py --test unavailability
python tests/special_test_cases.py --test pinned
python tests/special_test_cases.py --test cascade
```

### 3. API Testing

**Unit Tests**:
```bash
# Run API unit tests with pytest
pytest tests/test_api.py -v
```

**Integration Tests**:
```bash
# Run API integration tests
python tests/test_api.py --test-type integration

# Run performance tests
python tests/test_api.py --test-type performance --num-requests 50
```

**Manual API Testing**:
```bash
# Start the API server
python -m appointment_scheduler.api.app

# In another terminal, test endpoints:
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/patients
curl http://localhost:8000/api/v1/carestaff
```

### 4. Core Functionality Tests

**Basic Functionality**:
```bash
# Run basic functionality tests
pytest tests/test_basic_functionality.py -v
```

**Job Execution**:
```bash
# Test assignment job directly
python -m appointment_scheduler.jobs.assign_appointments

# Test day plan job directly
python -m appointment_scheduler.jobs.day_plan

# Test via scheduler
python -m appointment_scheduler.scheduler --mode once --job assign
python -m appointment_scheduler.scheduler --mode once --job dayplan
```

## Analysis Tools

### 1. Bug Analysis

**Comprehensive Bug Analysis**:
```bash
# Run bug analysis
python tools/bug_analysis.py

# Save detailed report
python tools/bug_analysis.py --output bug_report.json
```

### 2. Scenario Validation

**Validate All Scenarios**:
```bash
# Validate scenario structure and data
python -c "
from appointment_scheduler.utils.scenario_validator import ScenarioValidator
result = ScenarioValidator.validate_all_scenarios('data/scenarios')
print(f'Valid scenarios: {result[\"summary\"][\"valid_scenarios\"]}/{result[\"summary\"][\"total_scenarios\"]}')
"
```

## Setup and Configuration

### 1. Initial Setup

**Setup Basic Data Files**:
```bash
# Create minimal data files if missing
python tools/setup_basic_data.py

# Verify setup only
python tools/setup_basic_data.py --verify-only
```

### 2. Dependencies

**Install Dependencies**:
```bash
# Basic dependencies
pip install -e .

# Development dependencies
pip install -e ".[dev]"

# API dependencies
pip install -e ".[api]"

# All dependencies
pip install -e ".[dev,api]"
```

## Test Scenarios

The project includes comprehensive test scenarios covering:

### Core Dimensions
- **Skill Matching**: perfect, partial, hierarchy, missing, invalid
- **Availability**: available, unavailable, blackout, weekend, holidays
- **Location**: inside/outside service area, invalid coordinates, cross-state
- **Time Matching**: exact, windowed, flexible, strict, timezone handling
- **Working Hours**: within/outside hours, breaks, overtime, holidays
- **Appointment Overlap**: no/partial/full overlap, adjacency, duration edge cases
- **Punctuality**: on-time, early, late, deviations
- **Task Order**: sequential, parallel, dependencies, orphaned
- **Travel Optimization**: minimal travel, multiple stops, traffic
- **Load Balancing**: under/over/optimal capacity, skill-based
- **Geographic Distribution**: clustered, scattered, urban/rural
- **Preferences**: single/multiple/conflicting preferences
- **Capacity**: optimal/under/over/dynamic capacity
- **Provider Assignment**: same/different providers, continuity

### Special Cases
- **Provider Unavailability**: Sudden unavailability requiring replanning
- **Pinned Appointments**: Appointments that cannot be reassigned
- **Cascade Effects**: Changes that affect multiple appointments

## Continuous Integration

**Full Test Suite** (recommended for CI):
```bash
#!/bin/bash
# Full test pipeline

echo "🔧 Setting up basic data..."
python tools/setup_basic_data.py

echo "🔍 Running bug analysis..."
python tools/bug_analysis.py

echo "📊 Analyzing scenario coverage..."
python tools/scenario_coverage_analyzer.py

echo "🧪 Running scenario tests..."
python tests/test_runner.py

echo "🚨 Running special test cases..."
python tests/special_test_cases.py

echo "🌐 Running API tests..."
python tests/test_api.py --test-type all

echo "✅ All tests completed!"
```

## Troubleshooting

### Common Issues

1. **Missing Data Files**:
   ```bash
   python tools/setup_basic_data.py
   ```

2. **Import Errors**:
   ```bash
   pip install -e ".[dev,api]"
   ```

3. **Configuration Issues**:
   ```bash
   python tools/bug_analysis.py
   ```

4. **API Server Won't Start**:
   ```bash
   pip install fastapi uvicorn
   python -m appointment_scheduler.api.app
   ```

### Debug Mode

**Enable Debug Logging**:
```bash
# Set environment variable
export LOG_LEVEL=DEBUG

# Or modify config/scheduler.yml:
# scheduler:
#   log_level: "DEBUG"
```

**Verbose Test Output**:
```bash
# Run tests with verbose output
python tests/test_runner.py --scenario basic_demo
python tests/special_test_cases.py --test unavailability --output debug_results.json
```

## Performance Testing

**Load Testing**:
```bash
# API performance test
python tests/test_api.py --test-type performance --num-requests 100

# Scenario performance test
time python tests/test_runner.py --no-logs
```

**Memory Profiling**:
```bash
# Install memory profiler
pip install memory-profiler

# Profile job execution
python -m memory_profiler -m appointment_scheduler.jobs.assign_appointments
```

## Reporting

All test tools support JSON output for integration with CI/CD systems:

```bash
# Generate comprehensive test report
python tests/test_runner.py --output test_results.json
python tests/special_test_cases.py --output special_tests.json
python tests/test_api.py --test-type all --output api_tests.json
python tools/bug_analysis.py --output bug_analysis.json
python tools/scenario_coverage_analyzer.py --output coverage_analysis.json
```
