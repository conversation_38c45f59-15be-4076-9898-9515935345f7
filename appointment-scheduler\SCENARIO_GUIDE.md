# Healthcare Scheduling Scenarios Guide

This guide explains how to use the various test scenarios to demonstrate different features of the healthcare appointment scheduling system.

## Quick Start

### 1. List Available Scenarios
```bash
python switch_scenario.py list
```

### 2. Switch to a Scenario
```bash
python switch_scenario.py switch basic_demo
```

### 3. Run the Jobs
```bash
python -m src.appointment_scheduler.jobs.assign_appointments
python -m src.appointment_scheduler.jobs.day_plan
```

### 4. Run Demo Scripts
```bash
python demo_scenarios.py basic
python demo_scenarios.py geographic
python demo_scenarios.py continuity
```

## Available Scenarios

### 🟢 Basic Demo (`basic_demo/`)
**Purpose**: Simple demonstration of core functionality
**Best for**: Initial demos, basic feature overview
**Complexity**: Low

**Features Demonstrated**:
- Basic appointment assignment
- Provider skill matching
- Geographic service areas
- Simple workload distribution

**Data Overview**:
- **Providers**: 3 (RN, LPN, CNA)
- **Patients**: 5
- **Appointments**: 8
- **Geographic Coverage**: Manhattan, NY

**Usage**:
```bash
python switch_scenario.py switch basic_demo
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 🟡 Geographic Clustering (`geographic_clustering/`)
**Purpose**: Demonstrate geographic clustering optimization
**Best for**: Showing how the system groups nearby appointments
**Complexity**: Medium

**Features Demonstrated**:
- Geographic clustering optimization
- Service area constraints
- Provider location optimization
- Travel time minimization

**Data Overview**:
- **Providers**: 4 (spread across different areas)
- **Patients**: 12 (clustered in 3 geographic areas)
- **Appointments**: 15
- **Geographic Coverage**: 3 distinct clusters in NYC

**Geographic Clusters**:
1. **Downtown Manhattan** (Financial District)
2. **Midtown Manhattan** (Times Square area)
3. **Upper Manhattan** (Central Park area)

**Usage**:
```bash
python switch_scenario.py switch geographic_clustering
# Enable geographic clustering in config/scheduler.yml
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 🟡 Continuity of Care (`continuity_of_care/`)
**Purpose**: Demonstrate continuity of care optimization
**Best for**: Showing how the system maintains provider-patient relationships
**Complexity**: Medium

**Features Demonstrated**:
- Continuity of care optimization
- Provider-patient relationship maintenance
- Care episode grouping
- Historical assignment consideration

**Data Overview**:
- **Providers**: 3 (with established patient relationships)
- **Patients**: 6 (with multiple appointments in care episodes)
- **Appointments**: 12 (grouped by care episodes)
- **Care Episodes**: 3 distinct episodes

**Care Episodes**:
1. **Episode A**: Patient with diabetes management (4 appointments)
2. **Episode B**: Patient with wound care (3 appointments)
3. **Episode C**: Patient with mobility assistance (5 appointments)

**Usage**:
```bash
python switch_scenario.py switch continuity_of_care
# Enable continuity of care in config/scheduler.yml
python -m src.appointment_scheduler.jobs.assign_appointments
```

### 🟡 Patient Preferences (`patient_preferences/`)
**Purpose**: Demonstrate patient preference matching optimization
**Best for**: Showing how the system respects patient preferences
**Complexity**: Medium

**Features Demonstrated**:
- Patient preference matching
- Language preferences
- Gender preferences
- Provider preferences
- Cultural considerations

**Data Overview**:
- **Providers**: 5 (with diverse backgrounds and languages)
- **Patients**: 8 (with specific preferences)
- **Appointments**: 10
- **Preference Types**: Language, gender, cultural, provider-specific

**Usage**:
```bash
python switch_scenario.py switch patient_preferences
# Enable patient preferences in config/scheduler.yml
python -m src.appointment_scheduler.jobs.assign_appointments
```

## Feature Toggle Examples

### Basic Plan Demo
```yaml
# config/scheduler.yml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: false
enable_provider_capacity_management: false
enable_route_optimization: false
```

### Premium Plan Demo
```yaml
# config/scheduler.yml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: false
```

### Enterprise Plan Demo
```yaml
# config/scheduler.yml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: true
enable_advanced_traffic_integration: true
```

## Demo Scripts

### Basic Demo
```bash
python demo_scenarios.py basic
```

### Geographic Clustering Demo
```bash
python demo_scenarios.py geographic
```

### Continuity of Care Demo
```bash
python demo_scenarios.py continuity
```

### All Demos
```bash
python demo_scenarios.py all
```

### Feature Toggle Examples
```bash
python demo_scenarios.py toggles
```

## Scenario Management Commands

### List All Scenarios
```bash
python switch_scenario.py list
```

### Switch to Scenario
```bash
python switch_scenario.py switch <scenario_name>
```

### Show Current Scenario
```bash
python switch_scenario.py current
```

### Restore from Backup
```bash
python switch_scenario.py restore
```

## Demo Tips

### 1. Start Simple
Begin with `basic_demo` for new audiences to show core functionality.

### 2. Build Complexity
Progress to more advanced scenarios like `geographic_clustering` and `continuity_of_care`.

### 3. Show Feature Toggles
Demonstrate how features can be enabled/disabled by editing `config/scheduler.yml`.

### 4. Compare Results
Show before/after results with different feature configurations.

### 5. Explain Constraints
Highlight the difference between hard constraints (must be satisfied) and soft constraints (optimization goals).

### 6. Show Logs
Demonstrate the detailed logging and metrics that help understand the optimization process.

## Expected Results

### Basic Demo
- All appointments should be assigned
- Providers should be matched by skills
- Geographic constraints should be satisfied
- Basic workload balancing should be evident

### Geographic Clustering
- Providers should be assigned to patients in their nearest cluster
- Travel times should be minimized
- Geographic service areas should be respected
- Clustering should be evident in the final schedule

### Continuity of Care
- Patients should be assigned to the same provider across their care episode
- Provider-patient relationships should be maintained
- Care episodes should be grouped together
- Continuity should be prioritized over other soft constraints

### Patient Preferences
- Patients should be matched with providers who meet their preferences
- Language preferences should be respected
- Gender preferences should be considered
- Cultural considerations should be factored in
- Provider preferences should be prioritized when possible

## Troubleshooting

### Scenario Not Found
If a scenario is not found, check that the scenario directory exists in `data/scenarios/`.

### Jobs Fail
If jobs fail after switching scenarios:
1. Check that all required YAML files are present
2. Verify the YAML syntax is correct
3. Check the logs for specific error messages

### Feature Toggles Not Working
If feature toggles don't seem to work:
1. Verify the configuration in `config/scheduler.yml`
2. Check that the constraint files are properly implemented
3. Restart the jobs after changing configuration

## Advanced Usage

### Custom Scenarios
You can create custom scenarios by:
1. Creating a new directory in `data/scenarios/`
2. Adding `providers.yml`, `consumers.yml`, and `appointments.yml`
3. Creating a `README.md` with scenario description
4. Using the scenario management tools

### Scenario Comparison
To compare different scenarios:
1. Run the same job with different scenarios
2. Compare the logs and output
3. Analyze the differences in assignments and schedules

### Integration with External APIs
For scenarios that use external APIs (weather, traffic):
1. Ensure API keys are configured
2. Check network connectivity
3. Verify API quotas and limits 