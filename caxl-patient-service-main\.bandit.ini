[bandit]
# Target the whole project directory
targets: .

# Exclude tests and virtual environments
exclude: tests,venv,migrations

# Set severity and confidence levels
severity-level: medium
confidence-level: medium

# Show full path to each issue
show-absolute-path: true

# Format of output
format: screen

# List of Bandit tests to skip
# B101: assert used
# B105: hardcoded password
# B110: try/except pass
# You can remove items below based on your risk appetite
skips: B101,B105,B110

# Number of lines to consider for context around the issue
context: True
