"""
Pytest configuration and fixtures for appointment scheduler tests.
"""

import pytest
from pathlib import Path
from typing import Dict, Any
import tempfile
import shutil

from appointment_scheduler.data_loader import DataLoader


@pytest.fixture
def test_data_dir():
    """Fixture providing path to test data directory."""
    return Path(__file__).parent / "test_data"


@pytest.fixture
def basic_demo_data_dir():
    """Fixture providing path to basic demo scenario data."""
    return Path(__file__).parent.parent / "data" / "scenarios" / "basic_demo"


@pytest.fixture
def temp_data_dir():
    """Fixture providing temporary directory for test data."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def data_loader(basic_demo_data_dir):
    """Fixture providing DataLoader instance with basic demo data."""
    return DataLoader(str(basic_demo_data_dir))


@pytest.fixture
def sample_data(data_loader):
    """Fixture providing sample loaded data."""
    return data_loader.load_all_data()
