2025-06-25 09:09:43,044 - root - INFO - Logger initialized. Log file: D:\Work\Scheduler\appointment-scheduler\logs\assign_appointments_2025-06-25_09-09-43.log
2025-06-25 09:09:43,045 - __main__ - INFO - Initializing AssignAppointmentJob...
2025-06-25 09:09:43,052 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-25 09:09:43,053 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-25 09:09:43,055 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-25 09:09:46,182 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-25 09:09:46,184 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-25 09:09:46,186 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-25 09:09:47,926 - __main__ - INFO - AssignAppointment job initialized with solver config
2025-06-25 09:09:47,926 - __main__ - INFO - Running job for target date: 2025-06-25
2025-06-25 09:09:47,927 - __main__ - INFO - 🚀 === ASSIGNMENT JOB STARTED ===
2025-06-25 09:09:47,927 - __main__ - INFO - 📅 Target Date: 2025-06-25
2025-06-25 09:09:47,927 - __main__ - INFO - 🏥 Service Type: All Services
2025-06-25 09:09:47,927 - __main__ - INFO - === STAGE 1: Loading Data ===
2025-06-25 09:09:47,928 - src.appointment_scheduler.data_loader - INFO - 🚀 Starting data loading process...
2025-06-25 09:09:47,928 - src.appointment_scheduler.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-25 09:09:47,928 - src.appointment_scheduler.data_loader - INFO - Reading provider data from: data\providers.yml
2025-06-25 09:09:47,934 - src.appointment_scheduler.data_loader - INFO - ✅ Provider data loaded: 3 providers
2025-06-25 09:09:47,934 - src.appointment_scheduler.data_loader - INFO -    - Sarah Johnson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-25 09:09:47,934 - src.appointment_scheduler.data_loader - INFO -    - Michael Chen, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-25 09:09:47,934 - src.appointment_scheduler.data_loader - INFO -    - Maria Garcia, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-25 09:09:47,935 - src.appointment_scheduler.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-25 09:09:47,935 - src.appointment_scheduler.data_loader - INFO - Reading consumer data from: data\consumers.yml
2025-06-25 09:09:47,940 - src.appointment_scheduler.data_loader - INFO - ✅ Consumer data loaded: 5 consumers
2025-06-25 09:09:47,940 - src.appointment_scheduler.data_loader - INFO -    - Margaret Smith - Episode: episode-001
2025-06-25 09:09:47,941 - src.appointment_scheduler.data_loader - INFO -    - Robert Johnson - Episode: episode-002
2025-06-25 09:09:47,941 - src.appointment_scheduler.data_loader - INFO -    - Carmen Rodriguez - Episode: episode-003
2025-06-25 09:09:47,941 - src.appointment_scheduler.data_loader - INFO -    - David Kim - Episode: episode-004
2025-06-25 09:09:47,941 - src.appointment_scheduler.data_loader - INFO -    - Lisa Thompson - Episode: episode-005
2025-06-25 09:09:47,942 - src.appointment_scheduler.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-25 09:09:47,942 - src.appointment_scheduler.data_loader - INFO - Reading appointment data from: data\appointments.yml
2025-06-25 09:09:47,949 - src.appointment_scheduler.data_loader - INFO - ✅ Appointment data loaded: 8 appointments
2025-06-25 09:09:47,949 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:09:47,950 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 45min - 2025-06-25
2025-06-25 09:09:47,950 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 90min - 2025-06-25
2025-06-25 09:09:47,950 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 75min - 2025-06-25
2025-06-25 09:09:47,950 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:09:47,951 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT None - 45min - 2025-06-25
2025-06-25 09:09:47,951 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 30min - 2025-06-25
2025-06-25 09:09:47,951 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:09:47,951 - src.appointment_scheduler.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-25 09:09:47,951 - src.appointment_scheduler.data_loader - INFO - 📊 Total records loaded:
2025-06-25 09:09:47,952 - src.appointment_scheduler.data_loader - INFO -    - Providers: 3
2025-06-25 09:09:47,952 - src.appointment_scheduler.data_loader - INFO -    - Consumers: 5
2025-06-25 09:09:47,952 - src.appointment_scheduler.data_loader - INFO -    - Appointments: 8
2025-06-25 09:09:47,952 - src.appointment_scheduler.data_loader - INFO - ✅ All data loaded successfully!
2025-06-25 09:09:47,952 - __main__ - INFO - 📊 Initial data loaded: 3 providers, 5 consumers, 8 appointments
2025-06-25 09:09:47,953 - __main__ - INFO - === STAGE 2A: No service type filter applied ===
2025-06-25 09:09:47,953 - __main__ - INFO - ✅ Using all 8 appointments
2025-06-25 09:09:47,953 - __main__ - INFO - === STAGE 2B: Filtering by Date Range ===
2025-06-25 09:09:47,953 - __main__ - INFO - 📅 Date range: 2025-06-25 to 2025-07-01
2025-06-25 09:09:47,953 - __main__ - INFO - ✅ Filtered to 8 appointments for date range
2025-06-25 09:09:47,954 - __main__ - INFO - === STAGE 3: Creating Planning Entities ===
2025-06-25 09:09:47,954 - __main__ - INFO - ✅ Created 8 planning entities
2025-06-25 09:09:47,954 - __main__ - INFO - === STAGE 4: Creating Available Dates ===
2025-06-25 09:09:47,954 - __main__ - INFO - ✅ Created 5 available dates: ['2025-06-25', '2025-06-26', '2025-06-27', '2025-06-30', '2025-07-01']
2025-06-25 09:09:47,954 - __main__ - INFO - === STAGE 5: Creating Optimization Solution ===
2025-06-25 09:09:47,955 - __main__ - INFO - ✅ Solution created with 8 assignments, 5 available dates
2025-06-25 09:09:47,955 - __main__ - INFO - === STAGE 6: Starting Optimization Solver ===
2025-06-25 09:09:47,955 - __main__ - INFO - 🔧 Starting solver with 8 assignments
2025-06-25 09:09:47,955 - __main__ - INFO - 🏥 No specific service type - using default constraints
2025-06-25 09:09:47,956 - __main__ - INFO - 🏥 Using default service config: skilled_nursing
2025-06-25 09:09:47,956 - __main__ - INFO - 🔧 Creating solver instance...
2025-06-25 09:09:48,031 - __main__ - INFO - ⏱️  Solver time limit: 300 seconds
2025-06-25 09:09:48,031 - __main__ - INFO - 🚀 Starting optimization process...
2025-06-25 09:09:48,032 - __main__ - INFO -    - Applying constraint rules...
2025-06-25 09:09:48,032 - __main__ - INFO -    - Balancing workload across providers...
2025-06-25 09:09:48,032 - __main__ - INFO -    - Optimizing geographic distribution...
2025-06-25 09:09:48,033 - __main__ - INFO -    - Considering patient preferences...
2025-06-25 09:09:48,150 - timefold.solver - INFO - Solving started: time spent (39), best score (0hard/0soft), environment mode (PHASE_ASSERT), move thread count (NONE), random (JDK with seed 0).
2025-06-25 09:09:48,154 - timefold.solver - INFO - Problem scale: entity count (8), variable count (16), approximate value count (8), approximate problem scale (2,562,884,207).
2025-06-25 09:09:49,821 - timefold.solver - INFO - Construction Heuristic phase (0) ended: time spent (1717), best score (-1hard/-1soft), move evaluation speed (72/sec), step total (8).
2025-06-25 09:10:19,924 - timefold.solver - INFO - Local Search phase (1) ended: time spent (31820), best score (-1hard/-1soft), move evaluation speed (17/sec), step total (154).
2025-06-25 09:10:19,925 - timefold.solver - INFO - Solving ended: time spent (31821), best score (-1hard/-1soft), move evaluation speed (20/sec), phase total (2), environment mode (PHASE_ASSERT), move thread count (NONE).
2025-06-25 09:10:19,952 - __main__ - INFO - ✅ Solver completed successfully!
2025-06-25 09:10:19,953 - __main__ - INFO - 📊 Final score: -1hard/-1soft
2025-06-25 09:10:19,953 - __main__ - INFO - ✅ Optimization solver completed
2025-06-25 09:10:19,953 - __main__ - INFO - === STAGE 7: Processing Results ===
2025-06-25 09:10:19,953 - __main__ - INFO - 📊 Processing assignment results...
2025-06-25 09:10:19,954 - __main__ - INFO - 🔍 Analyzing assignment results...
2025-06-25 09:10:19,954 - __main__ - INFO - ✅ ASSIGNED: Margaret Smith -> Sarah Johnson, RN on 2025-06-25
2025-06-25 09:10:19,954 - __main__ - INFO - ✅ ASSIGNED: Robert Johnson -> Michael Chen, LPN on 2025-06-25
2025-06-25 09:10:19,954 - __main__ - INFO - ✅ ASSIGNED: Carmen Rodriguez -> Maria Garcia, CNA on 2025-06-25
2025-06-25 09:10:19,954 - __main__ - INFO - ✅ ASSIGNED: David Kim -> Sarah Johnson, RN on 2025-06-25
2025-06-25 09:10:19,955 - __main__ - INFO - ✅ ASSIGNED: Lisa Thompson -> Michael Chen, LPN on 2025-06-25
2025-06-25 09:10:19,955 - __main__ - INFO - ✅ ASSIGNED: Margaret Smith -> Sarah Johnson, RN on 2025-06-25
2025-06-25 09:10:19,955 - __main__ - INFO - ✅ ASSIGNED: Robert Johnson -> Michael Chen, LPN on 2025-06-25
2025-06-25 09:10:19,955 - __main__ - INFO - ✅ ASSIGNED: Carmen Rodriguez -> Maria Garcia, CNA on 2025-06-25
2025-06-25 09:10:19,955 - __main__ - INFO - 📈 Calculating assignment statistics...
2025-06-25 09:10:19,956 - __main__ - INFO - === ASSIGNMENT RESULTS SUMMARY ===
2025-06-25 09:10:19,956 - __main__ - INFO - 📊 Total appointments: 8
2025-06-25 09:10:19,956 - __main__ - INFO - ✅ Assigned: 8 (100.0%)
2025-06-25 09:10:19,956 - __main__ - INFO - ❌ Unassigned: 0
2025-06-25 09:10:19,957 - __main__ - INFO - ⏱️  Processing time: 32.03 seconds
2025-06-25 09:10:19,957 - __main__ - INFO - 📈 Final score: -1hard/-1soft
2025-06-25 09:10:19,957 - __main__ - INFO - === SERVICE TYPE STATISTICS ===
2025-06-25 09:10:19,957 - __main__ - INFO - 🏥 general: 8/8 (100.0%)
2025-06-25 09:10:19,957 - __main__ - INFO - === CONSTRAINT SUMMARY ===
2025-06-25 09:10:19,958 - __main__ - INFO - ✅ Satisfied constraints: 23
2025-06-25 09:10:19,958 - __main__ - INFO - ❌ Violated constraints: 1
2025-06-25 09:10:19,958 - __main__ - INFO -    ❌ required_provider_skills: 1
2025-06-25 09:10:19,958 - __main__ - INFO - 🎉 === ASSIGNMENT JOB COMPLETED ===
2025-06-25 09:10:19,959 - __main__ - INFO - ⏱️  Total processing time: 32.03 seconds
2025-06-25 09:10:19,959 - __main__ - INFO - Job completed.
2025-06-25 09:10:19,961 - __main__ - INFO - Job completed. Exiting...
