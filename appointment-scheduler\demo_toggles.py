#!/usr/bin/env python3
"""
Feature Toggle Demo Script for Healthcare Scheduling System

This script demonstrates each feature toggle configuration one after another.
"""

import sys
import time

def show_basic_plan():
    """Show Basic Plan feature toggles."""
    print("\n🔵 BASIC PLAN FEATURES")
    print("=" * 50)
    print("Core scheduling functionality with essential features:")
    print()
    print("Configuration (config/scheduler.yml):")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: false")
    print("  enable_provider_capacity_management: false")
    print("  enable_route_optimization: false")
    print()
    print("✅ Features Included:")
    print("  • Geographic clustering optimization")
    print("  • Continuity of care maintenance")
    print("  • Basic workload balancing")
    print()
    print("❌ Features Not Included:")
    print("  • Patient preference matching")
    print("  • Advanced capacity management")
    print("  • Route optimization")
    print("  • Traffic integration")
    print()
    print("💡 Best for: Small healthcare organizations with basic scheduling needs")

def show_premium_plan():
    """Show Premium Plan feature toggles."""
    print("\n🟡 PREMIUM PLAN FEATURES")
    print("=" * 50)
    print("Enhanced scheduling with patient preferences and capacity management:")
    print()
    print("Configuration (config/scheduler.yml):")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: false")
    print()
    print("✅ Features Included:")
    print("  • Geographic clustering optimization")
    print("  • Continuity of care maintenance")
    print("  • Advanced workload balancing")
    print("  • Patient preference matching")
    print("  • Provider capacity management")
    print()
    print("❌ Features Not Included:")
    print("  • Route optimization")
    print("  • Traffic integration")
    print()
    print("💡 Best for: Medium healthcare organizations with patient satisfaction focus")

def show_enterprise_plan():
    """Show Enterprise Plan feature toggles."""
    print("\n🟢 ENTERPRISE PLAN FEATURES")
    print("=" * 50)
    print("Full-featured scheduling with advanced optimization and traffic integration:")
    print()
    print("Configuration (config/scheduler.yml):")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: true")
    print("  enable_advanced_traffic_integration: true")
    print()
    print("✅ Features Included:")
    print("  • Geographic clustering optimization")
    print("  • Continuity of care maintenance")
    print("  • Advanced workload balancing")
    print("  • Patient preference matching")
    print("  • Provider capacity management")
    print("  • Route optimization")
    print("  • Real-time traffic integration")
    print("  • Weather impact consideration")
    print()
    print("💡 Best for: Large healthcare organizations with complex scheduling needs")

def show_custom_configuration():
    """Show how to create custom configurations."""
    print("\n⚙️  CUSTOM CONFIGURATION")
    print("=" * 50)
    print("You can mix and match features based on your specific needs:")
    print()
    print("Example 1 - Rural Healthcare:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: false")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: false")
    print("  enable_route_optimization: true")
    print()
    print("Example 2 - Urban Specialized Care:")
    print("  enable_geographic_clustering: false")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: false")
    print()
    print("Example 3 - Emergency Services:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: false")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: false")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: true")
    print()
    print("💡 Edit config/scheduler.yml to customize your configuration")

def show_feature_comparison():
    """Show a comparison table of all features."""
    print("\n📊 FEATURE COMPARISON TABLE")
    print("=" * 80)
    print(f"{'Feature':<35} {'Basic':<8} {'Premium':<8} {'Enterprise':<10}")
    print("-" * 80)
    
    features = [
        ("Geographic Clustering", "✅", "✅", "✅"),
        ("Continuity of Care", "✅", "✅", "✅"),
        ("Workload Balancing", "✅", "✅", "✅"),
        ("Patient Preferences", "❌", "✅", "✅"),
        ("Capacity Management", "❌", "✅", "✅"),
        ("Route Optimization", "❌", "❌", "✅"),
        ("Traffic Integration", "❌", "❌", "✅"),
        ("Weather Integration", "❌", "❌", "✅"),
    ]
    
    for feature, basic, premium, enterprise in features:
        print(f"{feature:<35} {basic:<8} {premium:<8} {enterprise:<10}")
    
    print("-" * 80)
    print("✅ = Included  ❌ = Not Included")

def main():
    """Main function to run all toggle demonstrations."""
    print("🏥 Healthcare Scheduling System - Feature Toggle Demo")
    print("=" * 60)
    print()
    print("This demo shows different feature toggle configurations")
    print("for the healthcare appointment scheduling system.")
    print()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python demo_toggles.py all           - Show all configurations")
        print("  python demo_toggles.py basic         - Show Basic Plan")
        print("  python demo_toggles.py premium       - Show Premium Plan")
        print("  python demo_toggles.py enterprise    - Show Enterprise Plan")
        print("  python demo_toggles.py custom        - Show custom configurations")
        print("  python demo_toggles.py comparison    - Show feature comparison")
        print()
        return
    
    command = sys.argv[1].lower()
    
    if command == "all":
        print("Running all toggle demonstrations...")
        show_basic_plan()
        time.sleep(2)
        show_premium_plan()
        time.sleep(2)
        show_enterprise_plan()
        time.sleep(2)
        show_custom_configuration()
        time.sleep(2)
        show_feature_comparison()
    elif command == "basic":
        show_basic_plan()
    elif command == "premium":
        show_premium_plan()
    elif command == "enterprise":
        show_enterprise_plan()
    elif command == "custom":
        show_custom_configuration()
    elif command == "comparison":
        show_feature_comparison()
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'all', 'basic', 'premium', 'enterprise', 'custom', or 'comparison'")

if __name__ == "__main__":
    main() 