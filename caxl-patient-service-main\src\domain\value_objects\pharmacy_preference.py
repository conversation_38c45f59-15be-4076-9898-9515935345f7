"""Pharmacy Preference Value Object"""

from dataclasses import dataclass
from uuid import UUID

from src.domain.foundations.base.value_object_base import ValueObjectBase


@dataclass(frozen=True)
class PharmacyPreference(ValueObjectBase):
    """Pharmacy preference value object"""

    name: str
    location_id: UUID
    phone: str | None
    email: str | None

    def __post_init__(self):
        if not self.name or not self.name.strip():
            raise ValueError("Pharmacy name is required")
        object.__setattr__(self, "name", self.name.strip())
