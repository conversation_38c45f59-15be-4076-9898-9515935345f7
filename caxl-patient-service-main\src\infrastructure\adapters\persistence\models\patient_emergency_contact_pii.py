from sqlalchemy import Column, Foreign<PERSON>ey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship as _relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientEmergencyContactPII(BaseModel):
    __tablename__ = "patient_emergencycontact_pii"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_emergencycontact_pii_id", UUID(as_uuid=True), primary_key=True)
    patient_pii_id = Column(UUID(as_uuid=True), ForeignKey("patient_pii.patient_pii_id"))
    relationship = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    phone_number = Column(String)
    phone_country_code = Column(String)
    email = Column(String)

    patient_pii = _relationship("PatientPII", back_populates="emergency_contacts", lazy="joined")
