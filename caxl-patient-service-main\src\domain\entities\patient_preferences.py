"""Patient Preferences Value Object"""

from typing import Any
from uuid import UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.value_objects.lab_preference import LabPreference
from src.domain.value_objects.pharmacy_preference import PharmacyPreference


class PatientPreferences(EntityBase):
    """Represents the preferences of a patient."""

    MIN_HOURS = 0
    MAX_HOURS = 23
    MIN_MINUTES = 0
    MAX_MINUTES = 59

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        pref_language: str | None = None,
        lab_preference: LabPreference | None = None,
        pharmacy_preference: PharmacyPreference | None = None,
        time_pref: dict[str, Any] | None = None,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._pref_language = pref_language
        self._lab_preference = lab_preference
        self._pharmacy_preference = pharmacy_preference
        self._time_pref = time_pref

    @property
    def pref_language(self) -> str | None:
        return self._pref_language

    @pref_language.setter
    def pref_language(self, value: str | None) -> None:
        self._pref_language = value

    @property
    def lab_preference(self) -> LabPreference | None:
        return self._lab_preference

    @lab_preference.setter
    def lab_preference(self, value: LabPreference | None) -> None:
        self._lab_preference = value

    @property
    def pharmacy_preference(self) -> PharmacyPreference | None:
        return self._pharmacy_preference

    @pharmacy_preference.setter
    def pharmacy_preference(self, value: PharmacyPreference | None) -> None:
        self._pharmacy_preference = value

    @property
    def time_pref(self) -> dict[str, Any] | None:
        return self._time_pref

    @time_pref.setter
    def time_pref(self, value: dict[str, Any] | None) -> None:
        if value and not self._is_valid_time_format(value):
            raise ValueError("Preferred contact time must be in HH:MM 24-hour format")
        self._time_pref = value

    @staticmethod
    def _is_valid_time_format(time_str: str) -> bool:
        try:
            hours, minutes = map(int, time_str.split(":"))
            return (
                PatientPreferences.MIN_HOURS <= hours <= PatientPreferences.MAX_HOURS
                and PatientPreferences.MIN_MINUTES <= minutes <= PatientPreferences.MAX_MINUTES
            )
        except (ValueError, TypeError):
            return False
