"""Patient Contact Information Value Object"""

from dataclasses import dataclass

from src.domain.entities.email import EmailInfo
from src.domain.entities.emergency_contact import EmergencyContact
from src.domain.entities.location import LocationInfo
from src.domain.entities.phone import PhoneInfo
from src.domain.foundations.base.value_object_base import ValueObjectBase


@dataclass(frozen=True)
class PatientContactInfo(ValueObjectBase):
    """Patient contact information value object"""

    emails: list[EmailInfo]
    phones: list[PhoneInfo]
    locations: list[LocationInfo]
    emergency_contacts: list[EmergencyContact]

    def __post_init__(self):
        # Validate that there's only one primary phone
        primary_phones = [p for p in self.phones if p.is_primary]
        if len(primary_phones) > 1:
            raise ValueError("Only one primary phone is allowed")

        if not self.locations or len(self.locations) == 0:
            raise ValueError("Location is required")
        if not self.phones or len(self.phones) == 0:
            raise ValueError("At least one phone number is required")
        if not self.emergency_contacts or len(self.emergency_contacts) == 0:
            raise ValueError("At least one emergency contact is required")

        # Validate that there's only one primary location
        primary_locations = [loc for loc in self.locations if loc.is_primary]
        if len(primary_locations) > 1:
            raise ValueError("Only one primary location is allowed")

    def get_primary_phone(self) -> PhoneInfo | None:
        """Get the primary phone if one exists"""
        return next((p for p in self.phones if p.is_primary), None)

    def get_primary_location(self) -> LocationInfo | None:
        """Get the primary location if one exists"""
        return next((loc for loc in self.locations if loc.is_primary), None)

    def get_verified_emails(self) -> list[EmailInfo]:
        """Get all verified emails"""
        return [email for email in self.emails if email.is_verified]

    def get_verified_phones(self) -> list[PhoneInfo]:
        """Get all verified phones"""
        return [phone for phone in self.phones if phone.is_verified]
