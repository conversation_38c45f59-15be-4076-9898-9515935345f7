service_type: home_health
required_skills:
  - "Certified Nursing Assistant (CNA)"
  - "Licensed Practical Nurse (LPN)"
  - "Home Health Aide"
  - "Personal Care"
  - "Basic Care"
  - "basic_care"
  - "personal_care"
  - "companionship"
  - "mobility_assistance"
  - "vital_signs"

geographic_radius_miles: 20.0
max_daily_appointments_per_provider: 10
max_weekly_hours_per_provider: 40

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.7
workload_balance_weight: 0.8
geographic_clustering_weight: 0.7
patient_preference_weight: 0.6
capacity_threshold_percentage: 0.95

# Service-specific settings
visit_duration_minutes: 45
requires_initial_assessment: false
allows_weekend_visits: true
emergency_response_time_hours: 12

# Geographic clustering settings
cluster_radius_miles: 10.0
max_cluster_size: 12
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 60
continuity_threshold_days: 21  # Shorter for basic care
existing_relationship_bonus: 30

# Workload balancing settings
target_daily_appointments: 8
workload_variance_tolerance: 4
overtime_penalty_multiplier: 1.2

# Patient preference settings
preferred_provider_bonus: 20
preferred_time_bonus: 10
preferred_location_bonus: 15

# Skill matching settings
strict_skill_matching: false  # More flexible for basic care
allow_skill_hierarchy: true
skill_mismatch_penalty: 100

# Time constraints
min_visit_duration_minutes: 30
max_visit_duration_minutes: 120
travel_buffer_minutes: 15
setup_time_minutes: 5

# Quality metrics
quality_score_weight: 0.2
patient_satisfaction_weight: 0.5
provider_efficiency_weight: 0.3
