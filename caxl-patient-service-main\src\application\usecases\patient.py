"""Patient use case implementation"""

from datetime import datetime
from uuid import UUID

from src.application.dtos.patient import PatientDTO, PatientListResponse
from src.application.mappers.patient_mapper import PatientMapper
from src.application.ports.input.patient_use_case_port import PatientUseCasePort
from src.config.logging import logging
from src.config.settings import settings
from src.domain.exceptions.base import ValidationError
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.repositories.patient_repository import PatientRepository
from src.infrastructure.api.schemas.auth_schemas import AuthSession

logger = logging.getLogger(__name__)


class PatientUseCase(PatientUseCasePort):
    """Patient use case implementation"""

    def __init__(self, patient_repository: PatientRepository, patient_mapper: PatientMapper):
        """Initialize use case with dependencies"""
        self._repository = patient_repository
        self._mapper = patient_mapper

    async def get_patient(self, patient_id: UUID) -> PatientDTO | None:
        """Get patient by ID"""
        patient = await self._repository.find_by_id(patient_id)
        return self._mapper.to_response(patient) if patient else None

    async def list_patients(self, page: int = 1, page_size: int = 10) -> PatientListResponse:
        """List patients with pagination"""
        patients = await self._repository.find_all(page, page_size)
        total = await self._repository.count()
        return self._mapper.to_list_response(
            entities=patients, total=total, page=page, page_size=page_size
        )

    async def create_patient(self, request: PatientDTO, context: AuthSession) -> PatientDTO:
        """Create new patient"""
        try:
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )
            # Convert DTO to domain entity
            patient = self._mapper.to_domain(dto=request, audit_stamp=audit_stamp)

            if not patient.pii:
                raise ValidationError("Patient PII is required")

            # Save patient
            saved_patient = await self._repository.save(entity=patient, audit_stamp=audit_stamp)
            return self._mapper.to_response(saved_patient)
        except Exception as e:
            raise ValidationError(f"Failed to create patient: {e!s}") from e

    async def update_patient(self, request: PatientDTO, context: AuthSession) -> PatientDTO:
        """Update existing patient"""
        try:
            # Get existing patient
            patient = await self._repository.find_by_id(request.id)
            if not patient:
                raise ValidationError(f"Patient not found with ID: {request.id}")

            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )
            if request.pii:
                pii = self._mapper.map_pii_to_domain(dto=request.pii, audit_stamp=audit_stamp)
                patient.update_pii(pii=pii)
            if request.preferences:
                preferences = self._mapper.map_preferences_to_domain(
                    dto=request.preferences, audit_stamp=audit_stamp
                )
                patient.update_preferences(new_preferences=preferences)
            if request.profile:
                profile = self._mapper.map_medical_profile_to_domain(
                    dto=request.profile, audit_stamp=audit_stamp
                )
                patient.update_profile(new_profile=profile)
            if request.caregivers:
                caregivers = self._mapper.map_caregivers_to_domain(
                    dto=request.caregivers, audit_stamp=audit_stamp
                )
                patient.update_caregivers(caregivers=caregivers)
            if request.referring_mrns:
                referring_mrns = self._mapper.map_referring_mrns_to_domain(
                    dto=request.referring_mrns, audit_stamp=audit_stamp
                )
                patient.update_referring_mrns(referring_mrns=referring_mrns)
            if request.mpu_id:
                patient.update_mpu_id(mpu_id=request.mpu_id, audit_stamp=audit_stamp)
            if request.nhid:
                patient.update_nhid(nhid=request.nhid, audit_stamp=audit_stamp)
            # Save updated patient
            saved_patient = await self._repository.update(entity=patient, audit_stamp=audit_stamp)
            return self._mapper.to_response(saved_patient)
        except Exception as e:
            raise ValidationError(f"Failed to update patient: {e!s}") from e

    async def delete_patient(self, patient_id: UUID, context: AuthSession) -> bool:
        """Delete patient"""
        try:
            # Get existing patient
            existing_patient = await self._repository.find_by_id(patient_id)
            if not existing_patient:
                raise ValidationError(f"Patient not found with ID: {patient_id}")

            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.DELETED,
            )
            # Delete patient
            return await self._repository.delete(entity_id=patient_id, audit_stamp=audit_stamp)
        except Exception as e:
            raise ValidationError(f"Failed to delete patient: {e!s}") from e
