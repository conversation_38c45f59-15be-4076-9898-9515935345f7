"""FastAPI application setup"""

from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.config.logging import logger
from src.config.settings import settings
from src.container import Container
from src.infrastructure.api.middleware.header_validation_middleware import (
    HeaderValidationMiddleware,
)
from src.infrastructure.api.routes import (
    health,
    patient,
    patient_episode_of_care,
    patient_episode_of_care_order,
)

# Initialize container
_container = Container()


# Define lifespan events
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    logger.info("Application startup")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    yield
    logger.info("Application shutdown")


def create_app() -> FastAPI:
    """Create FastAPI application"""
    app = FastAPI(
        lifespan=lifespan,
        title=settings.PROJECT_NAME,
        description="Patient service for multi-tenant applications",
        version=settings.API_V1_STR,
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ALLOW_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add header validation middleware
    app.add_middleware(
        HeaderValidationMiddleware,
        required_headers=[settings.TENANT_HEADER_NAME, "Authorization"],
        ignored_paths=[f"{settings.API_V1_STR}/health", "/docs", "/openapi.json"],
    )

    # Wire up container
    _container.wire(
        modules=[
            "src.infrastructure.api.routes.patient",
            "src.infrastructure.api.routes.health",
            "src.infrastructure.api.routes.patient_episode_of_care",
            "src.infrastructure.api.routes.patient_episode_of_care_order",
        ]
    )

    # Set container on app's state for DI access
    app.container = _container

    # Include routers
    app.include_router(health.router, prefix=settings.API_V1_STR)
    app.include_router(patient.router, prefix=settings.API_V1_STR)
    app.include_router(patient_episode_of_care.router, prefix=settings.API_V1_STR)
    app.include_router(patient_episode_of_care_order.router, prefix=settings.API_V1_STR)

    # Add global headers to OpenAPI schema
    @app.on_event("startup")
    async def add_global_headers_to_openapi():
        if app.openapi_schema:
            for path in app.openapi_schema.get("paths", {}).values():
                for method in path.values():
                    parameters = method.setdefault("parameters", [])
                    # Add x-tenant-id if not present
                    if not any(p.get("name") == settings.TENANT_HEADER_NAME for p in parameters):
                        parameters.append(
                            {
                                "name": settings.TENANT_HEADER_NAME,
                                "in": "header",
                                "required": True,
                                "schema": {"type": "string"},
                                "description": "Tenant ID",
                            }
                        )
                    # Add x-user-id if not present
                    if not any(p.get("name") == settings.USER_HEADER_NAME for p in parameters):
                        parameters.append(
                            {
                                "name": settings.USER_HEADER_NAME,
                                "in": "header",
                                "required": True,
                                "schema": {"type": "string"},
                                "description": "User ID",
                            }
                        )

    return app
