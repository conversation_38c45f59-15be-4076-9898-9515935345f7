"""SQLAlchemy model for Patient Episode of Care"""

from sqlalchemy import T<PERSON><PERSON><PERSON><PERSON>, Column, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientEpisodeOfCare(BaseModel):
    """SQLAlchemy model for Patient Episode of Care"""

    __tablename__ = "patient_episode_of_care"

    # Override the default id column from BaseModel
    id = Column("id", PGUUID(as_uuid=True), primary_key=True)
    patient_id = Column(
        PGUUID(as_uuid=True), ForeignKey("patient.patient_id", ondelete="CASCADE"), nullable=False
    )
    patient_mrn_id = Column(PGUUID(as_uuid=True), nullable=True)
    location_id = Column(PGUUID(as_uuid=True), nullable=True)
    discharge_summary = Column(Text, nullable=True)
    clinical_diagnosis = Column(Text, nullable=True)
    date_of_start_of_care = Column(TIMESTAMP(timezone=True), nullable=True)
    insurance_name = Column(String(100), nullable=True)
    insurance_type = Column(String(50), nullable=True)
    insurance_number = Column(String(50), nullable=True)

    patient = relationship("Patient", back_populates="patient_episodes_of_care", lazy="joined")
    patient_episode_of_care_orders = relationship(
        "PatientEpisodeOfCareOrder",
        back_populates="patient_episode_of_care",
        cascade="all, delete-orphan",
    )
