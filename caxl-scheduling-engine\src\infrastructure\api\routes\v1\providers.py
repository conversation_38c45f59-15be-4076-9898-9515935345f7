"""Provider API routes"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel

from src.infrastructure.external.provider_service_client import ProviderServiceClient


class ProviderResponse(BaseModel):
    """Provider response model"""
    id: str
    name: str
    role: str
    skills: List[str] = []
    status: str
    location: Optional[dict] = None
    service_radius_miles: float
    email: Optional[str] = None
    phone: Optional[str] = None


router = APIRouter()


@router.get(
    "/",
    response_model=List[ProviderResponse],
    status_code=status.HTTP_200_OK,
    summary="List Providers",
    description="Get list of healthcare providers",
)
async def list_providers(
    status_filter: Optional[str] = Query(None, description="Filter by provider status"),
    role: Optional[str] = Query(None, description="Filter by provider role"),
    skill: Optional[str] = Query(None, description="Filter by required skill"),
) -> List[ProviderResponse]:
    """List healthcare providers with optional filtering"""

    # Get providers from external service
    client = ProviderServiceClient()
    providers_data = await client.get_providers()

    # Convert to response format
    providers = [
        ProviderResponse(
            id=provider["id"],
            name=provider["name"],
            role=provider["role"],
            skills=provider["skills"],
            status=provider["status"],
            location=provider.get("location"),
            service_radius_miles=provider["service_radius_miles"],
            email=provider.get("email"),
            phone=provider.get("phone")
        )
        for provider in providers_data
    ]

    # Apply filters
    if status_filter:
        providers = [p for p in providers if p.status == status_filter]

    if role:
        providers = [p for p in providers if p.role.lower() == role.lower()]

    if skill:
        providers = [p for p in providers if skill.lower() in [s.lower() for s in p.skills]]

    return providers


@router.get(
    "/{provider_id}",
    response_model=ProviderResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Provider",
    description="Get provider by ID",
)
async def get_provider(provider_id: str) -> ProviderResponse:
    """Get provider by ID"""
    
    # Mock response for demonstration
    return ProviderResponse(
        id=provider_id,
        name="Sarah Johnson",
        role="RN",
        skills=["wound_care", "medication_administration", "patient_assessment"],
        status="active",
        location={
            "latitude": 40.7589,
            "longitude": -73.9851,
            "address": "Manhattan, NY"
        },
        service_radius_miles=25.0,
        email="<EMAIL>",
        phone="(*************"
    )


@router.get(
    "/{provider_id}/availability",
    response_model=dict,
    status_code=status.HTTP_200_OK,
    summary="Get Provider Availability",
    description="Get provider availability schedule",
)
async def get_provider_availability(provider_id: str) -> dict:
    """Get provider availability"""
    
    return {
        "provider_id": provider_id,
        "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
        "working_hours": {
            "start": "08:00",
            "end": "17:00"
        },
        "break_periods": [
            {"start": "12:00", "end": "13:00"}
        ],
        "time_off": [],
        "max_appointments_per_day": 8,
        "max_hours_per_day": 8
    }
