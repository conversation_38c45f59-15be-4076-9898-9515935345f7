# Timefold Tutorial for Appointment Scheduling

This tutorial explains how Timefold is used in the appointment scheduler project, with practical examples and best practices.

## What is Timefold?

Timefold is a constraint satisfaction solver that finds optimal solutions to complex planning problems. It uses artificial intelligence to automatically assign resources (providers) to tasks (appointments) while respecting business rules and optimizing objectives.

## Core Timefold Concepts

### 1. Planning Problem Structure

Every Timefold problem consists of:
- **Planning Entities**: Objects that can be changed (appointments)
- **Planning Variables**: Properties that the solver can modify (assigned provider, time slot)
- **Problem Facts**: Fixed data that constrains the solution (providers, time slots)
- **Planning Solution**: The complete problem definition with score

### 2. Key Annotations

#### @PlanningId
Uniquely identifies objects in the planning domain.

```python
@dataclass
class Provider:
    @PlanningId
    id: str  # Unique identifier for this provider
    name: str
    skills: List[str]
```

**Why needed**: Timefold needs to track objects during optimization.

#### @PlanningEntity
Marks objects that the solver can modify during optimization.

```python
@dataclass
@PlanningEntity
class AppointmentData:
    id: str
    consumer_id: str
    required_skills: List[str]
    
    # These are planning variables - solver will assign values
    @PlanningVariable(value_range_provider_refs=["providerRange"])
    provider: Optional[Provider] = None
    
    @PlanningVariable(value_range_provider_refs=["timeSlotRange"])
    time_slot: Optional[TimeSlot] = None
```

**Key points**:
- Only planning entities can have planning variables
- Each appointment is a planning entity because we want to assign it to a provider and time slot

#### @PlanningVariable
Defines what the solver can change to optimize the solution.

```python
@PlanningVariable(value_range_provider_refs=["providerRange"])
provider: Optional[Provider] = None
```

**Parameters**:
- `value_range_provider_refs`: Points to the collection of possible values
- The solver will try different combinations of these values

#### @PlanningSolution
The root object containing the entire planning problem.

```python
@dataclass
@PlanningSolution
class AppointmentSchedule:
    # Collection of entities to be planned
    @PlanningEntityCollectionProperty
    appointments: List[AppointmentData]
    
    # Available providers (value range for provider variable)
    @ValueRangeProvider(id="providerRange")
    @ProblemFactCollectionProperty
    providers: List[Provider]
    
    # Available time slots (value range for time_slot variable)
    @ValueRangeProvider(id="timeSlotRange")
    @ProblemFactCollectionProperty
    time_slots: List[TimeSlot]
    
    # The optimization score
    @PlanningScore
    score: Optional[HardSoftScore] = None
```

#### @ValueRangeProvider
Defines the possible values for planning variables.

```python
@ValueRangeProvider(id="providerRange")
@ProblemFactCollectionProperty
providers: List[Provider]
```

**Connection**: The `id="providerRange"` matches the `value_range_provider_refs=["providerRange"]` in the planning variable.

#### @PlanningScore
Holds the quality score of the current solution.

```python
@PlanningScore
score: Optional[HardSoftScore] = None
```

**Score Types**:
- `HardSoftScore`: Hard constraints (must be satisfied) + Soft constraints (preferences)
- Hard score: -1 for each broken hard constraint
- Soft score: Optimization objectives (higher is better)

## Working Example: Simple Appointment Assignment

Let's build a minimal appointment scheduling example:

### Step 1: Define the Domain Model

```python
from dataclasses import dataclass
from typing import List, Optional
from timefold.solver import *

@dataclass
class Provider:
    @PlanningId
    id: str
    name: str
    skills: List[str]
    max_appointments: int

@dataclass
class TimeSlot:
    @PlanningId
    id: str
    start_time: str
    end_time: str

@dataclass
@PlanningEntity
class Appointment:
    @PlanningId
    id: str
    patient_name: str
    required_skills: List[str]
    duration_minutes: int
    
    # Planning variables
    @PlanningVariable(value_range_provider_refs=["providerRange"])
    assigned_provider: Optional[Provider] = None
    
    @PlanningVariable(value_range_provider_refs=["timeSlotRange"])
    assigned_time_slot: Optional[TimeSlot] = None

@dataclass
@PlanningSolution
class ScheduleSolution:
    @PlanningEntityCollectionProperty
    appointments: List[Appointment]
    
    @ValueRangeProvider(id="providerRange")
    @ProblemFactCollectionProperty
    providers: List[Provider]
    
    @ValueRangeProvider(id="timeSlotRange")
    @ProblemFactCollectionProperty
    time_slots: List[TimeSlot]
    
    @PlanningScore
    score: Optional[HardSoftScore] = None
```

### Step 2: Define Constraints

```python
from timefold.solver.score import constraint_provider, Constraint, HardSoftScore

@constraint_provider
def appointment_constraints(constraint_factory):
    return [
        # Hard constraints (must be satisfied)
        skill_match_constraint(constraint_factory),
        provider_capacity_constraint(constraint_factory),
        time_slot_conflict_constraint(constraint_factory),
        
        # Soft constraints (optimization objectives)
        minimize_unassigned_appointments(constraint_factory),
        balance_workload(constraint_factory)
    ]

def skill_match_constraint(constraint_factory):
    """Provider must have all required skills for the appointment."""
    return (constraint_factory
            .for_each(Appointment)
            .filter(lambda apt: apt.assigned_provider is not None)
            .filter(lambda apt: not all(skill in apt.assigned_provider.skills 
                                      for skill in apt.required_skills))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint("Skill match required"))

def provider_capacity_constraint(constraint_factory):
    """Provider cannot exceed maximum appointments per day."""
    return (constraint_factory
            .for_each(Appointment)
            .filter(lambda apt: apt.assigned_provider is not None)
            .group_by(lambda apt: apt.assigned_provider, count())
            .filter(lambda provider, count: count > provider.max_appointments)
            .penalize(HardSoftScore.ONE_HARD, lambda provider, count: count - provider.max_appointments)
            .as_constraint("Provider capacity limit"))

def time_slot_conflict_constraint(constraint_factory):
    """No two appointments can use the same time slot with the same provider."""
    return (constraint_factory
            .for_each_unique_pair(Appointment)
            .filter(lambda apt1, apt2: apt1.assigned_provider is not None 
                                     and apt2.assigned_provider is not None
                                     and apt1.assigned_provider == apt2.assigned_provider
                                     and apt1.assigned_time_slot == apt2.assigned_time_slot)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint("No time slot conflicts"))

def minimize_unassigned_appointments(constraint_factory):
    """Prefer solutions with fewer unassigned appointments."""
    return (constraint_factory
            .for_each(Appointment)
            .filter(lambda apt: apt.assigned_provider is None)
            .penalize(HardSoftScore.ONE_SOFT)
            .as_constraint("Minimize unassigned"))

def balance_workload(constraint_factory):
    """Prefer balanced workload across providers."""
    return (constraint_factory
            .for_each(Appointment)
            .filter(lambda apt: apt.assigned_provider is not None)
            .group_by(lambda apt: apt.assigned_provider, count())
            .reward(HardSoftScore.ONE_SOFT, lambda provider, count: -count * count)  # Quadratic penalty
            .as_constraint("Balance workload"))
```

### Step 3: Create and Solve the Problem

```python
from timefold.solver import SolverFactory, SolverConfig
from datetime import timedelta

def solve_appointment_schedule():
    # Create sample data
    providers = [
        Provider("P1", "Dr. Smith", ["basic_care", "wound_care"], 4),
        Provider("P2", "Nurse Johnson", ["basic_care", "medication"], 6),
        Provider("P3", "Therapist Brown", ["physical_therapy"], 5)
    ]
    
    time_slots = [
        TimeSlot("T1", "09:00", "09:30"),
        TimeSlot("T2", "09:30", "10:00"),
        TimeSlot("T3", "10:00", "10:30"),
        TimeSlot("T4", "10:30", "11:00"),
        TimeSlot("T5", "11:00", "11:30")
    ]
    
    appointments = [
        Appointment("A1", "Patient Alpha", ["basic_care"], 30),
        Appointment("A2", "Patient Beta", ["wound_care"], 30),
        Appointment("A3", "Patient Gamma", ["medication"], 30),
        Appointment("A4", "Patient Delta", ["physical_therapy"], 30),
        Appointment("A5", "Patient Epsilon", ["basic_care"], 30)
    ]
    
    # Create the planning problem
    problem = ScheduleSolution(
        appointments=appointments,
        providers=providers,
        time_slots=time_slots
    )
    
    # Configure the solver
    solver_config = SolverConfig(
        solution_class=ScheduleSolution,
        entity_class_list=[Appointment],
        constraint_provider_function=appointment_constraints,
        termination_spent_limit=timedelta(seconds=30)
    )
    
    # Create and run solver
    solver_factory = SolverFactory.create(solver_config)
    solver = solver_factory.build_solver()
    
    solution = solver.solve(problem)
    
    # Print results
    print(f"Score: {solution.score}")
    for appointment in solution.appointments:
        if appointment.assigned_provider and appointment.assigned_time_slot:
            print(f"{appointment.patient_name} -> {appointment.assigned_provider.name} at {appointment.assigned_time_slot.start_time}")
        else:
            print(f"{appointment.patient_name} -> UNASSIGNED")
    
    return solution

# Run the solver
if __name__ == "__main__":
    solution = solve_appointment_schedule()
```

## How the Solver Works

### 1. Initial Solution
Timefold starts with an initial solution (often random or empty assignments).

### 2. Local Search
The solver uses algorithms like:
- **Hill Climbing**: Make small changes that improve the score
- **Tabu Search**: Avoid recently tried solutions
- **Simulated Annealing**: Sometimes accept worse solutions to escape local optima

### 3. Constraint Evaluation
For each potential solution, Timefold:
1. Evaluates all constraints
2. Calculates hard score (constraint violations)
3. Calculates soft score (optimization objectives)
4. Compares with current best solution

### 4. Termination
Solver stops when:
- Time limit reached
- Score threshold achieved
- No improvement for specified time

## Best Practices

### 1. Design Good Constraints
```python
# Good: Specific and efficient
def skill_match_constraint(constraint_factory):
    return (constraint_factory
            .for_each(Appointment)
            .filter(lambda apt: apt.assigned_provider is not None)
            .filter(lambda apt: not has_required_skills(apt))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint("Skill match"))

# Avoid: Vague or inefficient constraints
def bad_constraint(constraint_factory):
    return (constraint_factory
            .for_each(Appointment)
            .penalize(HardSoftScore.ONE_HARD, lambda apt: calculate_complex_penalty(apt))
            .as_constraint("Complex penalty"))
```

### 2. Use Appropriate Score Weights
```python
# Hard constraints: Must be satisfied
.penalize(HardSoftScore.ONE_HARD)

# Soft constraints: Preferences with different weights
.penalize(HardSoftScore.of(0, 10))  # High priority preference
.penalize(HardSoftScore.of(0, 1))   # Low priority preference
```

### 3. Optimize Performance
- Use filters to reduce constraint evaluation overhead
- Group related constraints
- Avoid complex calculations in constraint functions

### 4. Test Incrementally
Start with simple constraints and add complexity gradually:
1. Basic assignment constraints
2. Capacity constraints
3. Preference constraints
4. Optimization objectives

## Integration with Appointment Scheduler

In the actual appointment scheduler project:

1. **Domain Models** (`domain.py`): Business entities
2. **Planning Models** (`planning_models.py`): Timefold-annotated versions
3. **Constraints** (`constraints/`): Business rules and optimization
4. **Jobs** (`jobs/`): Solver execution and data integration

The solver integrates with the broader system through:
- Data loading from YAML files
- Configuration management
- Result processing and storage
- API endpoints for triggering optimization

This architecture separates business logic from optimization logic, making the system maintainable and testable.
