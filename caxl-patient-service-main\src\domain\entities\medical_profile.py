"""Medical Profile Value Object"""

from uuid import UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.value_objects.insurance import Insurance


class MedicalProfile(EntityBase):
    """Represents the medical profile of a patient."""

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        insurance: Insurance | None = None,
        clinical_diagnosis: str | None = None,
        medical_history: str | None = None,
        social_history: str | None = None,
        allergies: str | None = None,
        notes: str | None = None,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._insurance = insurance
        self._clinical_diagnosis = clinical_diagnosis
        self._medical_history = medical_history
        self._social_history = social_history
        self._allergies = allergies
        self._notes = notes

    @property
    def insurance(self) -> Insurance | None:
        return self._insurance

    @insurance.setter
    def insurance(self, value: Insurance | None) -> None:
        self._insurance = value

    @property
    def clinical_diagnosis(self) -> str | None:
        return self._clinical_diagnosis

    @clinical_diagnosis.setter
    def clinical_diagnosis(self, value: str | None) -> None:
        self._clinical_diagnosis = value

    @property
    def medical_history(self) -> str | None:
        return self._medical_history

    @medical_history.setter
    def medical_history(self, value: str | None) -> None:
        self._medical_history = value

    @property
    def social_history(self) -> str | None:
        return self._social_history

    @social_history.setter
    def social_history(self, value: str | None) -> None:
        self._social_history = value

    @property
    def allergies(self) -> str | None:
        return self._allergies

    @allergies.setter
    def allergies(self, value: str | None) -> None:
        self._allergies = value

    @property
    def notes(self) -> str | None:
        return self._notes

    @notes.setter
    def notes(self, value: str | None) -> None:
        self._notes = value
