"""Appointment API routes"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel

from src.config.settings import settings


class AppointmentResponse(BaseModel):
    """Appointment response model"""
    id: str
    consumer_id: str
    provider_id: Optional[str] = None
    appointment_date: Optional[str] = None
    scheduled_start_time: Optional[str] = None
    scheduled_end_time: Optional[str] = None
    duration_minutes: int
    status: str
    location: Optional[dict] = None
    required_skills: List[str] = []


class CreateAppointmentRequest(BaseModel):
    """Create appointment request"""
    consumer_id: str
    duration_minutes: int = 30
    required_skills: List[str] = []
    priority: str = "normal"
    urgent: bool = False


router = APIRouter()


@router.get(
    "/",
    response_model=List[AppointmentResponse],
    status_code=status.HTTP_200_OK,
    summary="List Appointments",
    description="Get list of appointments with optional filtering",
)
async def list_appointments(
    status_filter: Optional[str] = Query(None, description="Filter by appointment status"),
    provider_id: Optional[str] = Query(None, description="Filter by provider ID"),
    consumer_id: Optional[str] = Query(None, description="Filter by consumer ID"),
    date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)"),
) -> List[AppointmentResponse]:
    """List appointments with optional filtering"""
    
    # Mock data for demonstration
    mock_appointments = [
        AppointmentResponse(
            id="apt-001",
            consumer_id="consumer-001",
            provider_id="provider-001",
            appointment_date="2024-01-15",
            scheduled_start_time="08:00:00",
            scheduled_end_time="09:00:00",
            duration_minutes=60,
            status="scheduled",
            location={
                "latitude": 40.758,
                "longitude": -73.9855,
                "address": "123 Park Avenue, Apt 4B, New York, NY 10016"
            },
            required_skills=["wound_care", "medication_administration"]
        ),
        AppointmentResponse(
            id="apt-002",
            consumer_id="consumer-002",
            provider_id="provider-002",
            appointment_date="2024-01-15",
            scheduled_start_time="09:30:00",
            scheduled_end_time="10:30:00",
            duration_minutes=60,
            status="scheduled",
            location={
                "latitude": 40.7505,
                "longitude": -73.9934,
                "address": "456 Madison Avenue, Suite 8, New York, NY 10022"
            },
            required_skills=["physical_therapy"]
        )
    ]
    
    # Apply filters (simplified for demo)
    filtered_appointments = mock_appointments
    
    if status_filter:
        filtered_appointments = [apt for apt in filtered_appointments if apt.status == status_filter]
    
    if provider_id:
        filtered_appointments = [apt for apt in filtered_appointments if apt.provider_id == provider_id]
    
    if consumer_id:
        filtered_appointments = [apt for apt in filtered_appointments if apt.consumer_id == consumer_id]
    
    return filtered_appointments


@router.get(
    "/{appointment_id}",
    response_model=AppointmentResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Appointment",
    description="Get appointment by ID",
)
async def get_appointment(appointment_id: str) -> AppointmentResponse:
    """Get appointment by ID"""
    
    # Mock response for demonstration
    return AppointmentResponse(
        id=appointment_id,
        consumer_id="consumer-001",
        provider_id="provider-001",
        appointment_date="2024-01-15",
        scheduled_start_time="08:00:00",
        scheduled_end_time="09:00:00",
        duration_minutes=60,
        status="scheduled",
        location={
            "latitude": 40.758,
            "longitude": -73.9855,
            "address": "123 Park Avenue, Apt 4B, New York, NY 10016"
        },
        required_skills=["wound_care", "medication_administration"]
    )


@router.post(
    "/",
    response_model=AppointmentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Appointment",
    description="Create a new appointment",
)
async def create_appointment(request: CreateAppointmentRequest) -> AppointmentResponse:
    """Create a new appointment"""
    
    # Mock response for demonstration
    return AppointmentResponse(
        id="apt-new-001",
        consumer_id=request.consumer_id,
        provider_id=None,
        appointment_date=None,
        scheduled_start_time=None,
        scheduled_end_time=None,
        duration_minutes=request.duration_minutes,
        status="pending",
        location=None,
        required_skills=request.required_skills
    )
