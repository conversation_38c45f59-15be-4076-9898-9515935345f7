import json

from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTP<PERSON>iddleware, RequestResponseEndpoint

from src.config.logging import logging
from src.config.settings import settings
from src.domain.exceptions.base import (
    ErrorCode,
)

logger = logging.getLogger(__name__)


class HeaderValidationMiddleware(BaseHTTPMiddleware):
    """Middleware to validate required headers and handle exceptions."""

    def __init__(self, app, required_headers: list[str], ignored_paths: list[str]):
        super().__init__(app)
        self.required_headers = set(required_headers)
        self.ignored_paths = set(ignored_paths)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            # Check if path is public
            if self._is_ignored_path(request.url.path):
                return await call_next(request)

            # Validate headers and get response if there are any issues
            error_response = self._validate_request_headers(request)
            if error_response:
                return error_response

            # Store headers in request state for later use
            request.state.tenant_id = request.headers.get(settings.TENANT_HEADER_NAME)
            request.state.user_id = request.headers.get(settings.USER_HEADER_NAME)

            return await call_next(request)

        except Exception as e:
            logger.error(f"Unexpected error in header validation: {e!s}")
            return self._create_error_response(
                "Internal server error",
                ErrorCode.INTERNAL_ERROR,
                {"error": str(e)},
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _is_ignored_path(self, path: str) -> bool:
        """Check if the request path is public."""
        for ignored_path in self.ignored_paths:
            if path.startswith(ignored_path):
                return True
        return False

    def _validate_headers(self, request: Request) -> set[str]:
        """Validate required headers and return missing ones."""
        headers = request.headers
        missing = set()

        for header in self.required_headers:
            if header.lower() not in headers:
                missing.add(header)

        return missing

    def _validate_request_headers(self, request: Request) -> Response | None:
        """Validate all required headers and return error response if any issues found."""
        # Check for missing required headers
        missing_headers = self._validate_headers(request)
        if missing_headers:
            return self._create_error_response(
                "Missing required headers",
                ErrorCode.MISSING_REQUIRED_HEADER,
                {"missing_headers": list(missing_headers)},
                status.HTTP_400_BAD_REQUEST,
            )

        # Validate tenant ID
        tenant_id = request.headers.get(settings.TENANT_HEADER_NAME)
        if not tenant_id:
            return self._create_error_response(
                "Missing tenant ID",
                ErrorCode.MISSING_REQUIRED_HEADER,
                {"header": settings.TENANT_HEADER_NAME},
                status.HTTP_400_BAD_REQUEST,
            )
        if tenant_id != settings.TENANT_ID:
            return self._create_error_response(
                "Invalid tenant ID",
                ErrorCode.INVALID_HEADER_VALUE,
                {"header": settings.TENANT_HEADER_NAME, "value": tenant_id},
                status.HTTP_400_BAD_REQUEST,
            )

        # Validate user ID
        user_id = request.headers.get(settings.USER_HEADER_NAME)
        if not user_id:
            return self._create_error_response(
                "Missing user ID",
                ErrorCode.MISSING_REQUIRED_HEADER,
                {"header": settings.USER_HEADER_NAME},
                status.HTTP_400_BAD_REQUEST,
            )

        return None

    def _create_error_response(
        self, error: str, error_code: str, details: dict, status_code: int
    ) -> Response:
        """Create an error response with the given details."""
        error_response = {
            "error": error,
            "error_code": error_code,
            "details": details,
        }
        return Response(
            content=json.dumps(error_response),
            status_code=status_code,
            headers={"Content-Type": "application/json"},
        )
