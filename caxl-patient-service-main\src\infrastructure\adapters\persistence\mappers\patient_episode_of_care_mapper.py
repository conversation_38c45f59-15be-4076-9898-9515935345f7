"""Mapper for Patient Episode of Care"""

from src.config.settings import settings
from src.domain.entities.patient_episode_of_care import PatientEpisodeOfCareEntity
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.mapper_base import BaseMapper
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.infrastructure.adapters.persistence.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper,
)
from src.infrastructure.adapters.persistence.models import BaseModel
from src.infrastructure.adapters.persistence.models.patient_episode_of_care import (
    PatientEpisodeOfCare,
)


class PatientEpisodeOfCareMapper(BaseMapper[PatientEpisodeOfCareEntity, PatientEpisodeOfCare]):
    """Mapper for Patient Episode of Care"""

    def __init__(self):
        """Initialize mapper"""
        self._order_mapper = PatientEpisodeOfCareOrderMapper()

    @property
    def model(self) -> type[PatientEpisodeOfCare]:
        """Get the model class"""
        return PatientEpisodeOfCare

    def _get_audit_stamp(self, model: BaseModel):
        return AuditStamp(
            record_status=RecordStatusEnum(model.record_status),
            mod_at=model.mod_at,
            mod_by=model.mod_by,
            mod_service=model.mod_service if model.mod_service else settings.DEFAULT_MOD_SERVICE,
            tenant_id=model.tenant_id or "elara1",
        )

    def to_domain(self, model: PatientEpisodeOfCare | None) -> PatientEpisodeOfCareEntity | None:
        """Convert model to entity"""
        if not model:
            return None

        # Map orders from patient_episode_of_care_orders relationship
        orders = []
        try:
            if hasattr(model, "patient_episode_of_care_orders"):
                orders = [
                    self._order_mapper.to_domain(order)
                    for order in model.patient_episode_of_care_orders
                    if order
                ]
        except Exception:
            # If there's an error accessing orders, continue with empty list
            pass

        return PatientEpisodeOfCareEntity(
            id=model.id,
            audit_stamp=self._get_audit_stamp(model=model),
            patient_id=model.patient_id,
            patient_mrn_id=model.patient_mrn_id,
            location_id=model.location_id,
            discharge_summary=model.discharge_summary,
            clinical_diagnosis=model.clinical_diagnosis,
            date_of_start_of_care=model.date_of_start_of_care,
            insurance_name=model.insurance_name,
            insurance_type=model.insurance_type,
            insurance_number=model.insurance_number,
            orders=orders,
        )

    def to_model(self, entity: PatientEpisodeOfCareEntity | None) -> PatientEpisodeOfCare | None:
        """Convert entity to model"""
        if not entity:
            return None
        return PatientEpisodeOfCare(
            id=entity.id if hasattr(entity, "id") else None,
            patient_id=entity.patient_id,
            patient_mrn_id=entity.patient_mrn_id,
            location_id=entity.location_id,
            discharge_summary=entity.discharge_summary,
            clinical_diagnosis=entity.clinical_diagnosis,
            date_of_start_of_care=entity.date_of_start_of_care,
            insurance_name=entity.insurance_name,
            insurance_type=entity.insurance_type,
            insurance_number=entity.insurance_number,
            tenant_id=entity.audit_stamp.tenant_id,
            mod_at=entity.audit_stamp.mod_at,
            mod_by=entity.audit_stamp.mod_by,
            mod_service=entity.audit_stamp.mod_service,
            record_status=entity.audit_stamp.record_status,
        )
