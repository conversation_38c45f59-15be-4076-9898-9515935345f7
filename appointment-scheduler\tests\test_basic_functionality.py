"""
Basic functionality tests for appointment scheduler.
"""

import pytest
from pathlib import Path
import tempfile
import shutil

from appointment_scheduler.data_loader import DataLoader
from appointment_scheduler.utils.scenario_validator import <PERSON><PERSON>rioValidator
from appointment_scheduler.utils.test_helpers import TestHelpers


class TestDataLoader:
    """Test DataLoader functionality."""
    
    def test_load_basic_demo_data(self, basic_demo_data_dir):
        """Test loading basic demo scenario data."""
        data_loader = DataLoader(str(basic_demo_data_dir))
        data = data_loader.load_all_data()
        
        assert "providers" in data
        assert "consumers" in data
        assert "appointments" in data
        
        assert len(data["providers"]) > 0
        assert len(data["consumers"]) > 0
        assert len(data["appointments"]) > 0
    
    def test_load_providers(self, basic_demo_data_dir):
        """Test loading providers specifically."""
        data_loader = DataLoader(str(basic_demo_data_dir))
        providers = data_loader.load_providers()
        
        assert len(providers) > 0
        
        # Check first provider has required fields
        provider = providers[0]
        assert provider.id is not None
        assert provider.name is not None
        assert provider.skills is not None
    
    def test_load_consumers(self, basic_demo_data_dir):
        """Test loading consumers specifically."""
        data_loader = DataLoader(str(basic_demo_data_dir))
        consumers = data_loader.load_consumers()
        
        assert len(consumers) > 0
        
        # Check first consumer has required fields
        consumer = consumers[0]
        assert consumer.id is not None
        assert consumer.name is not None
        assert consumer.location is not None
    
    def test_load_appointments(self, basic_demo_data_dir):
        """Test loading appointments specifically."""
        data_loader = DataLoader(str(basic_demo_data_dir))
        appointments = data_loader.load_appointments()
        
        assert len(appointments) > 0
        
        # Check first appointment has required fields
        appointment = appointments[0]
        assert appointment.id is not None
        assert appointment.consumer_id is not None
        assert appointment.required_skills is not None


class TestScenarioValidator:
    """Test ScenarioValidator functionality."""
    
    def test_validate_basic_demo_scenario(self, basic_demo_data_dir):
        """Test validating basic demo scenario."""
        validator = ScenarioValidator(basic_demo_data_dir)
        result = validator.validate_scenario()
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_missing_directory(self):
        """Test validation with missing directory."""
        validator = ScenarioValidator("/nonexistent/path")
        result = validator.validate_scenario()
        
        assert result["valid"] is False
        assert len(result["errors"]) > 0
    
    def test_validate_incomplete_scenario(self, temp_data_dir):
        """Test validation with incomplete scenario."""
        # Create incomplete scenario (missing appointments.yml)
        (temp_data_dir / "providers.yml").write_text("providers: []")
        (temp_data_dir / "consumers.yml").write_text("consumers: []")
        
        validator = ScenarioValidator(temp_data_dir)
        result = validator.validate_scenario()
        
        assert result["valid"] is False
        assert any("appointments.yml" in error for error in result["errors"])


class TestTestHelpers:
    """Test TestHelpers functionality."""
    
    def test_create_minimal_scenario(self):
        """Test creating minimal scenario."""
        scenario = TestHelpers.create_minimal_scenario()
        
        assert "providers" in scenario
        assert "consumers" in scenario
        assert "appointments" in scenario
        
        assert len(scenario["providers"]) == 1
        assert len(scenario["consumers"]) == 1
        assert len(scenario["appointments"]) == 1
    
    def test_create_temp_scenario(self):
        """Test creating temporary scenario."""
        scenario_data = TestHelpers.create_minimal_scenario()
        temp_path = TestHelpers.create_temp_scenario(scenario_data)
        
        try:
            # Verify files were created
            assert (temp_path / "providers.yml").exists()
            assert (temp_path / "consumers.yml").exists()
            assert (temp_path / "appointments.yml").exists()
            
            # Verify data can be loaded
            data_loader = DataLoader(str(temp_path))
            data = data_loader.load_all_data()
            
            assert len(data["providers"]) == 1
            assert len(data["consumers"]) == 1
            assert len(data["appointments"]) == 1
            
        finally:
            TestHelpers.cleanup_temp_scenario(temp_path)
    
    def test_create_edge_case_scenarios(self):
        """Test creating edge case scenarios."""
        edge_cases = [
            "no_matching_skills",
            "unavailable_provider", 
            "geographic_mismatch",
            "overloaded_provider"
        ]
        
        for case_type in edge_cases:
            scenario = TestHelpers.create_edge_case_scenario(case_type)
            
            assert "providers" in scenario
            assert "consumers" in scenario
            assert "appointments" in scenario
            
            # Verify scenario has expected characteristics
            if case_type == "overloaded_provider":
                assert len(scenario["appointments"]) > 5  # Many appointments
                assert len(scenario["consumers"]) > 5  # Many consumers
            else:
                assert len(scenario["providers"]) >= 1
                assert len(scenario["consumers"]) >= 1
                assert len(scenario["appointments"]) >= 1
