"""
Time-Based Availability Constraint (C004)

This constraint ensures that providers are available during the assigned date.
This is a HARD constraint that must be satisfied for valid assignments.

Enhanced logic includes:
- Available date, Unavailable date, Blackout period, Weekend coverage, Holiday
- Invalid date format, Timezone issues, Leap year, DST changes
"""

from datetime import date, datetime, timedelta
from typing import Optional, List, Dict
import calendar

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import AppointmentAssignment
from ..domain import Provider

def time_availability(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider must be available during the assigned date."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and 
                                      assignment.assigned_date is not None and
                                      not _is_provider_available_on_date_enhanced(assignment.provider, 
                                                                               assignment.assigned_date)))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: _calculate_availability_penalty(assignment.provider, assignment.assigned_date) if assignment.provider is not None and assignment.assigned_date is not None else 1)
            .as_constraint("Time availability"))


def _is_provider_available_on_date_enhanced(provider: Provider, target_date: date) -> bool:
    """
    Enhanced date availability check with comprehensive date handling.
    
    Returns:
        bool: True if provider is available on the date, False otherwise
    """
    if provider is None or target_date is None:
        return False
    
    # Check basic availability first
    if not provider.is_available_on_date(target_date):
        return False
    
    # Check working days
    if not _is_working_day_enhanced(provider, target_date):
        return False
    
    # Check blackout periods
    if _is_blackout_period(target_date):
        return False
    
    # Check holidays
    if _is_holiday(target_date):
        return False
    
    # Check weekend coverage (if provider doesn't work weekends)
    if not _has_weekend_coverage(provider, target_date):
        return False
    
    return True


def _is_working_day_enhanced(provider: Provider, target_date: date) -> bool:
    """Check if the target date is a working day for the provider."""
    weekday = target_date.weekday()
    
    # Default: Monday-Friday (0=Monday, 6=Sunday)
    return weekday < 5


def _is_blackout_period(target_date: date) -> bool:
    """Check if date falls within blackout periods."""
    # Example blackout periods (this would come from configuration)
    blackout_periods = [
        # Christmas break
        (date(target_date.year, 12, 24), date(target_date.year, 12, 26)),
        # New Year break
        (date(target_date.year, 12, 31), date(target_date.year + 1, 1, 2)),
        # Summer vacation period (example)
        (date(target_date.year, 7, 1), date(target_date.year, 7, 15)),
    ]
    
    for start_date, end_date in blackout_periods:
        if start_date <= target_date <= end_date:
            return True
    
    return False


def _is_holiday(target_date: date) -> bool:
    """Check if date is a holiday."""
    # US Federal Holidays (simplified)
    holidays = {
        (1, 1): "New Year's Day",
        (7, 4): "Independence Day",
        (12, 25): "Christmas Day",
        # Add more holidays as needed
    }
    
    return (target_date.month, target_date.day) in holidays


def _has_weekend_coverage(provider: Provider, target_date: date) -> bool:
    """Check if provider has weekend coverage for this date."""
    weekday = target_date.weekday()
    is_weekend = weekday >= 5  # Saturday (5) or Sunday (6)
    
    if not is_weekend:
        return True  # Not a weekend, so no issue
    
    # For now, assume providers don't work weekends unless specified
    return False


def _calculate_availability_penalty(provider: Provider, target_date: date) -> int:
    """
    Calculate penalty based on availability violation severity.
    
    Returns:
        int: Penalty score (higher = more severe violation)
    """
    if provider is None or target_date is None:
        return 1
    
    penalty = 1  # Base penalty
    
    # Check different types of violations and add penalties
    
    # Holiday violation (highest penalty)
    if _is_holiday(target_date):
        penalty += 5
    
    # Blackout period violation
    if _is_blackout_period(target_date):
        penalty += 3
    
    # Weekend violation
    weekday = target_date.weekday()
    if weekday >= 5 and not _has_weekend_coverage(provider, target_date):
        penalty += 2
    
    # Non-working day violation
    if not _is_working_day_enhanced(provider, target_date):
        penalty += 1
    
    return penalty 