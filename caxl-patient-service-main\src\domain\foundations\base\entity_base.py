from __future__ import annotations

import json
import uuid
from abc import ABC, abstractmethod
from datetime import UTC, date, datetime, time
from enum import Enum
from typing import Any, Generic, TypeVar, Union, get_args, get_origin

from .audit_stamp import AuditStamp
from .record_status_enum import RecordStatusEnum

# ──────────────────────────  ENTITY BASE  ───────────────────────────
T_Ent = TypeVar("T_Ent", bound="EntityBase")


class EntityBase:
    """Root class for all domain entities."""

    id: uuid.UUID
    audit_stamp: AuditStamp

    def __init__(
        self,
        audit_stamp: AuditStamp,
        id: uuid.UUID | None = None,
    ) -> None:
        """Initializes the base entity with an ID and an audit stamp.

        Args:
            audit_stamp: The audit stamp for the entity (mandatory).
            id: The unique identifier for the entity. If None, a new UUID is generated.
        """
        self.id = id if id is not None else self.new_id()
        if not isinstance(audit_stamp, AuditStamp):
            raise TypeError(
                "audit_stamp must be an instance of "
                "src.domain.foundations.base.audit_stamp.AuditStamp"
            )
        self.audit_stamp = audit_stamp

    # ---------- utilities ----------
    @classmethod
    def new_id(cls) -> uuid.UUID:
        """Centralised UUID generation – override if you need ULIDs, KSUIDs, …"""
        return uuid.uuid4()

    def update(self, **kwargs: Any) -> None:
        """Shallow attribute update (skips immutable core fields like id and audit_stamp itself).

        Note: Updating the 'audit_stamp' field on an entity means assigning a *new*
        AuditStamp instance to self.audit_stamp. This method does not do that;
        it's for other mutable attributes of the entity.
        The concrete entity's update method should handle audit_stamp updates.
        """
        for key, value in kwargs.items():
            if key in ("id", "audit_stamp"):
                continue
            if not hasattr(self, key):
                raise AttributeError(f"{self.__class__.__name__} has no field '{key}'")
            setattr(self, key, value)

    # ---------- (de)serialisation ----------
    def _encode(self, obj: Any) -> Any:
        """Internal helper for JSON-safe conversion."""
        if obj is None or isinstance(obj, str | int | float | bool):
            return obj
        if isinstance(obj, datetime):
            if obj.tzinfo is not None and obj.tzinfo.utcoffset(obj) is not None:
                return obj.astimezone(UTC).isoformat().replace("+00:00", "Z")
            return obj.isoformat()
        if isinstance(obj, date):
            return obj.isoformat()
        if isinstance(obj, time):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, "to_dict") and callable(obj.to_dict):
            return obj.to_dict()
        if isinstance(obj, list | tuple | set):
            return [self._encode(o) for o in obj]
        raise TypeError(f"Cannot serialise {obj!r}")

    def to_dict(self) -> dict[str, Any]:
        return {k: self._encode(v) for k, v in self.__dict__.items() if not k.startswith("_")}

    @classmethod
    def from_dict(cls: type[T_Ent], data: dict[str, Any]) -> T_Ent:
        """Default generic deserialiser. Override if you need bespoke rules."""
        init_kwargs = {}

        hints = {}
        for c_ in cls.__mro__:
            if hasattr(c_, "__annotations__"):
                hints.update(c_.__annotations__)

        if "id" in data and data["id"] is not None:
            raw_id = data["id"]
            init_kwargs["id"] = uuid.UUID(raw_id) if isinstance(raw_id, str) else raw_id

        if "audit_stamp" in data and data["audit_stamp"] is not None:
            audit_stamp_data = data["audit_stamp"]
            if isinstance(audit_stamp_data, AuditStamp):
                init_kwargs["audit_stamp"] = audit_stamp_data
            elif isinstance(audit_stamp_data, dict):
                init_kwargs["audit_stamp"] = AuditStamp.from_dict(audit_stamp_data)
            else:
                raise TypeError(f"Invalid type for 'audit_stamp' data: {type(audit_stamp_data)}")
        elif "audit_stamp" not in hints:
            pass
        elif hints.get("audit_stamp") is not AuditStamp:
            pass

            # Handle 'is_active' for backward compatibility if present at the top level of data
        if "is_active" in data and "audit_stamp" in init_kwargs:
            audit_stamp_for_entity = init_kwargs["audit_stamp"]
            if isinstance(audit_stamp_for_entity.record_status, RecordStatusEnum):
                current_status = audit_stamp_for_entity.record_status
                new_record_status = None
                if data["is_active"] is False and current_status == RecordStatusEnum.ACTIVE:
                    new_record_status = RecordStatusEnum.INACTIVE
                elif data["is_active"] is True and current_status != RecordStatusEnum.ACTIVE:
                    new_record_status = RecordStatusEnum.ACTIVE

                if new_record_status is not None:
                    init_kwargs["audit_stamp"] = audit_stamp_for_entity.copy_with_update(
                        mod_by=audit_stamp_for_entity.mod_by,
                        mod_service=audit_stamp_for_entity.mod_service,
                        record_status=new_record_status,
                    )

        # Filter init_kwargs to only include what the EntityBase constructor expects (id, audit_stamp)
        final_init_kwargs = {k: v for k, v in init_kwargs.items() if k in ("id", "audit_stamp")}

        for key, value in data.items():
            if key in final_init_kwargs:
                continue

            expected = hints.get(key)
            if expected is None:
                if hasattr(cls, key) and not callable(getattr(cls, key, None)):
                    final_init_kwargs[key] = value
                continue

            origin = get_origin(expected)
            args = get_args(expected)
            is_optional_type = origin is Union and type(None) in args

            if is_optional_type:
                if value is None:
                    final_init_kwargs[key] = None
                    continue
                expected = next(t for t in args if t is not type(None))
                origin = get_origin(expected)
                args = get_args(expected)

            if value is None and not is_optional_type:
                hint_source_cls = "Unknown"
                for c_search in cls.__mro__:
                    if hasattr(c_search, "__annotations__") and key in c_search.__annotations__:
                        hint_source_cls = c_search.__name__
                        break
                raise ValueError(
                    f"NON_OPT_NONE_ERR: Field '{key}' (from {hint_source_cls}.__annotations__) expects type '{expected}' but received None."
                )

            if expected is uuid.UUID:
                final_init_kwargs[key] = uuid.UUID(value) if isinstance(value, str) else value
            elif expected is datetime:
                if isinstance(value, str):
                    if value.endswith("Z"):
                        final_init_kwargs[key] = datetime.fromisoformat(value[:-1] + "+00:00")
                    else:
                        final_init_kwargs[key] = datetime.fromisoformat(value)
                elif isinstance(value, datetime):
                    final_init_kwargs[key] = value
                else:
                    raise TypeError(f"Invalid type for datetime field '{key}': {type(value)}")
            elif isinstance(expected, type) and issubclass(expected, Enum):
                final_init_kwargs[key] = expected(value)
            elif (
                hasattr(expected, "from_dict")
                and isinstance(value, dict)
                and callable(expected.from_dict)
            ):
                final_init_kwargs[key] = expected.from_dict(value)
            elif origin in (list, tuple, set) and args and value is not None:
                inner_type = args[0]
                processed_list = []
                for item_val in value:
                    if (
                        hasattr(inner_type, "from_dict")
                        and isinstance(item_val, dict)
                        and callable(inner_type.from_dict)
                    ):
                        processed_list.append(inner_type.from_dict(item_val))
                    elif isinstance(inner_type, type) and issubclass(inner_type, Enum):
                        processed_list.append(inner_type(item_val))
                    elif inner_type is datetime and isinstance(item_val, str):
                        if item_val.endswith("Z"):
                            processed_list.append(datetime.fromisoformat(item_val[:-1] + "+00:00"))
                        else:
                            processed_list.append(datetime.fromisoformat(item_val))
                    elif inner_type is uuid.UUID and isinstance(item_val, str):
                        processed_list.append(uuid.UUID(item_val))
                    else:
                        processed_list.append(item_val)
                final_init_kwargs[key] = origin(processed_list) if origin else processed_list
            else:
                final_init_kwargs[key] = value

        if "audit_stamp" not in final_init_kwargs and "audit_stamp" in hints:
            raise ValueError(
                "Deserialization failed: 'audit_stamp' is missing or could not be processed for EntityBase."
            )

        return cls(**final_init_kwargs)

    def to_json(self, *, indent: int | None = None, ensure_ascii: bool = False) -> str:
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=ensure_ascii)

    @classmethod
    def from_json(cls: type[T_Ent], payload: str) -> T_Ent:
        return cls.from_dict(json.loads(payload))

    # ---------- validation ----------
    def validate(self) -> None:
        """Domain-specific invariants must be enforced by concrete subclasses."""
        if self.id is None:
            raise ValueError(f"{self.__class__.__name__} ID cannot be None.")
        if self.audit_stamp is None:
            raise ValueError(f"{self.__class__.__name__} audit_stamp cannot be None.")

    # ---------- round-trip test ----------
    def validate_round_trip(self) -> None:
        try:
            dict_from_obj = self.to_dict()
            obj_from_dict = self.from_dict(dict_from_obj)
            round_trip_dict = obj_from_dict.to_dict()

            if round_trip_dict != dict_from_obj:
                raise ValueError("dict ⇆ object round-trip mismatch")
        except Exception as e:
            raise ValueError(f"Round-trip validation failed for {self.__class__.__name__}: {e}")

    # ---------- dunder ----------
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id='{self.id!s}')>"


# ────────────────────────── SENTINEL DEFINITION ───────────────────────────
class _Sentinel:
    def __repr__(self):
        return "SENTINEL"


SENTINEL = _Sentinel()
_SENTINEL_DEFAULT = SENTINEL

# ────────────────────────── AUDITABLE ENTITY ───────────────────────────
ID_TYPE_AE = TypeVar("ID_TYPE_AE", bound=uuid.UUID)
T_AuditableEnt = TypeVar("T_AuditableEnt", bound="AuditableEntity")


class AuditableEntity(Generic[ID_TYPE_AE], ABC):
    """
    Base class for entities that require detailed audit trailing fields
    directly on the entity, distinct from EntityBase's AuditStamp composition.
    """

    id: ID_TYPE_AE
    tenant_id: uuid.UUID | None
    record_status: RecordStatusEnum
    version: int
    created_at: datetime
    updated_at: datetime
    created_by: str | None
    updated_by: str | None
    deleted_at: datetime | None
    deleted_by: str | None

    def __init__(
        self,
        id: ID_TYPE_AE,
        tenant_id: uuid.UUID | None = None,
        record_status: RecordStatusEnum = RecordStatusEnum.ACTIVE,
        version: int = 0,
        created_at: datetime | None = None,
        updated_at: datetime | None = None,
        created_by: str | None = None,
        updated_by: str | None = None,
        deleted_at: datetime | None = None,
        deleted_by: str | None = None,
    ) -> None:
        if id is None:
            raise ValueError(f"{self.__class__.__name__} requires an id.")
        self.id = id
        self.tenant_id = tenant_id

        if not isinstance(record_status, RecordStatusEnum):
            raise ValueError(f"record_status must be a RecordStatusEnum. Got {type(record_status)}")
        self.record_status = record_status
        self.version = version

        current_time_utc = datetime.now(UTC)
        self.created_at = created_at if created_at is not None else current_time_utc
        self.updated_at = updated_at if updated_at is not None else self.created_at
        self.created_by = created_by
        self.updated_by = updated_by if updated_by is not None else self.created_by
        self.deleted_at = deleted_at
        self.deleted_by = deleted_by

    @classmethod
    def new_id(cls) -> uuid.UUID:
        return uuid.uuid4()

    def touch_update(self, mod_by: str, mod_service: str | None = "DefaultModService") -> None:
        self.updated_at = datetime.now(UTC)
        self.updated_by = mod_by
        self.version += 1

    def touch_soft_delete(self, mod_by: str, mod_service: str | None = "DefaultModService") -> None:
        if self.record_status != RecordStatusEnum.DELETED:
            self.deleted_at = datetime.now(UTC)
            self.deleted_by = mod_by
            self.record_status = RecordStatusEnum.DELETED
            self.touch_update(mod_by, mod_service)

    def activate(self, mod_by: str, mod_service: str | None = "DefaultModService") -> None:
        if self.record_status != RecordStatusEnum.ACTIVE:
            self.record_status = RecordStatusEnum.ACTIVE
            self.deleted_at = None
            self.deleted_by = None
            self.touch_update(mod_by, mod_service)

    def deactivate(self, mod_by: str, mod_service: str | None = "DefaultModService") -> None:
        if self.record_status != RecordStatusEnum.INACTIVE:
            self.record_status = RecordStatusEnum.INACTIVE
            self.touch_update(mod_by, mod_service)

    def _ae_encode_value(self, obj: Any) -> Any:
        if obj is None or isinstance(obj, str | int | float | bool):
            return obj
        if isinstance(obj, datetime):
            if obj.tzinfo is not None and obj.tzinfo.utcoffset(obj) is not None:
                return obj.astimezone(UTC).isoformat().replace("+00:00", "Z")
            return obj.isoformat()
        if isinstance(obj, date):
            return obj.isoformat()
        if isinstance(obj, time):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, "to_dict") and callable(obj.to_dict):
            return obj.to_dict()
        if isinstance(obj, list | tuple | set):
            return [self._ae_encode_value(o) for o in obj]
        raise TypeError(
            f"AuditableEntity: Cannot serialize object of type {type(obj).__name__} with value {obj!r}"
        )

    def to_dict(self) -> dict[str, Any]:
        """Converts the AuditableEntity to a dictionary."""
        return {
            "id": self._ae_encode_value(self.id),
            "tenant_id": self._ae_encode_value(self.tenant_id),
            "record_status": self._ae_encode_value(self.record_status),
            "version": self.version,
            "created_at": self._ae_encode_value(self.created_at),
            "updated_at": self._ae_encode_value(self.updated_at),
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "deleted_at": self._ae_encode_value(self.deleted_at),
            "deleted_by": self.deleted_by,
        }

    @classmethod
    @abstractmethod
    def from_dict(cls: type[T_AuditableEnt], data: dict[str, Any]) -> T_AuditableEnt:
        """
        Abstract method. Subclasses MUST implement this to correctly reconstruct
        an entity from a dictionary, including all base AuditableEntity fields.
        """
        raise NotImplementedError(f"{cls.__name__}.from_dict must be implemented.")

    def to_json(self, *, indent: int | None = None, ensure_ascii: bool = False) -> str:
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=ensure_ascii)

    @classmethod
    def from_json(cls: type[T_AuditableEnt], payload: str) -> T_AuditableEnt:
        data = json.loads(payload)
        return cls.from_dict(data)

    def validate(self) -> None:
        """Perform basic validation for AuditableEntity. Subclasses should call super().validate() and add specific checks."""
        if not isinstance(self.id, uuid.UUID):
            raise ValueError(
                f"{self.__class__.__name__}.id must be a UUID instance. Got {type(self.id)}"
            )
        if self.tenant_id is not None and not isinstance(self.tenant_id, uuid.UUID):
            raise ValueError(
                f"{self.__class__.__name__}.tenant_id must be a UUID instance or None."
            )
        if not isinstance(self.record_status, RecordStatusEnum):
            raise ValueError(
                f"{self.__class__.__name__}.record_status ({self.record_status}) must be a RecordStatusEnum instance. Got {type(self.record_status)}"
            )
        if not isinstance(self.version, int) or self.version < 0:
            raise ValueError(f"{self.__class__.__name__}.version must be a non-negative integer.")
        if not isinstance(self.created_at, datetime):
            raise ValueError(f"{self.__class__.__name__}.created_at must be a datetime instance.")
        if not isinstance(self.updated_at, datetime):
            raise ValueError(f"{self.__class__.__name__}.updated_at must be a datetime instance.")

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, self.__class__):
            return NotImplemented
        return self.id == other.id

    def __hash__(self) -> int:
        return hash(self.id)

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id!r})>"
