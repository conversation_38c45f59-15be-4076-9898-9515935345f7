# CareAXL Patient Service

A FastAPI-based patient management service that handles patient data, care episodes, caregivers, and emergency contacts. The service provides a comprehensive interface for managing patient-related operations in the GrowthAXL ecosystem.

## Features

- Patient CRUD operations
- Care episode tracking
- Caregiver management
- Emergency contact management
- Comprehensive test coverage
- Containerized deployment
- Middleware for authentication and authorization
- Multi-tenant support

## Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL database
- Google Cloud Platform account (for deployment)

## Local Development Setup

1. Create and activate a virtual environment:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install .[dev]
   pre-commit install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Run the development server:
   ```bash
   uvicorn src.main:app --reload
   ```

## Testing

### Running Tests

1. Unit Tests:
   ```bash
   pytest tests/unit
   ```

2. All Tests with Coverage:
   ```bash
   pytest --cov=src --cov-report=term-missing --cov-report=html
   ```

## Deployment

Deployment to Dev/Staging/Prod environments will be taken care by pipelines.

### Docker Build

```bash
docker build -t caxl-patient-service .
```

## API Documentation

Once the service is running, access the API documentation at:
- Swagger UI: `http://localhost:8002/docs`
- ReDoc: `http://localhost:8002/redoc`

## Core Functionality

### Patient Management

The service provides comprehensive patient management capabilities:

- Create, read, update, and delete patient records
- Search patients by ID or MRN
- Manage patient demographics and personal information
- Handle contact information and medical profiles
- Manage patient preferences and referring information

### Care Episode Management

- Track active care episodes
- Record clinical diagnoses and discharge summaries
- Associate episodes with locations and patients

### Caregiver Management

- Add and manage patient caregivers
- Track caregiver relationships
- Manage primary caregiver assignments
- Handle caregiver contact information

### Emergency Contact Management

- Manage patient emergency contacts
- Track contact relationships and priority
- Handle contact information and availability

## Domain Model

The service implements a rich domain model with:

- Patient aggregate root
- Value objects for personal information, contact details, and medical profiles
- Entities for care episodes, caregivers, and emergency contacts
- Domain events for important state changes
- Domain services for business logic validation

## Security

- JWT token validation
- CORS configuration for secure cross-origin requests
- Secure cookie settings
- HTTPS enforcement in production
- Role-based access control

## Monitoring and Logging

- Health check endpoint at `/health`
- Structured logging for all operations
- Performance metrics collection
- Error tracking and reporting

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check PostgreSQL connection settings
   - Verify database migrations
   - Check connection pool settings

2. **Authentication Issues**
   - Verify JWT token configuration
   - Check authentication middleware settings
   - Validate token expiration

3. **Deployment Issues**
   - Check Cloud Build logs
   - Verify Kubernetes pod status
   - Check service connectivity
   - Verify environment variables

### Logging

- Set `LOG_LEVEL=DEBUG` for detailed logging
- Monitor application logs for errors
- Check database query logs
- Review authentication logs
