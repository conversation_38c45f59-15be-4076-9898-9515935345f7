"""Value Object for Patient Episode of Care Order Directive"""

from dataclasses import dataclass
from uuid import UUID


@dataclass(frozen=True)
class PatientEpisodeOfCareOrderDirective:
    """Value Object for Patient Episode of Care Order Directive"""

    patient_episode_of_care_order_id: UUID
    instructions: str
    id: UUID | None = None

    def __post_init__(self):
        """Validate the value object after initialization"""
        if not self.patient_episode_of_care_order_id:
            raise ValueError("Patient episode of care order ID is required")
        if not self.instructions:
            raise ValueError("Instructions are required")
        if not isinstance(self.instructions, str):
            raise ValueError("Instructions must be a string")
        if not self.instructions.strip():
            raise ValueError("Instructions cannot be empty or whitespace")
