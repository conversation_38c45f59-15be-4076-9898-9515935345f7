#!/usr/bin/env python3
"""
Setup Basic Data Files

Creates basic data files if they don't exist, using the basic_demo scenario as a template.
"""

import sys
import shutil
from pathlib import Path
import yaml


def setup_basic_data(project_root: str = "."):
    """Setup basic data files for the scheduler."""
    project_root = Path(project_root)
    data_dir = project_root / "data"
    scenarios_dir = data_dir / "scenarios"
    basic_demo_dir = scenarios_dir / "basic_demo"
    
    print("🔧 Setting up basic data files...")
    
    # Create data directory if it doesn't exist
    data_dir.mkdir(exist_ok=True)
    
    # Check if basic data files exist
    required_files = ["providers.yml", "consumers.yml", "appointments.yml"]
    missing_files = [f for f in required_files if not (data_dir / f).exists()]
    
    if not missing_files:
        print("✅ All basic data files already exist")
        return True
    
    # Try to copy from basic_demo scenario
    if basic_demo_dir.exists():
        print(f"📋 Copying missing files from basic_demo scenario...")
        
        for file_name in missing_files:
            source_file = basic_demo_dir / file_name
            target_file = data_dir / file_name
            
            if source_file.exists():
                shutil.copy2(source_file, target_file)
                print(f"  ✅ Copied {file_name}")
            else:
                print(f"  ⚠️  {file_name} not found in basic_demo")
                create_minimal_data_file(target_file, file_name)
    else:
        print("📝 Creating minimal data files...")
        for file_name in missing_files:
            target_file = data_dir / file_name
            create_minimal_data_file(target_file, file_name)
    
    print("✅ Basic data setup completed")
    return True


def create_minimal_data_file(file_path: Path, file_name: str):
    """Create a minimal data file."""
    if file_name == "providers.yml":
        data = {
            "providers": [
                {
                    "id": "minimal-provider-001",
                    "name": "Default Provider, RN",
                    "role": "RN",
                    "skills": ["basic_care", "medication_management"],
                    "home_location": {
                        "latitude": 40.7589,
                        "longitude": -73.9851,
                        "city": "New York",
                        "state": "NY",
                        "address": "123 Main Street, New York, NY 10001"
                    },
                    "availability": {
                        "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                        "working_hours": ["09:00", "17:00"]
                    },
                    "capacity": {
                        "max_hours_per_day": 8,
                        "max_tasks_count_in_day": 6
                    }
                }
            ]
        }
    elif file_name == "consumers.yml":
        data = {
            "consumers": [
                {
                    "id": "minimal-consumer-001",
                    "name": "Default Patient",
                    "location": {
                        "latitude": 40.7580,
                        "longitude": -73.9855,
                        "city": "New York",
                        "state": "NY",
                        "address": "456 Patient Street, New York, NY 10002"
                    },
                    "care_episode_id": "episode-minimal-001",
                    "consumer_preferences": {
                        "preferred_days": ["monday", "wednesday", "friday"],
                        "preferred_hours": ["10:00", "16:00"],
                        "language": "English"
                    }
                }
            ]
        }
    elif file_name == "appointments.yml":
        data = {
            "appointments": [
                {
                    "consumer_id": "minimal-consumer-001",
                    "required_skills": ["basic_care"],
                    "duration_min": 30,
                    "appointment_date": "2024-01-15",
                    "priority": "normal",
                    "location": {
                        "latitude": 40.7580,
                        "longitude": -73.9855,
                        "city": "New York",
                        "state": "NY"
                    }
                }
            ]
        }
    else:
        data = {}
    
    with open(file_path, 'w') as f:
        yaml.dump(data, f, default_flow_style=False, sort_keys=False)
    
    print(f"  ✅ Created minimal {file_name}")


def setup_config_files(project_root: str = "."):
    """Setup basic configuration files."""
    project_root = Path(project_root)
    config_dir = project_root / "config"
    
    print("⚙️  Setting up configuration files...")
    
    # Create config directory if it doesn't exist
    config_dir.mkdir(exist_ok=True)
    
    # Create basic scheduler config if it doesn't exist
    scheduler_config = config_dir / "scheduler.yml"
    if not scheduler_config.exists():
        config_data = {
            "scheduler": {
                "max_solving_time_seconds": 120,
                "log_level": "INFO",
                "daemon_check_interval_seconds": 60
            }
        }
        
        with open(scheduler_config, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        
        print("  ✅ Created scheduler.yml")
    
    # Create basic service config if it doesn't exist
    service_config = config_dir / "physical_therapy.yml"
    if not service_config.exists():
        config_data = {
            "service_config": {
                "service_type": "physical_therapy",
                "required_skills": ["basic_care"],
                "geographic_radius_miles": 25.0,
                "max_daily_appointments_per_provider": 8,
                "max_weekly_hours_per_provider": 40,
                "continuity_weight": 0.7,
                "workload_balance_weight": 0.6,
                "geographic_clustering_weight": 0.4,
                "patient_preference_weight": 0.7,
                "capacity_threshold_percentage": 0.9
            }
        }
        
        with open(service_config, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        
        print("  ✅ Created physical_therapy.yml")
    
    print("✅ Configuration setup completed")


def verify_setup(project_root: str = "."):
    """Verify that the setup is working."""
    project_root = Path(project_root)
    
    print("🔍 Verifying setup...")
    
    try:
        # Add src to path for imports
        sys.path.insert(0, str(project_root / "src"))
        
        # Try to import and use DataLoader
        from appointment_scheduler.data_loader import DataLoader
        
        data_loader = DataLoader(str(project_root / "data"))
        data = data_loader.load_all_data()
        
        print(f"  ✅ DataLoader working: {len(data['providers'])} providers, {len(data['consumers'])} consumers, {len(data['appointments'])} appointments")
        
        # Try to import jobs
        from appointment_scheduler.jobs.assign_appointments import AssignAppointmentJob
        from appointment_scheduler.jobs.day_plan import DayPlanJob
        
        print("  ✅ Job classes can be imported")
        
        # Try to import API
        try:
            from appointment_scheduler.api.app import create_app
            print("  ✅ API can be imported")
        except ImportError as e:
            print(f"  ⚠️  API import issue (may need fastapi): {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Verification failed: {e}")
        return False


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Setup basic data and config files')
    parser.add_argument('--project-root', default='.', help='Project root directory')
    parser.add_argument('--verify-only', action='store_true', help='Only verify setup, do not create files')
    
    args = parser.parse_args()
    
    try:
        if args.verify_only:
            success = verify_setup(args.project_root)
        else:
            setup_basic_data(args.project_root)
            setup_config_files(args.project_root)
            success = verify_setup(args.project_root)
        
        if success:
            print("\n✅ Setup completed successfully!")
        else:
            print("\n❌ Setup verification failed")
            sys.exit(1)
    
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
