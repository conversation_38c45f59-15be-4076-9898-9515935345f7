"""Patient repository implementation"""

from src.domain.aggregates.patient import Patient
from src.domain.repositories.patient_repository import PatientRepository
from src.infrastructure.adapters.persistence.mappers.patient_mapper import PatientMapper
from src.infrastructure.adapters.persistence.models import Patient as PatientModel
from src.infrastructure.adapters.persistence.repositories.base_repository import BaseRepositoryImpl
from src.infrastructure.adapters.persistence.session import SessionManager


class PatientRepositoryImpl(BaseRepositoryImpl[Patient, PatientModel], PatientRepository):
    """Implementation of patient repository"""

    def __init__(self, session_manager: SessionManager, mapper: PatientMapper):
        """Initialize repository with session factory and mapper"""
        super().__init__(session_manager, mapper)
