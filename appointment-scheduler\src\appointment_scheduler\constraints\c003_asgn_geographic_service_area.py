"""
Geographic Service Area Constraint (C003)

This constraint ensures that providers are within reasonable service area of patient location.
This is a SOFT constraint for optimization preferences.

Enhanced logic includes:
- Polygon-based geofences (primary)
- Circular radius fallback
- Multiple areas, Boundary case, Cross-state
- Invalid coordinates, Null location, International, Rural areas
"""

from math import radians, cos, sin, asin, sqrt
from typing import Optional, Dict, List, Tuple

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import AppointmentAssignment
from ..domain import Provider, Location

def geographic_service_area(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider should be within reasonable service area of patient location."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and 
                                      assignment.appointment_data.location is not None and
                                      not _is_within_geofence(assignment.provider, 
                                                             assignment.appointment_data.location)))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: _calculate_geographic_penalty(assignment.provider, assignment.appointment_data.location) if assignment.provider is not None and assignment.appointment_data.location is not None else 1)
            .as_constraint("Geographic service area"))


def _is_within_geofence(provider: Provider, appointment_location: Location) -> bool:
    """
    Check if consumer location is within provider's geofence.
    Supports both polygon-based geofences and circular radius fallback.
    
    Returns:
        bool: True if provider is within service area, False otherwise
    """
    if provider is None or appointment_location is None:
        return True  # Default to True if location data is missing
    
    if provider.home_location is None:
        return True  # Provider has no location, assume they can travel
    
    # First, try polygon-based geofence check
    if _has_polygon_geofence(provider):
        return _is_point_in_polygon(appointment_location, _get_provider_geofence_polygon(provider))
    
    # Fallback to circular radius check
    return _is_within_service_radius_enhanced(provider, appointment_location)


def _has_polygon_geofence(provider: Provider) -> bool:
    """Check if provider has a polygon-based geofence defined."""
    # This would check if provider has geofence polygon data
    # For now, we'll check if provider has a geofence_polygon attribute
    return hasattr(provider, 'geofence_polygon') and provider.geofence_polygon is not None


def _get_provider_geofence_polygon(provider: Provider) -> List[Tuple[float, float]]:
    """
    Get provider's geofence polygon coordinates.
    
    Returns:
        List[Tuple[float, float]]: List of (latitude, longitude) coordinates defining the polygon
    """
    if not _has_polygon_geofence(provider):
        return []
    
    # This would return the actual polygon coordinates from the provider
    # For now, return a placeholder - in real implementation, this would come from:
    # - provider.geofence_polygon.coordinates
    # - provider.service_areas[0].polygon.coordinates
    # - provider.geofence_data.polygon_points
    
    return provider.geofence_polygon


def _is_point_in_polygon(point: Location, polygon: List[Tuple[float, float]]) -> bool:
    """
    Check if a point is inside a polygon using the Ray Casting Algorithm.
    
    Args:
        point: Location object with latitude and longitude
        polygon: List of (latitude, longitude) tuples defining the polygon
    
    Returns:
        bool: True if point is inside polygon, False otherwise
    """
    if not polygon or len(polygon) < 3:
        return False  # Invalid polygon
    
    x, y = point.longitude, point.latitude
    n = len(polygon)
    inside = False
    
    p1x, p1y = polygon[0]
    for i in range(n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside


def _is_within_service_radius_enhanced(provider: Provider, appointment_location: Location) -> bool:
    """
    Enhanced geographic service area check with multiple considerations.
    Fallback method when polygon geofence is not available.
    
    Returns:
        bool: True if provider is within service area, False otherwise
    """
    if provider is None or appointment_location is None:
        return True  # Default to True if location data is missing
    
    if provider.home_location is None:
        return True  # Provider has no location, assume they can travel
    
    # Calculate base distance
    distance = _calculate_distance_enhanced(provider.home_location, appointment_location)
    
    # Get service radius based on provider type and area type
    service_radius = _get_service_radius(provider, appointment_location)
    
    return distance <= service_radius


def _calculate_distance_enhanced(loc1: Location, loc2: Location) -> float:
    """
    Enhanced distance calculation with coordinate validation.
    
    Returns:
        float: Distance in miles
    """
    if loc1 is None or loc2 is None:
        return 0.0
    
    # Validate coordinates
    if not _are_valid_coordinates(loc1) or not _are_valid_coordinates(loc2):
        return 0.0
    
    # Convert decimal degrees to radians
    lat1, lon1 = radians(loc1.latitude), radians(loc1.longitude)
    lat2, lon2 = radians(loc2.latitude), radians(loc2.longitude)
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    
    # Radius of Earth in miles
    r = 3956
    
    return c * r


def _are_valid_coordinates(location: Location) -> bool:
    """Check if coordinates are valid."""
    if location is None:
        return False
    
    # Check latitude range (-90 to 90)
    if not (-90 <= location.latitude <= 90):
        return False
    
    # Check longitude range (-180 to 180)
    if not (-180 <= location.longitude <= 180):
        return False
    
    return True


def _get_service_radius(provider: Provider, appointment_location: Location) -> float:
    """
    Get service radius based on provider type and area characteristics.
    Used as fallback when polygon geofence is not available.
    
    Returns:
        float: Service radius in miles
    """
    base_radius = 25.0  # Default radius
    
    # Adjust based on provider type
    provider_type_multipliers = {
        "doctor": 1.5,      # Doctors can travel further
        "nurse": 1.2,       # Nurses have moderate range
        "cna": 0.8,         # CNAs have shorter range
        "pt": 1.3,          # Physical therapists need to travel
        "lpn": 1.0,         # LPNs have standard range
    }
    
    if hasattr(provider, 'role') and provider.role:
        role = provider.role.lower()
        for provider_type, multiplier in provider_type_multipliers.items():
            if provider_type in role:
                base_radius *= multiplier
                break
    
    # Adjust based on area type (rural vs urban)
    if _is_rural_area(appointment_location):
        base_radius *= 1.5  # Rural areas need larger service radius
    elif _is_urban_area(appointment_location):
        base_radius *= 0.8  # Urban areas can have smaller radius
    
    # Adjust for cross-state boundaries
    if (provider.home_location is not None and 
        _is_cross_state_boundary(provider.home_location, appointment_location)):
        base_radius *= 0.7  # Reduce radius for cross-state assignments
    
    return base_radius


def _is_rural_area(location: Location) -> bool:
    """Determine if location is in a rural area."""
    if location is None or location.city is None:
        return False
    
    # Simplified rural area detection
    rural_indicators = ["county", "township", "village", "unincorporated"]
    location_text = location.city.lower()
    
    return any(indicator in location_text for indicator in rural_indicators)


def _is_urban_area(location: Location) -> bool:
    """Determine if location is in an urban area."""
    if location is None or location.city is None:
        return False
    
    # Major urban cities
    urban_cities = ["new york", "los angeles", "chicago", "houston", "phoenix", 
                   "philadelphia", "san antonio", "san diego", "dallas", "san jose"]
    
    return location.city.lower() in urban_cities


def _is_cross_state_boundary(loc1: Location, loc2: Location) -> bool:
    """Check if locations are in different states."""
    if loc1 is None or loc2 is None:
        return False
    
    # This would require state information in the Location model
    # For now, return False as placeholder
    return False


def _calculate_geographic_penalty(provider: Provider, appointment_location: Location) -> int:
    """
    Calculate penalty based on geographic distance and area characteristics.
    
    Returns:
        int: Penalty score (higher = more severe distance issue)
    """
    if provider is None or appointment_location is None or provider.home_location is None:
        return 1
    
    # Check if using polygon geofence or circular radius
    if _has_polygon_geofence(provider):
        # For polygon geofences, calculate distance to nearest polygon edge
        polygon = _get_provider_geofence_polygon(provider)
        distance = _distance_to_polygon_edge(appointment_location, polygon)
        penalty = _calculate_polygon_penalty(distance)
    else:
        # Fallback to circular radius calculation
        distance = _calculate_distance_enhanced(provider.home_location, appointment_location)
        service_radius = _get_service_radius(provider, appointment_location)
        
        # Base penalty for being outside service area
        if distance > service_radius:
            penalty = int((distance - service_radius) / 5) + 1  # 1 point per 5 miles over limit
        else:
            penalty = 0
    
    # Additional penalties for specific scenarios
    
    # Rural area penalty (harder to serve)
    if _is_rural_area(appointment_location):
        penalty += 2
    
    # Cross-state penalty
    if _is_cross_state_boundary(provider.home_location, appointment_location):
        penalty += 3
    
    # Very long distance penalty
    if distance > 50:
        penalty += 5
    
    return max(1, penalty)  # Minimum penalty of 1


def _distance_to_polygon_edge(point: Location, polygon: List[Tuple[float, float]]) -> float:
    """
    Calculate the minimum distance from a point to the edge of a polygon.
    
    Returns:
        float: Distance in miles to the nearest polygon edge
    """
    if not polygon or len(polygon) < 3:
        return float('inf')
    
    min_distance = float('inf')
    n = len(polygon)
    
    for i in range(n):
        p1 = polygon[i]
        p2 = polygon[(i + 1) % n]
        
        # Calculate distance to line segment
        distance = _distance_to_line_segment(point, p1, p2)
        min_distance = min(min_distance, distance)
    
    return min_distance


def _distance_to_line_segment(point: Location, p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    """
    Calculate the distance from a point to a line segment.
    
    Returns:
        float: Distance in miles
    """
    x, y = point.longitude, point.latitude
    x1, y1 = p1
    x2, y2 = p2
    
    # Calculate the length of the line segment
    segment_length = sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    if segment_length == 0:
        # Point to point distance
        return _calculate_distance_enhanced(Location(latitude=y, longitude=x), 
                                          Location(latitude=y1, longitude=x1))
    
    # Calculate the projection of the point onto the line
    t = max(0, min(1, ((x - x1) * (x2 - x1) + (y - y1) * (y2 - y1)) / (segment_length**2)))
    
    # Find the closest point on the line segment
    closest_x = x1 + t * (x2 - x1)
    closest_y = y1 + t * (y2 - y1)
    
    # Calculate distance to the closest point
    return _calculate_distance_enhanced(Location(latitude=y, longitude=x), 
                                      Location(latitude=closest_y, longitude=closest_x))


def _calculate_polygon_penalty(distance_to_edge: float) -> int:
    """
    Calculate penalty based on distance to polygon edge.
    
    Returns:
        int: Penalty score
    """
    if distance_to_edge <= 0:
        return 0  # Inside polygon
    
    # Penalty increases with distance from polygon edge
    if distance_to_edge <= 5:
        return 1
    elif distance_to_edge <= 10:
        return 2
    elif distance_to_edge <= 20:
        return 3
    else:
        return int(distance_to_edge / 5) + 3 