# Application Configuration
# Environment
ENVIRONMENT=development
DEBUG=True

# Application Configuration
PROJECT_NAME=caxl-patient-service
API_V1_STR=/api/v1

# Server Configuration
HOST=0.0.0.0
PORT=8002

# CORS Configuration
CORS_ALLOW_ORIGINS=["http://localhost:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]
CORS_EXPOSE_HEADERS=["*"]

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_ECHO=False

# Tenant Configuration
TENANT_HEADER=x-tenant-id
TENANT_DB_PREFIX=caxl_patient_service_
TENANT_ID=elara

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
