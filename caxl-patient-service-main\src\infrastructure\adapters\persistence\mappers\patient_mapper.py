"""Patient mapper for transforming between domain entities and persistence models"""

from uuid import uuid4

from src.config.settings import settings
from src.domain.aggregates.patient import Patient
from src.domain.entities.caregiver import Caregiver
from src.domain.entities.email import EmailInfo
from src.domain.entities.emergency_contact import EmergencyContact
from src.domain.entities.location import LocationInfo
from src.domain.entities.medical_profile import MedicalProfile
from src.domain.entities.patient_episode_of_care import PatientEpisodeOfCareEntity
from src.domain.entities.patient_pii import PatientPII
from src.domain.entities.patient_preferences import PatientPreferences
from src.domain.entities.phone import PhoneInfo
from src.domain.entities.referring_info import ReferringInformation
from src.domain.enums import (
    CaregiverRelationship,
    EmergencyContactRelationship,
    Gender,
    InsuranceType,
    LocationName,
    PhoneType,
)
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.mapper_base import BaseMapper
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.value_objects.insurance import Insurance
from src.domain.value_objects.lab_preference import LabPreference
from src.domain.value_objects.patient_contact_info import PatientContactInfo
from src.domain.value_objects.personal_info import BasicPersonalInfo, PersonalInfo
from src.domain.value_objects.pharmacy_preference import PharmacyPreference
from src.infrastructure.adapters.persistence.models import (
    BaseModel,
)
from src.infrastructure.adapters.persistence.models import (
    Patient as PatientModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientCaregiver as PatientCaregiverModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientEmergencyContactPII as PatientEmergencyContactPIIModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientEpisodeOfCare as PatientEpisodeOfCareModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientPII as PatientPIIModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientPIIEmail as PatientPIIEmailModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientPIILocation as PatientPIILocationModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientPIIPhone as PatientPIIPhoneModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientPreferences as PatientPrefModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientProfile as PatientProfileModel,
)
from src.infrastructure.adapters.persistence.models import (
    PatientReferringMRN as PatientReferringMRNModel,
)


class PatientMapper(BaseMapper[Patient, PatientModel]):
    """Mapper for transforming between Patient domain entities and persistence models"""

    @property
    def model(self) -> type[PatientModel]:
        """Get the model class"""
        return PatientModel

    def _get_audit_stamp(self, model: BaseModel):
        return AuditStamp(
            record_status=RecordStatusEnum(model.record_status),
            mod_at=model.mod_at,
            mod_by=model.mod_by,
            mod_service=model.mod_service if model.mod_service else settings.DEFAULT_MOD_SERVICE,
            tenant_id=model.tenant_id or "elara1",
        )

    def to_domain(self, model: PatientModel | None) -> Patient | None:
        """Convert PatientModel to Patient domain entity"""
        if not model:
            return None

        # Create PII
        pii = None
        if model.pii:
            pii = PatientPII(
                id=model.pii.id,
                audit_stamp=self._get_audit_stamp(model=model.pii),
                personal_info=PersonalInfo(
                    basic_info=BasicPersonalInfo(
                        first_name=model.pii.first_name,
                        last_name=model.pii.last_name,
                        gender=Gender(model.pii.gender),
                        dob=model.pii.dob,
                    ),
                    ethnicity=model.pii.ethnicity,
                    race=model.pii.race,
                    ssn=model.pii.ssn,
                ),
                contact_info=PatientContactInfo(
                    emails=(
                        [
                            EmailInfo(
                                id=e.id,
                                audit_stamp=self._get_audit_stamp(model=e),
                                email=e.email,
                                email_type=e.email_type,
                                is_verified=e.is_verified,
                            )
                            for e in model.pii.emails
                        ]
                        if model.pii.emails
                        else []
                    ),
                    locations=(
                        [
                            LocationInfo(
                                id=loc.id,
                                audit_stamp=self._get_audit_stamp(model=loc),
                                location_id=loc.location_id,
                                location_name=LocationName(loc.location_name),
                                is_primary=loc.is_primary,
                            )
                            for loc in model.pii.locations
                        ]
                        if model.pii.locations
                        else []
                    ),
                    phones=(
                        [
                            PhoneInfo(
                                id=p.id,
                                audit_stamp=self._get_audit_stamp(model=p),
                                phone_number=p.phone_number,
                                phone_country_code=p.phone_country_code,
                                phone_type=PhoneType(p.phone_type),
                                is_primary=p.is_primary,
                                is_verified=p.is_verified,
                                preferred_for_sms=p.preferred_for_sms,
                            )
                            for p in model.pii.phones
                        ]
                        if model.pii.phones
                        else []
                    ),
                    emergency_contacts=[
                        EmergencyContact(
                            id=c.id,
                            audit_stamp=self._get_audit_stamp(model=c),
                            first_name=c.first_name,
                            last_name=c.last_name,
                            relationship=EmergencyContactRelationship(c.relationship),
                            phone_number=c.phone_number,
                            phone_country_code=c.phone_country_code,
                            email=c.email,
                        )
                        for c in model.pii.emergency_contacts
                    ],
                ),
            )

        # Create preferences
        preferences = None
        if model.preferences:
            preferences = PatientPreferences(
                id=model.preferences.id,
                audit_stamp=self._get_audit_stamp(model=model.preferences),
                lab_preference=LabPreference(
                    name=model.preferences.lab_name,
                    location_id=model.preferences.lab_location_id,
                    phone=model.preferences.lab_phone,
                    phone_country_code=model.preferences.lab_phone_country_code,
                    email=model.preferences.lab_email,
                ),
                pharmacy_preference=PharmacyPreference(
                    name=model.preferences.pharmacy_name,
                    location_id=model.preferences.pharmacy_location_id,
                    phone=model.preferences.pharmacy_phone,
                    email=model.preferences.pharmacy_email,
                ),
                pref_language=model.preferences.pref_language,
                time_pref=model.preferences.time_pref,
            )

        # Create profile
        profile = None
        if model.profile:
            profile = MedicalProfile(
                id=model.profile.id,
                audit_stamp=self._get_audit_stamp(model=model.profile),
                insurance=Insurance(
                    name=model.profile.insurance_name,
                    type=InsuranceType(model.profile.insurance_type),
                    number=model.profile.insurance_number,
                ),
                clinical_diagnosis=model.profile.clinical_diagnosis,
                medical_history=model.profile.medical_history,
                social_history=model.profile.social_history,
                allergies=model.profile.allergies,
                notes=model.profile.notes,
            )

        # Create caregivers
        caregivers = []
        if model.caregivers:
            caregivers = [
                Caregiver(
                    id=c.id,
                    audit_stamp=self._get_audit_stamp(model=c),
                    relationship=CaregiverRelationship(c.caregiver_relationship),
                    personal_info=BasicPersonalInfo(
                        first_name=c.caregiver_first_name,
                        last_name=c.caregiver_last_name,
                        gender=Gender(c.caregiver_gender),
                    ),
                    phone_number=c.caregiver_phone_number,
                    phone_country_code=c.caregiver_phone_country_code,
                    is_phone_verified=c.caregiver_phone_verified,
                    email=c.caregiver_email,
                    email_verified=c.caregiver_email_verified,
                    location_id=c.location_id,
                    patient_id=model.id,
                )
                for c in model.caregivers
            ]

        # Create referring MRNs
        referring_mrns = []
        if model.referring_mrns:
            referring_mrns = [
                ReferringInformation(
                    id=m.id,
                    audit_stamp=self._get_audit_stamp(model=m),
                    referring_mrn=m.referring_mrn,
                    referring_name=m.referring_name,
                    referring_state=m.referring_state,
                    referring_hospital=m.referring_hospital,
                    referring_npi=m.referring_npi,
                    inpatient_discharge_date=m.inpatient_discharge_date,
                )
                for m in model.referring_mrns
            ]

        # Create episodes of care
        episodes_of_care = []
        if model.patient_episodes_of_care:
            episodes_of_care = [
                PatientEpisodeOfCareEntity(
                    id=e.id,
                    audit_stamp=self._get_audit_stamp(model=e),
                    patient_id=e.patient_id,
                    patient_mrn_id=e.patient_mrn_id,
                    location_id=e.location_id,
                    discharge_summary=e.discharge_summary,
                    clinical_diagnosis=e.clinical_diagnosis,
                    date_of_start_of_care=e.date_of_start_of_care,
                    insurance_name=e.insurance_name,
                    insurance_type=e.insurance_type,
                    insurance_number=e.insurance_number,
                )
                for e in model.patient_episodes_of_care
            ]

        return Patient(
            id=model.id,
            audit_stamp=self._get_audit_stamp(model=model),
            pii=pii,
            preferences=preferences,
            profile=profile,
            caregivers=caregivers,
            referring_mrns=referring_mrns,
            episodes_of_care=episodes_of_care,
            mpu_id=model.mpu_id,
            nhid=model.nhid,
        )

    def to_model(self, entity: Patient | None) -> PatientModel | None:  # noqa: PLR0912
        """Convert Patient domain entity to PatientModel"""
        if not entity:
            return None

        # Create base model
        model = PatientModel(id=entity.id, mpu_id=entity.mpu_id, nhid=entity.nhid)
        model.apply_audit_stamp(audit_stamp=entity.audit_stamp)

        # Map PII if exists
        if entity.pii:
            pii_model = PatientPIIModel(
                id=entity.pii.id,
                ssn=entity.pii.personal_info.ssn,
                ethnicity=entity.pii.personal_info.ethnicity,
                race=entity.pii.personal_info.race,
                first_name=entity.pii.personal_info.basic_info.first_name,
                last_name=entity.pii.personal_info.basic_info.last_name,
                gender=entity.pii.personal_info.basic_info.gender.value,
                dob=entity.pii.personal_info.basic_info.dob,
            )
            pii_model.apply_audit_stamp(audit_stamp=entity.pii.audit_stamp)
            model.pii = pii_model

            # Map emails
            if entity.pii.contact_info and entity.pii.contact_info.emails:
                pii_model.emails = [
                    PatientPIIEmailModel(
                        id=e.id if e.id else uuid4(),
                        patient_pii_id=pii_model.id,
                        email=e.email,
                        email_type=e.email_type,
                        is_verified=e.is_verified,
                    )
                    for e in entity.pii.contact_info.emails
                ]
                for e in pii_model.emails:
                    e.apply_audit_stamp(audit_stamp=entity.pii.audit_stamp)

            # Map phones
            if entity.pii.contact_info and entity.pii.contact_info.phones:
                pii_model.phones = [
                    PatientPIIPhoneModel(
                        id=p.id if p.id else uuid4(),
                        patient_pii_id=pii_model.id,
                        phone_number=p.phone_number,
                        phone_country_code=p.phone_country_code,
                        phone_type=p.phone_type.value,
                        is_primary=p.is_primary,
                        is_verified=p.is_verified,
                        preferred_for_sms=p.preferred_for_sms,
                    )
                    for p in entity.pii.contact_info.phones
                ]

                for p in pii_model.phones:
                    p.apply_audit_stamp(audit_stamp=entity.pii.audit_stamp)

            # Map locations
            if entity.pii.contact_info and entity.pii.contact_info.locations:
                pii_model.locations = [
                    PatientPIILocationModel(
                        id=loc.id if loc.id else uuid4(),
                        patient_pii_id=pii_model.id,
                        location_id=loc.location_id,
                        location_name=loc.location_name.value,
                        is_primary=loc.is_primary,
                    )
                    for loc in entity.pii.contact_info.locations
                ]
                for loc in pii_model.locations:
                    loc.apply_audit_stamp(audit_stamp=entity.pii.audit_stamp)

            # Map emergency contacts
            if entity.pii.contact_info and entity.pii.contact_info.emergency_contacts:
                pii_model.emergency_contacts = [
                    PatientEmergencyContactPIIModel(
                        id=c.id if c.id else uuid4(),
                        patient_pii_id=pii_model.id,
                        relationship=c.relationship.value,
                        first_name=c.first_name,
                        last_name=c.last_name,
                        phone_number=c.phone_number,
                        phone_country_code=c.phone_country_code,
                        email=c.email,
                    )
                    for c in entity.pii.contact_info.emergency_contacts
                ]
                for c in pii_model.emergency_contacts:
                    c.apply_audit_stamp(audit_stamp=entity.pii.audit_stamp)

        # Map preferences if exists
        if entity.preferences:
            model.preferences = PatientPrefModel(
                id=entity.preferences.id if entity.preferences.id else uuid4(),
                pref_language=entity.preferences.pref_language,
                lab_name=entity.preferences.lab_preference.name,
                lab_location_id=entity.preferences.lab_preference.location_id,
                lab_phone=entity.preferences.lab_preference.phone,
                lab_phone_country_code=entity.preferences.lab_preference.phone_country_code,
                lab_email=entity.preferences.lab_preference.email,
                pharmacy_name=entity.preferences.pharmacy_preference.name,
                pharmacy_location_id=entity.preferences.pharmacy_preference.location_id,
                pharmacy_phone=entity.preferences.pharmacy_preference.phone,
                pharmacy_email=entity.preferences.pharmacy_preference.email,
                time_pref=entity.preferences.time_pref,
            )
            model.preferences.apply_audit_stamp(audit_stamp=entity.preferences.audit_stamp)

        # Map profile if exists
        if entity.profile:
            model.profile = PatientProfileModel(
                id=entity.profile.id if entity.profile.id else uuid4(),
                insurance_name=entity.profile.insurance.name,
                insurance_type=entity.profile.insurance.type,
                insurance_number=entity.profile.insurance.number,
                clinical_diagnosis=entity.profile.clinical_diagnosis,
                medical_history=entity.profile.medical_history,
                social_history=entity.profile.social_history,
                allergies=entity.profile.allergies,
                notes=entity.profile.notes,
            )
            model.profile.apply_audit_stamp(audit_stamp=entity.profile.audit_stamp)

        # Map caregivers if exist
        if entity.caregivers:
            model.caregivers = [
                PatientCaregiverModel(
                    id=c.id,
                    patient_id=model.id,
                    caregiver_relationship=c.relationship,
                    caregiver_first_name=c.personal_info.first_name,
                    caregiver_last_name=c.personal_info.last_name,
                    caregiver_gender=c.personal_info.gender.value,
                    caregiver_email=c.email,
                    caregiver_email_verified=c.email_verified,
                    caregiver_phone_number=c.phone_number,
                    caregiver_phone_country_code=c.phone_country_code,
                    caregiver_phone_verified=c.is_phone_verified,
                    location_id=c.location_id,
                )
                for c in entity.caregivers
            ]
            for c in model.caregivers:
                c.apply_audit_stamp(audit_stamp=entity.audit_stamp)

        # Map referring MRNs if exist
        if entity.referring_mrns:
            model.referring_mrns = [
                PatientReferringMRNModel(
                    id=m.id if m.id else uuid4(),
                    patient_id=entity.id,
                    referring_mrn=m.referring_mrn,
                    referring_name=m.referring_name,
                    referring_state=m.referring_state,
                    referring_hospital=m.referring_hospital,
                    referring_npi=m.referring_npi,
                    inpatient_discharge_date=m.inpatient_discharge_date,
                )
                for m in entity.referring_mrns
            ]
            for r in model.referring_mrns:
                r.apply_audit_stamp(audit_stamp=entity.audit_stamp)

        # Map episodes of care if exist
        if hasattr(entity, "episodes_of_care") and entity.episodes_of_care:
            model.patient_episodes_of_care = [
                PatientEpisodeOfCareModel(
                    id=e.id if e.id else uuid4(),
                    patient_id=entity.id,
                    patient_mrn_id=e.patient_mrn_id,
                    location_id=e.location_id,
                    discharge_summary=e.discharge_summary,
                    clinical_diagnosis=e.clinical_diagnosis,
                    date_of_start_of_care=e.date_of_start_of_care,
                    insurance_name=e.insurance_name,
                    insurance_type=e.insurance_type,
                    insurance_number=e.insurance_number,
                )
                for e in entity.episodes_of_care
            ]
            for e in model.patient_episodes_of_care:
                e.apply_audit_stamp(audit_stamp=entity.audit_stamp)

        return model
