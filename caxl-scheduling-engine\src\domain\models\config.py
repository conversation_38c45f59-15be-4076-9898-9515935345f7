"""Configuration models for the CareAxl Scheduling Engine."""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class ServiceConfig(BaseModel):
    """Configuration for a specific service type."""
    service_type: str
    required_skills: List[str]
    geographic_radius_miles: float = 25.0
    max_daily_appointments_per_provider: int = 8
    max_weekly_hours_per_provider: int = 40
    
    # Constraint weights (0.0 to 1.0)
    continuity_weight: float = 0.8
    workload_balance_weight: float = 0.6
    geographic_clustering_weight: float = 0.4
    patient_preference_weight: float = 0.7
    capacity_threshold_percentage: float = 0.9
    
    # Service-specific settings
    visit_duration_minutes: int = 60
    requires_initial_assessment: bool = True
    allows_weekend_visits: bool = False
    emergency_response_time_hours: int = 24
    
    # Geographic clustering settings
    cluster_radius_miles: float = 15.0
    max_cluster_size: int = 8
    prefer_same_day_clustering: bool = True
    
    # Continuity of care settings
    continuity_priority_bonus: int = 100
    continuity_threshold_days: int = 30
    existing_relationship_bonus: int = 50
    
    # Workload balancing settings
    target_daily_appointments: int = 5
    workload_variance_tolerance: int = 2
    overtime_penalty_multiplier: float = 1.5
    
    # Patient preference settings
    preferred_provider_bonus: int = 30
    preferred_time_bonus: int = 20
    preferred_location_bonus: int = 25
    
    # Skill matching settings
    strict_skill_matching: bool = True
    allow_skill_hierarchy: bool = True
    skill_mismatch_penalty: int = 200
    
    # Time constraints
    min_visit_duration_minutes: int = 30
    max_visit_duration_minutes: int = 120
    travel_buffer_minutes: int = 15
    setup_time_minutes: int = 5
    
    # Quality metrics
    quality_score_weight: float = 0.3
    patient_satisfaction_weight: float = 0.4
    provider_efficiency_weight: float = 0.3


class SchedulerConfig(BaseModel):
    """Main scheduler configuration."""
    rolling_window_days: int = 7
    batch_size: int = 100
    max_solving_time_seconds: int = 300
    config_folder: str = "config"
    log_level: str = "INFO"

    # External service configuration
    external_services: Optional[Dict[str, Any]] = None

    # Feature Toggles - Assignment Stage
    enable_geographic_clustering: bool = True
    enable_continuity_of_care: bool = True
    enable_workload_balancing: bool = True
    enable_patient_preferences: bool = True
    enable_provider_capacity_management: bool = True

    # Feature Toggles - Day Planning Stage
    enable_healthcare_task_sequencing: bool = True
    enable_travel_time_optimization: bool = True
    enable_break_time_management: bool = True
    enable_route_optimization: bool = True

    # Pinned appointment handling
    enable_pinned_appointments: bool = True
    respect_pinned_times: bool = True
    allow_pinned_provider_changes: bool = False

    # Advanced Traffic Integration (Enterprise Feature)
    enable_advanced_traffic_integration: bool = False
    
    # Traffic Integration Configuration
    traffic_integration: Optional[Dict[str, Any]] = None
    
    # Basic Traffic Model Configuration
    traffic_model: Optional[Dict[str, Any]] = None

    # Constraint configuration
    constraints: Optional[Dict[str, Any]] = None

    # Solver configuration
    solver: Optional[Dict[str, Any]] = None

    # Logging configuration
    logging: Optional[Dict[str, Any]] = None


class ExternalServiceConfig(BaseModel):
    """Configuration for external service connections."""
    base_url: str
    timeout_seconds: int = 30
    retry_attempts: int = 3
    circuit_breaker_threshold: int = 5
    api_key: Optional[str] = None
    headers: Optional[Dict[str, str]] = None


class ConstraintConfig(BaseModel):
    """Configuration for scheduling constraints."""
    skill_matching: Dict[str, Any] = Field(default_factory=dict)
    geographic: Dict[str, Any] = Field(default_factory=dict)
    workload: Dict[str, Any] = Field(default_factory=dict)
    continuity: Dict[str, Any] = Field(default_factory=dict)
    timing: Dict[str, Any] = Field(default_factory=dict)


class SolverConfig(BaseModel):
    """Configuration for the optimization solver."""
    engine: str = "timefold"
    max_solving_time_seconds: int = 300
    termination_conditions: List[Dict[str, Any]] = Field(default_factory=list)
    weights: Dict[str, int] = Field(default_factory=dict)


class TrafficModelConfig(BaseModel):
    """Configuration for traffic modeling."""
    urban_cities: List[str] = Field(default_factory=list)
    speed_factors: Dict[str, float] = Field(default_factory=dict)
    time_adjustments: Dict[str, float] = Field(default_factory=dict)
