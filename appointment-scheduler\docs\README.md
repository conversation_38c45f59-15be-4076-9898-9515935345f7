# Appointment Scheduler Documentation

A comprehensive healthcare appointment scheduling system using Timefold optimization engine.

## Table of Contents

1. [Timefold Basics](#timefold-basics)
2. [Project Architecture](#project-architecture)
3. [Data Models](#data-models)
4. [Scheduling Jobs](#scheduling-jobs)
5. [REST API](#rest-api)
6. [Configuration](#configuration)
7. [Testing](#testing)
8. [Deployment](#deployment)

## Timefold Basics

### Core Concepts

Timefold is a constraint satisfaction solver that optimizes complex scheduling problems. The appointment scheduler uses these key Timefold annotations:

#### @PlanningId
Uniquely identifies entities in the planning problem.

```python
@dataclass
class Provider:
    @PlanningId
    id: str
    name: str
    skills: List[str]
```

#### @PlanningEntity
Marks objects that can be changed during optimization.

```python
@dataclass
@PlanningEntity
class AppointmentData:
    id: str
    consumer_id: str
    
    @PlanningVariable(value_range_provider_refs=["providerRange"])
    provider: Optional[Provider] = None
    
    @PlanningVariable(value_range_provider_refs=["timeSlotRange"])
    time_slot: Optional[TimeSlot] = None
```

#### @PlanningVariable
Defines variables that the solver can modify to find optimal solutions.

#### @PlanningSolution
Represents the complete problem with all entities and constraints.

```python
@dataclass
@PlanningSolution
class AppointmentSchedule:
    @PlanningEntityCollectionProperty
    appointments: List[AppointmentData]
    
    @ValueRangeProvider(id="providerRange")
    @ProblemFactCollectionProperty
    providers: List[Provider]
    
    @ValueRangeProvider(id="timeSlotRange")
    @ProblemFactCollectionProperty
    time_slots: List[TimeSlot]
    
    @PlanningScore
    score: Optional[HardSoftScore] = None
```

### Solver Configuration

The solver is configured through YAML files that define:
- Solving time limits
- Constraint weights
- Optimization algorithms

```yaml
scheduler:
  max_solving_time_seconds: 120
  log_level: INFO
```

### Simple Working Example

```python
from appointment_scheduler.jobs.assign_appointments import AssignAppointmentJob
from appointment_scheduler.config_manager import ConfigManager

# Create and run assignment job
config = ConfigManager("config")
job = AssignAppointmentJob(config_manager=config, daemon_mode=False)
result = job.run()

print(f"Assigned {result['summary']['assigned_appointments']} appointments")
```

## Project Architecture

### Directory Structure

```
appointment-scheduler/
├── src/appointment_scheduler/
│   ├── jobs/                    # Scheduling jobs
│   │   ├── assign_appointments.py
│   │   └── day_plan.py
│   ├── constraints/             # Optimization constraints
│   ├── api/                     # REST API endpoints
│   ├── utils/                   # Utility modules
│   ├── domain.py               # Data models
│   ├── data_loader.py          # Data loading
│   ├── scheduler.py            # Main scheduler
│   └── planning_models.py      # Timefold models
├── data/                       # Data files and scenarios
├── config/                     # Configuration files
├── tests/                      # Test suite
└── tools/                      # Analysis tools
```

### Core Components

#### 1. Data Models (`domain.py`)
Defines the core business entities:
- `Provider`: Healthcare providers with skills and availability
- `Consumer`: Patients requiring care
- `AppointmentData`: Individual appointments to be scheduled
- `Location`: Geographic coordinates and addresses

#### 2. Planning Models (`planning_models.py`)
Timefold-specific models for optimization:
- `AppointmentSchedule`: Complete planning solution
- `TimeSlot`: Available time periods
- Constraint definitions

#### 3. Jobs (`jobs/`)
Main scheduling operations:
- `AssignAppointmentJob`: Assigns providers to appointments
- `DayPlanJob`: Optimizes daily schedules for providers

#### 4. Scheduler (`scheduler.py`)
Orchestrates job execution with support for:
- One-time execution
- Daemon mode (scheduled execution)
- Different service types

## Data Models

### Provider Model

```python
@dataclass
class Provider:
    id: str
    name: str
    role: str  # RN, LPN, CNA, etc.
    skills: List[str]
    home_location: Location
    availability: Availability
    capacity: Capacity
```

### Consumer Model

```python
@dataclass
class Consumer:
    id: str
    name: str
    location: Location
    care_episode_id: str
    consumer_preferences: ConsumerPreferences
```

### Appointment Model

```python
@dataclass
class AppointmentData:
    id: str
    consumer_id: str
    required_skills: List[str]
    duration_min: int
    appointment_date: date
    priority: str
    urgent: bool = False
```

## Scheduling Jobs

### Assignment Job

**Purpose**: Assigns healthcare providers to patient appointments based on skills, availability, and preferences.

**Execution**:
```bash
# Direct execution
python -m appointment_scheduler.jobs.assign_appointments

# Via scheduler (one-time)
python -m appointment_scheduler.scheduler --mode once --job assign

# Via scheduler (daemon mode - runs daily at 2:00 AM)
python -m appointment_scheduler.scheduler --mode daemon
```

**Process**:
1. Load data (providers, consumers, appointments)
2. Create planning problem with constraints
3. Run Timefold solver
4. Return optimized assignments

### Day Plan Job

**Purpose**: Optimizes daily schedules for providers, determining optimal time slots and travel routes.

**Execution**:
```bash
# Direct execution
python -m appointment_scheduler.jobs.day_plan

# Via scheduler
python -m appointment_scheduler.scheduler --mode once --job dayplan
```

**Process**:
1. Load assigned appointments for target date
2. Generate time slots and travel constraints
3. Optimize for minimal travel and efficient scheduling
4. Return time-optimized day plans

### Scheduler Configuration

The `scheduler.py` supports multiple execution modes:

```python
# One-time execution
scheduler = AppointmentScheduler("config", daemon_mode=False)
result = scheduler.run_assign_appointments()

# Daemon mode (scheduled execution)
scheduler = AppointmentScheduler("config", daemon_mode=True)
scheduler.start()  # Runs continuously
```

## REST API

### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/patients` | Load patients from data/consumers.yaml |
| GET | `/carestaff` | Load care staff from data/providers.yaml |
| POST | `/assign-appointments` | Trigger assignment solver |
| POST | `/reassign` | Handle provider unavailability/changes |
| POST | `/run-dayplan` | Run dayplan solver for providers |

### API Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Scheduler
    participant Timefold
    participant DataLoader

    Client->>API: GET /patients
    API->>DataLoader: load_consumers()
    DataLoader-->>API: Consumer data
    API-->>Client: Patient list

    Client->>API: POST /assign-appointments
    API->>Scheduler: run_assign_appointments()
    Scheduler->>DataLoader: load_all_data()
    Scheduler->>Timefold: solve(problem)
    Timefold-->>Scheduler: optimized solution
    Scheduler-->>API: assignment results
    API-->>Client: Assignment response
```

### Example API Usage

**Start API Server**:
```bash
python -m appointment_scheduler.api.app
# Server runs on http://localhost:8000
```

**Load Patients**:
```bash
curl http://localhost:8000/api/v1/patients
```

**Trigger Assignment**:
```bash
curl -X POST http://localhost:8000/api/v1/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{
    "appointments": [
      {
        "consumer_id": "patient-001",
        "required_skills": ["basic_care"],
        "duration_min": 30,
        "priority": "normal"
      }
    ],
    "target_date": "2024-01-15"
  }'
```

**Run Day Plan**:
```bash
curl -X POST http://localhost:8000/api/v1/run-dayplan \
  -H "Content-Type: application/json" \
  -d '{
    "target_date": "2024-01-15",
    "provider_ids": ["provider-001"]
  }'
```

### API Response Format

**Assignment Response**:
```json
{
  "success": true,
  "message": "Assignment completed successfully",
  "total_appointments": 10,
  "assigned_appointments": 8,
  "unassigned_appointments": 2,
  "processing_time_seconds": 45.2,
  "assignments": [
    {
      "appointment_id": "apt-001",
      "provider_id": "provider-001",
      "scheduled_time": "2024-01-15T10:00:00"
    }
  ]
}
```

## Configuration

### Service Configuration

Each service type has its own configuration file:

**config/physical_therapy.yml**:
```yaml
service_config:
  service_type: "physical_therapy"
  required_skills: ["basic_care"]
  geographic_radius_miles: 25.0
  max_daily_appointments_per_provider: 8
  continuity_weight: 0.7
  workload_balance_weight: 0.6
  geographic_clustering_weight: 0.4
  patient_preference_weight: 0.7
```

**config/skilled_nursing.yml**:
```yaml
service_config:
  service_type: "skilled_nursing"
  required_skills: ["medication_management", "wound_care"]
  geographic_radius_miles: 30.0
  max_daily_appointments_per_provider: 6
  continuity_weight: 0.8
  workload_balance_weight: 0.7
```

### Scheduler Configuration

**config/scheduler.yml**:
```yaml
scheduler:
  max_solving_time_seconds: 120
  log_level: "INFO"
  daemon_check_interval_seconds: 60
  daily_execution_time: "02:00"
```

## Testing

### Test Structure

The project includes comprehensive testing across multiple dimensions:

- **Scenario Testing**: 14+ test dimensions with edge cases
- **Special Cases**: Provider unavailability, pinned appointments
- **API Testing**: Unit, integration, and performance tests
- **Bug Analysis**: Automated issue detection

### Running Tests

```bash
# Run all scenario tests
python tests/test_runner.py

# Run special test cases
python tests/special_test_cases.py

# Run API tests
python tests/test_api.py --test-type all

# Analyze test coverage
python tools/scenario_coverage_analyzer.py
```

See [TESTING_GUIDE.md](../TESTING_GUIDE.md) for detailed testing instructions.

## Deployment

### Production Deployment

1. **Install Dependencies**:
   ```bash
   pip install -e ".[api]"
   ```

2. **Configure Environment**:
   ```bash
   export LOG_LEVEL=INFO
   export DATA_DIR=/path/to/production/data
   export CONFIG_DIR=/path/to/production/config
   ```

3. **Start Services**:
   ```bash
   # API Server
   uvicorn appointment_scheduler.api.app:create_app --host 0.0.0.0 --port 8000

   # Scheduler Daemon
   python -m appointment_scheduler.scheduler --mode daemon
   ```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .
RUN pip install -e ".[api]"

EXPOSE 8000
CMD ["uvicorn", "appointment_scheduler.api.app:create_app", "--host", "0.0.0.0", "--port", "8000"]
```

### Monitoring

- **Health Check**: `GET /health`
- **Metrics**: Available through API endpoints
- **Logging**: Configured via `config/logger.yaml`

---

For more detailed information, see the individual documentation files in this directory.
