from enum import Enum

from src.domain.exceptions.base import RecordStatusInvalidError
from src.domain.foundations.base.value_object_base import ValueObjectBase


class RecordStatusEnum(ValueObjectBase, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    ARCHIVED = "ARCHIVED"
    DELETED = "DELETED"

    # ValueObjectBase requires this method to be implemented.
    def validate(self) -> None:
        """Enum members are inherently valid within their defined set."""
        pass

    # Override to_dict for a representation suitable for an Enum as a VO.
    # ValueObjectBase's default to_dict iterates self.__dict__, which might not
    # be what we want for an Enum member (name and value are more typical).
    def to_dict(self) -> dict:
        return {"name": self.name, "value": self.value}

    def __str__(self) -> str:
        return self.value

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}.{self.name}: '{self.value}'>"

    @classmethod
    def from_value(cls, value: str) -> "RecordStatusEnum":
        """
        Creates a RecordStatusEnum member from a string value.
        Matches case-insensitively by value, then by name.
        """
        if not isinstance(value, str):
            raise RecordStatusInvalidError(f"Invalid type for RecordStatusEnum value: {type(value)}. Expected str.")

        value_lower = value.lower()
        for member in cls:
            if member.value.lower() == value_lower:
                return member
            if member.name.lower() == value_lower:
                return member
        raise RecordStatusInvalidError(f"Invalid value for RecordStatusEnum: '{value}'.")

    # Note: ValueObjectBase's __eq__ and __hash__ will use the above to_dict().
    # Enum's default __eq__ and __hash__ are usually sufficient and robust.
    # If this causes issues, `RecordStatusEnum` might need to explicitly
    # re-delegate __eq__ and __hash__ to `super(ValueObjectBase, self).__eq__` etc.
    # or just rely on Enum's by removing them from ValueObjectBase for Enums.
    # For now, let's see if this resolves the creation error.
