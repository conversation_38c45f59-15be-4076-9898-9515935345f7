"""Patient Contact Information Value Object"""

from uuid import UUID

from src.domain.enums import PhoneType
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class PhoneInfo(EntityBase):
    """Phone information value object"""

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        phone_number: str,
        phone_type: PhoneType | None = None,
        phone_country_code: str | None = None,
        is_primary: bool = False,
        is_verified: bool = False,
        preferred_for_sms: bool = False,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._phone_number = phone_number.strip()
        self._phone_type = phone_type.value if phone_type else None
        self._phone_country_code = phone_country_code.strip() if phone_country_code else None
        self._is_primary = is_primary
        self._is_verified = is_verified
        self._preferred_for_sms = preferred_for_sms

        if not self._phone_number:
            raise ValueError("Phone number is required")

    @property
    def phone_number(self) -> str:
        return self._phone_number

    @phone_number.setter
    def phone_number(self, value: str) -> None:
        if not value or not value.strip():
            raise ValueError("Phone number is required")
        self._phone_number = value.strip()

    @property
    def phone_type(self) -> PhoneType | None:
        return PhoneType(self._phone_type) if self._phone_type else None

    @phone_type.setter
    def phone_type(self, value: PhoneType | None) -> None:
        self._phone_type = value.value if value else None

    @property
    def phone_country_code(self) -> str | None:
        return self._phone_country_code

    @phone_country_code.setter
    def phone_country_code(self, value: str | None) -> None:
        self._phone_country_code = value.strip() if value else None

    @property
    def is_primary(self) -> bool:
        return self._is_primary

    @is_primary.setter
    def is_primary(self, value: bool) -> None:
        self._is_primary = value

    @property
    def is_verified(self) -> bool:
        return self._is_verified

    @is_verified.setter
    def is_verified(self, value: bool) -> None:
        self._is_verified = value

    @property
    def preferred_for_sms(self) -> bool:
        return self._preferred_for_sms

    @preferred_for_sms.setter
    def preferred_for_sms(self, value: bool) -> None:
        self._preferred_for_sms = value
