"""Appointment domain entity"""

from datetime import date, datetime
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import dataclass, field

from src.domain.value_objects.location import Location
from src.domain.enums.appointment_status import AppointmentStatus


@dataclass
class Appointment:
    """Appointment domain entity"""
    
    id: UUID = field(default_factory=uuid4)
    consumer_id: UUID = field(default_factory=uuid4)
    provider_id: Optional[UUID] = None
    
    # Scheduling details
    appointment_date: Optional[date] = None
    scheduled_start_time: Optional[datetime] = None
    scheduled_end_time: Optional[datetime] = None
    duration_minutes: int = 30
    
    # Requirements
    required_skills: List[Skill] = field(default_factory=list)
    location: Optional[Location] = None
    
    # Metadata
    priority: Priority = Priority.NORMAL
    status: AppointmentStatus = AppointmentStatus.PENDING
    urgent: bool = False
    
    # Constraints
    is_pinned: bool = False
    pinned_provider_id: Optional[UUID] = None
    pinned_time_slot: Optional[datetime] = None
    pin_reason: Optional[str] = None
    can_unpin: bool = True
    
    # Episode information
    care_episode_id: Optional[UUID] = None
    requires_same_provider: bool = False
    
    # Audit fields
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization validation"""
        if self.duration_minutes <= 0:
            raise ValueError("Duration must be positive")
        
        if self.scheduled_start_time and self.scheduled_end_time:
            if self.scheduled_end_time <= self.scheduled_start_time:
                raise ValueError("End time must be after start time")
    
    def assign_provider(self, provider_id: UUID) -> None:
        """Assign a provider to this appointment"""
        if self.is_pinned and self.pinned_provider_id and self.pinned_provider_id != provider_id:
            if not self.can_unpin:
                raise ValueError("Cannot reassign pinned appointment")
        
        self.provider_id = provider_id
        self.status = AppointmentStatus.ASSIGNED
        self.updated_at = datetime.utcnow()
    
    def schedule_time(self, start_time: datetime, end_time: datetime) -> None:
        """Schedule specific time for this appointment"""
        if end_time <= start_time:
            raise ValueError("End time must be after start time")
        
        self.scheduled_start_time = start_time
        self.scheduled_end_time = end_time
        self.appointment_date = start_time.date()
        self.updated_at = datetime.utcnow()
    
    def pin_to_provider(self, provider_id: UUID, reason: str, can_unpin: bool = True) -> None:
        """Pin appointment to specific provider"""
        self.is_pinned = True
        self.pinned_provider_id = provider_id
        self.pin_reason = reason
        self.can_unpin = can_unpin
        self.updated_at = datetime.utcnow()
    
    def unpin(self) -> None:
        """Unpin appointment if allowed"""
        if not self.can_unpin:
            raise ValueError("Cannot unpin this appointment")
        
        self.is_pinned = False
        self.pinned_provider_id = None
        self.pin_reason = None
        self.updated_at = datetime.utcnow()
    
    def mark_completed(self) -> None:
        """Mark appointment as completed"""
        self.status = AppointmentStatus.COMPLETED
        self.updated_at = datetime.utcnow()
    
    def cancel(self, reason: str) -> None:
        """Cancel appointment"""
        self.status = AppointmentStatus.CANCELLED
        self.updated_at = datetime.utcnow()
    
    @property
    def is_scheduled(self) -> bool:
        """Check if appointment is scheduled"""
        return (
            self.scheduled_start_time is not None 
            and self.scheduled_end_time is not None
            and self.provider_id is not None
        )
    
    @property
    def is_assigned(self) -> bool:
        """Check if appointment has assigned provider"""
        return self.provider_id is not None
