"""Mapper for Patient Episode of Care requests"""

from src.application.dtos.patient_episode_of_care_dto import (
    PatientEpisodeOfCareCreateDTO,
    PatientEpisodeOfCareUpdateDTO,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient_episode_of_care_schema import (
    PatientEpisodeOfCareCreateSchema,
    PatientEpisodeOfCareUpdateSchema,
)


def map_create_request_to_dto(
    request: PatientEpisodeOfCareCreateSchema,
    context: AuthSession,
) -> PatientEpisodeOfCareCreateDTO:
    """Map create request to DTO"""
    return PatientEpisodeOfCareCreateDTO(
        patient_id=request.patient_id,
        patient_mrn_id=request.patient_mrn_id,
        location_id=request.location_id,
        discharge_summary=request.discharge_summary,
        clinical_diagnosis=request.clinical_diagnosis,
        date_of_start_of_care=request.date_of_start_of_care,
        insurance_name=request.insurance_name,
        insurance_type=request.insurance_type,
        insurance_number=request.insurance_number,
    )


def map_update_request_to_dto(
    request: PatientEpisodeOfCareUpdateSchema,
    context: AuthSession,
) -> PatientEpisodeOfCareUpdateDTO:
    """Map update request to DTO"""
    return PatientEpisodeOfCareUpdateDTO(
        id=request.id,
        patient_id=request.patient_id,
        patient_mrn_id=request.patient_mrn_id,
        location_id=request.location_id,
        discharge_summary=request.discharge_summary,
        clinical_diagnosis=request.clinical_diagnosis,
        date_of_start_of_care=request.date_of_start_of_care,
        insurance_name=request.insurance_name,
        insurance_type=request.insurance_type,
        insurance_number=request.insurance_number,
    )
