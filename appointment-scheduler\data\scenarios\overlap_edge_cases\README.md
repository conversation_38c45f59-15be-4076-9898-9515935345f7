# Appointment Overlap Edge Cases

**Purpose**: Test appointment overlap detection and prevention across various scenarios
**Best for**: Testing temporal conflict resolution and scheduling constraints
**Complexity**: High

## Features Demonstrated
- No overlap scheduling
- Partial overlap detection
- Full overlap prevention
- Adjacent appointment handling
- Zero duration appointments
- Long duration appointments
- Concurrent appointment conflicts

## Data Overview
- **Providers**: 3 (different capacity levels)
- **Patients**: 8 (various scheduling needs)
- **Appointments**: 12 (designed to create overlap scenarios)
- **Geographic Coverage**: New York Metro Area

## Test Dimensions Covered
- **appointment_overlap**: no_overlap, partial_overlap, full_overlap, adjacency, zero_duration, long_duration, concurrent_appointments
- **capacity**: optimal_capacity, over_capacity
- **time_matching**: strict_timing, flexible_timing

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/overlap_edge_cases/* data/

# Run assignment job
python -m appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m appointment_scheduler.jobs.day_plan
```

## Expected Outcomes
- Overlapping appointments should be resolved by assignment to different providers or time slots
- Zero duration appointments should be handled appropriately
- Long duration appointments should not block entire schedules
- Adjacent appointments should maintain proper buffer times
