"""Domain Enums"""

from enum import Enum


class Gender(Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"
    OTHER = "OTHER"
    UNKNOWN = "UNKNOWN"


class CaregiverRelationship(Enum):
    SPOUSE = "SPOUSE"
    DAUGHTER = "DAUGHTER"
    SON = "SON"
    FRIEND = "FRIEND"
    PARENT = "PARENT"
    SIBLING = "SIBLING"
    OTHER = "OTHER"


class EmergencyContactRelationship(Enum):
    SPOUSE = "spouse"
    DAUGHTER = "daughter"
    SON = "son"
    FRIEND = "friend"
    PARENT = "parent"
    SIBLING = "sibling"
    OTHER = "other"


class LocationName(Enum):
    HOME = "home"
    WORK = "work"
    FACILITY = "facility"
    FAMILY = "family"


class PhoneType(Enum):
    HOME = "home"
    MOBILE = "mobile"
    WORK = "work"
    EMERGENCY = "911"


class InsuranceType(Enum):
    PRIVATE = "private"
    MEDICARE = "medicare"


class OrderStatus(Enum):
    """Order status enum"""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
