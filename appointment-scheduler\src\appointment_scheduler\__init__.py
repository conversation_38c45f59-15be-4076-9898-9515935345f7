"""
Appointment Scheduler Package

A US-based appointment scheduling system using Timefold optimization 
with a two-job architecture for realistic healthcare scheduling.
"""

__version__ = "0.1.0"
__author__ = "Appointment Scheduler Team"

from .scheduler import AppointmentScheduler
from .config_manager import ConfigManager
from .domain import (
    Provider, Consumer, AppointmentData, ScheduledAppointment,
    BatchAssignmentResult, ServiceConfig, SchedulerConfig
)

__all__ = [
    "AppointmentScheduler",
    "ConfigManager", 
    "Provider",
    "Consumer",
    "AppointmentData",
    "ScheduledAppointment",
    "BatchAssignmentResult",
    "ServiceConfig",
    "SchedulerConfig",
]
