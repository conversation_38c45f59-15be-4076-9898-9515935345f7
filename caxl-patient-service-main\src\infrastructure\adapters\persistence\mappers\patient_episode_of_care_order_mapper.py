"""Mapper for Patient Episode of Care Order"""

from src.config.settings import settings
from src.domain.entities.patient_episode_of_care_order import PatientEpisodeOfCareOrder
from src.domain.enums import OrderStatus
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.mapper_base import BaseMapper
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.value_objects.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)
from src.infrastructure.adapters.persistence.models import BaseModel
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order import (
    PatientEpisodeOfCareOrder as PatientEpisodeOfCareOrderModel,
)
from src.infrastructure.adapters.persistence.models.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective as PatientEpisodeOfCareOrderDirectiveModel,
)


class PatientEpisodeOfCareOrderMapper(
    BaseMapper[PatientEpisodeOfCareOrder, PatientEpisodeOfCareOrderModel]
):
    """Mapper for Patient Episode of Care Order"""

    @property
    def model(self) -> type[PatientEpisodeOfCareOrderModel]:
        """Get the model class"""
        return PatientEpisodeOfCareOrderModel

    def _get_audit_stamp(self, model: BaseModel):
        """Get audit stamp from model"""
        return AuditStamp(
            record_status=RecordStatusEnum(model.record_status),
            mod_at=model.mod_at,
            mod_by=model.mod_by,
            mod_service=model.mod_service if model.mod_service else settings.DEFAULT_MOD_SERVICE,
            tenant_id=model.tenant_id or "elara1",
        )

    def to_domain(
        self, model: PatientEpisodeOfCareOrderModel | None
    ) -> PatientEpisodeOfCareOrder | None:
        """Convert model to domain entity"""
        if not model:
            return None

        # Convert directives to value objects
        directives = []
        if model.directives:
            for directive_model in model.directives:
                directives.append(
                    PatientEpisodeOfCareOrderDirective(
                        id=directive_model.id,  # Include the directive ID
                        patient_episode_of_care_order_id=model.id,  # Use the parent order's ID
                        instructions=directive_model.instructions,
                    )
                )

        return PatientEpisodeOfCareOrder(
            id=model.id,
            audit_stamp=self._get_audit_stamp(model=model),
            patient_episode_of_care_id=model.patient_episode_of_care_id,
            order_status=OrderStatus(model.order_status),  # Convert string to enum
            order_date=model.order_date,
            order_notes=model.order_notes,
            directives=directives,
        )

    def to_model(self, entity: PatientEpisodeOfCareOrder) -> PatientEpisodeOfCareOrderModel:
        """Convert domain entity to model"""
        # Create a new model instance
        model = PatientEpisodeOfCareOrderModel()

        # Set the ID if it exists (for updates)
        if entity.id:
            model.id = entity.id

        # Set other fields
        model.patient_episode_of_care_id = entity.patient_episode_of_care_id
        model.order_status = entity.order_status.value  # Convert enum to string
        model.order_date = entity.order_date
        model.order_notes = entity.order_notes
        model.tenant_id = entity.audit_stamp.tenant_id
        model.mod_at = entity.audit_stamp.mod_at
        model.mod_by = entity.audit_stamp.mod_by
        model.mod_service = entity.audit_stamp.mod_service
        model.record_status = entity.audit_stamp.record_status.value

        # Convert directives to models
        if entity.directives:
            model.directives = [
                PatientEpisodeOfCareOrderDirectiveModel(
                    id=None,  # Let the database generate the ID
                    patient_episode_of_care_order_id=entity.id,  # Use the parent order's ID
                    instructions=directive.instructions,
                    tenant_id=entity.audit_stamp.tenant_id,
                    mod_at=entity.audit_stamp.mod_at,
                    mod_by=entity.audit_stamp.mod_by,
                    mod_service=entity.audit_stamp.mod_service,
                    record_status=entity.audit_stamp.record_status.value,
                )
                for directive in entity.directives
            ]

        return model
