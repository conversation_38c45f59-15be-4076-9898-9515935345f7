"""Appointment service API client"""

import httpx
from typing import List, Optional, Dict, Any
from src.config.settings import settings


class AppointmentServiceClient:
    """Client for appointment service API"""
    
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or settings.APPOINTMENT_SERVICE_URL
        self.timeout = 30.0
    
    async def get_appointments(self, appointment_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get appointments from appointment service"""
        
        # For now, return mock data (stub implementation)
        # In production, this would make actual API calls
        mock_appointments = [
            {
                "id": "apt-001",
                "patient_id": "patient-001",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 60,
                "status": "pending",
                "location": {
                    "latitude": 40.758,
                    "longitude": -73.9855,
                    "address": "123 Park Avenue, Apt 4B, New York, NY 10016"
                },
                "required_skills": ["wound_care", "medication_administration"],
                "priority": "high",
                "urgent": False,
                "care_episode_id": "episode-001",
                "service_type": "skilled_nursing",
                "is_pinned": False
            },
            {
                "id": "apt-002",
                "patient_id": "patient-002",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 45,
                "status": "pending",
                "location": {
                    "latitude": 40.7505,
                    "longitude": -73.9934,
                    "address": "456 Madison Avenue, Suite 8, New York, NY 10022"
                },
                "required_skills": ["basic_care", "vital_signs"],
                "priority": "normal",
                "urgent": False,
                "care_episode_id": "episode-002",
                "service_type": "home_health",
                "is_pinned": False
            },
            {
                "id": "apt-003",
                "patient_id": "patient-003",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 75,
                "status": "pending",
                "location": {
                    "latitude": 40.7505,
                    "longitude": -74.0134,
                    "address": "321 Broadway, Apt 15A, New York, NY 10007"
                },
                "required_skills": ["physical_therapy"],
                "priority": "normal",
                "urgent": False,
                "care_episode_id": "episode-003",
                "service_type": "physical_therapy",
                "is_pinned": False
            },
            {
                "id": "apt-004",
                "patient_id": "patient-004",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 30,
                "status": "pending",
                "location": {
                    "latitude": 40.7282,
                    "longitude": -73.7949,
                    "address": "789 Queens Boulevard, Apt 12C, Queens, NY 11373"
                },
                "required_skills": ["wound_care"],
                "priority": "high",
                "urgent": True,
                "care_episode_id": "episode-004",
                "service_type": "skilled_nursing",
                "is_pinned": False
            },
            {
                "id": "apt-005",
                "patient_id": "patient-005",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 90,
                "status": "pending",
                "location": {
                    "latitude": 40.6892,
                    "longitude": -74.0445,
                    "address": "456 Liberty Street, Apt 8B, Jersey City, NJ 07302"
                },
                "required_skills": ["post_surgical_care", "medication_administration"],
                "priority": "high",
                "urgent": False,
                "care_episode_id": "episode-005",
                "service_type": "skilled_nursing",
                "is_pinned": False
            },
            {
                "id": "apt-006",
                "patient_id": "patient-001",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 45,
                "status": "pending",
                "location": {
                    "latitude": 40.758,
                    "longitude": -73.9855,
                    "address": "123 Park Avenue, Apt 4B, New York, NY 10016"
                },
                "required_skills": ["personal_care"],
                "priority": "normal",
                "urgent": False,
                "care_episode_id": "episode-001",
                "service_type": "home_health",
                "is_pinned": False
            },
            {
                "id": "apt-007",
                "patient_id": "patient-002",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 60,
                "status": "pending",
                "location": {
                    "latitude": 40.7505,
                    "longitude": -73.9934,
                    "address": "456 Madison Avenue, Suite 8, New York, NY 10022"
                },
                "required_skills": ["medication_administration", "patient_assessment"],
                "priority": "normal",
                "urgent": False,
                "care_episode_id": "episode-002",
                "service_type": "skilled_nursing",
                "is_pinned": False
            },
            {
                "id": "apt-008",
                "patient_id": "patient-003",
                "provider_id": None,
                "appointment_date": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "duration_minutes": 45,
                "status": "pending",
                "location": {
                    "latitude": 40.7505,
                    "longitude": -74.0134,
                    "address": "321 Broadway, Apt 15A, New York, NY 10007"
                },
                "required_skills": ["mobility_assistance", "companionship"],
                "priority": "low",
                "urgent": False,
                "care_episode_id": "episode-003",
                "service_type": "home_health",
                "is_pinned": False
            },
            {
                "id": "apt-009",
                "patient_id": "patient-001",
                "provider_id": None,
                "appointment_date": "2025-06-25",
                "scheduled_start_time": "14:00",
                "scheduled_end_time": "15:00",
                "duration_minutes": 60,
                "status": "pending",
                "location": {
                    "latitude": 40.758,
                    "longitude": -73.9855,
                    "address": "123 Park Avenue, Apt 4B, New York, NY 10016"
                },
                "required_skills": ["wound_care"],
                "priority": "high",
                "urgent": False,
                "care_episode_id": "episode-001",
                "service_type": "skilled_nursing",
                "is_pinned": True,
                "pinned_date": "2025-06-25",
                "pinned_time": "14:00",
                "pinned_reason": "Patient requested specific time for medication schedule"
            }
        ]
        
        # Filter by appointment_ids if provided
        if appointment_ids:
            mock_appointments = [a for a in mock_appointments if a["id"] in appointment_ids]
        
        return mock_appointments
    
    async def get_appointment(self, appointment_id: str) -> Optional[Dict[str, Any]]:
        """Get single appointment by ID"""
        appointments = await self.get_appointments([appointment_id])
        return appointments[0] if appointments else None
    
    async def get_pending_appointments(self, date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get pending appointments for scheduling"""
        appointments = await self.get_appointments()
        return [apt for apt in appointments if apt["status"] == "pending"]
    
    async def update_appointments(self, appointments: List[Dict[str, Any]]) -> bool:
        """Update appointments with scheduling results"""
        # In production, this would make API calls to update appointments
        # For now, just return success
        return True
