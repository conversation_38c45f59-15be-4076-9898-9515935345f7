service_type: physical_therapy
required_skills:
  - "Physical Therapist (PT)"
  - "Physical Therapy"
  - "Exercise Therapy"
  - "Mobility Training"
  - "Pain Management"
  - "physical_therapy"
  - "mobility_assistance"
  - "exercise_therapy"
  - "rehabilitation"

geographic_radius_miles: 25.0
max_daily_appointments_per_provider: 8
max_weekly_hours_per_provider: 40

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.8
workload_balance_weight: 0.6
geographic_clustering_weight: 0.5
patient_preference_weight: 0.7
capacity_threshold_percentage: 0.9

# Service-specific settings
visit_duration_minutes: 45
requires_initial_assessment: true
allows_weekend_visits: true
emergency_response_time_hours: 24

# Geographic clustering settings
cluster_radius_miles: 12.0
max_cluster_size: 10
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 80
continuity_threshold_days: 45  # Longer continuity for PT
existing_relationship_bonus: 40

# Workload balancing settings
target_daily_appointments: 6
workload_variance_tolerance: 3
overtime_penalty_multiplier: 1.3

# Patient preference settings
preferred_provider_bonus: 25
preferred_time_bonus: 15
preferred_location_bonus: 20

# Skill matching settings
strict_skill_matching: false  # PT can be more flexible
allow_skill_hierarchy: true
skill_mismatch_penalty: 150

# Time constraints
min_visit_duration_minutes: 30
max_visit_duration_minutes: 90
travel_buffer_minutes: 10
setup_time_minutes: 5

# Quality metrics
quality_score_weight: 0.4
patient_satisfaction_weight: 0.3
provider_efficiency_weight: 0.3
