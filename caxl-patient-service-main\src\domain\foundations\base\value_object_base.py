from __future__ import annotations

import json
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, TypeVar, get_args, get_origin

T_VO = TypeVar("T_VO", bound="ValueObjectBase")


class ValueObjectBase:
    """
    Base class for DDD value objects.

    Key characteristics
    -------------------
    • **Immutability**  – frozen after __init__ (attempts to mutate raise).
    • **Equality by value** – __eq__ / __hash__ use the encoded value set.
    • **(De)serialisation helpers** – same API as EntityBase.
    """

    # ─────────────────────────  IMMUTABILITY  ────────────────────────
    _frozen: bool = False

    def __setattr__(self, name: str, value: Any) -> None:
        if getattr(self, "_frozen", False):
            raise AttributeError(f"{self.__class__.__name__} is immutable")
        super().__setattr__(name, value)

    def _freeze(self) -> None:
        """Call at the *end* of subclass __init__."""
        object.__setattr__(self, "_frozen", True)

    # ─────────────────────  (DE)SERIALISATION  ───────────────────────
    def _encode(self, obj: Any) -> Any:
        if obj is None or isinstance(obj, str | int | float | bool):
            return obj
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, "to_dict"):
            return obj.to_dict()
        if isinstance(obj, list | tuple | set):
            return [self._encode(o) for o in obj]
        raise TypeError(f"Cannot serialise {obj!r}")

    def to_dict(self) -> dict[str, Any]:
        return {k: self._encode(v) for k, v in self.__dict__.items() if not k.startswith("_")}

    @classmethod
    def from_dict(cls: type[T_VO], data: dict[str, Any]) -> T_VO:
        hints = cls.__annotations__
        kwargs: dict[str, Any] = {}

        for key, value in data.items():
            expected = hints.get(key)
            if expected is None:
                kwargs[key] = value
                continue

            origin = get_origin(expected)
            args = get_args(expected)

            if expected is uuid.UUID:
                kwargs[key] = uuid.UUID(value)
            elif expected is datetime:
                kwargs[key] = datetime.fromisoformat(value)
            elif isinstance(expected, type) and issubclass(expected, Enum):
                kwargs[key] = expected(value)
            elif hasattr(expected, "from_dict"):
                kwargs[key] = expected.from_dict(value)
            elif origin in (list, tuple, set) and args:
                inner = args[0]
                kwargs[key] = origin(
                    inner.from_dict(v) if hasattr(inner, "from_dict") else v for v in value  # type: ignore
                )
            else:
                kwargs[key] = value

        obj = cls(**kwargs)  # type: ignore[arg-type]
        return obj

    def to_json(self, *, indent: int | None = None) -> str:
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)

    @classmethod
    def from_json(cls: type[T_VO], payload: str) -> T_VO:
        return cls.from_dict(json.loads(payload))

    # ───────────────────────  EQUALITY / HASH  ───────────────────────
    def __eq__(self, other: object) -> bool:
        return isinstance(other, self.__class__) and self.to_dict() == other.to_dict()

    def __hash__(self) -> int:  # frozen ⇒ hashable
        # stable hash from sorted key/value JSON
        return hash(json.dumps(self.to_dict(), sort_keys=True, default=str))

    # ─────────────────────────  VALIDATION  ──────────────────────────
    # @abstractmethod
    def validate(self) -> None:
        """Concrete value objects enforce their invariants here."""
        raise NotImplementedError(
            f"{self.__class__.__name__} must implement the 'validate' method."
        )

    def validate_round_trip(self) -> None:
        if self.from_dict(self.to_dict()).to_dict() != self.to_dict():
            raise ValueError("dict ⇆ object round-trip mismatch")

    # ───────────────────────────  MISC  ──────────────────────────────
    def __repr__(self) -> str:  # pragma: no cover
        return f"<{self.__class__.__name__} {self.to_dict()}>"
