"""Base model for all repositories"""

from datetime import UTC, datetime
from uuid import uuid4

from sqlalchemy import Column, Enum, String
from sqlalchemy.dialects.postgresql import TIMESTAMP, UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.infrastructure.adapters.persistence.base import Base


class BaseModel(Base):
    """Base model with common fields"""

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    tenant_id = Column(String, nullable=False, index=True)
    mod_at = Column(
        TIMESTAMP(timezone=True),
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC),
    )
    mod_by = Column(String, nullable=False)
    mod_service = Column(String, nullable=False)
    record_status = Column(
        Enum(RecordStatusEnum, name="record_status", create_type=False),
        nullable=False,
        default=RecordStatusEnum.ACTIVE,
    )

    def apply_audit_stamp(self, audit_stamp: AuditStamp):
        self.record_status = audit_stamp.record_status
        self.tenant_id = audit_stamp.tenant_id
        self.mod_by = audit_stamp.mod_by
        self.mod_at = audit_stamp.mod_at
        self.mod_service = audit_stamp.mod_service if audit_stamp.mod_service else "PatientService"
