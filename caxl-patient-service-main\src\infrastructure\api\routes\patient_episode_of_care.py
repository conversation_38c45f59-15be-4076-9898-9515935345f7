"""FastAPI routes for patient episode of care operations"""

import traceback
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query

from src.application.dtos.patient_episode_of_care_dto import (
    PatientEpisodeOfCareResponseDTO,
)
from src.application.usecases.patient_episode_of_care_usecase import PatientEpisodeOfCareUseCase
from src.config.logging import logging
from src.container import Container
from src.infrastructure.api.dependencies.headers import get_tenant_id, get_user_id
from src.infrastructure.api.dependencies.session import get_user_context
from src.infrastructure.api.request_mappers.patient_episode_of_care_mapper import (
    map_create_request_to_dto,
    map_update_request_to_dto,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient_episode_of_care_schema import (
    PatientEpisodeOfCareCreateSchema,
    PatientEpisodeOfCareGetByIdResponseSchema,
    PatientEpisodeOfCareListResponseSchema,
    PatientEpisodeOfCareUpdateSchema,
)

logger = logging.getLogger(__name__)


async def get_patient_episode_use_case() -> PatientEpisodeOfCareUseCase:
    """Get patient episode of care use case instance"""
    container = Container()
    return container.patient_episode_use_case()


router = APIRouter(
    prefix="/patient-eoc",
    tags=["patient-episodes"],
    dependencies=[
        Depends(get_tenant_id),
        Depends(get_user_id),
    ],
)


@router.post("", response_model=PatientEpisodeOfCareResponseDTO)
async def create_patient_episode_route(
    request: PatientEpisodeOfCareCreateSchema,
    usecase: PatientEpisodeOfCareUseCase = Depends(get_patient_episode_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareResponseDTO:
    """Create a new patient episode of care"""
    try:
        episode_dto = map_create_request_to_dto(request, user_context)
        return await usecase.create(episode_dto, context=user_context)
    except Exception as e:
        logger.error(f"Error creating patient episode: {e!s}")
        traceback.print_exc()
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("")
async def get_patient_episodes_route(
    usecase: PatientEpisodeOfCareUseCase = Depends(get_patient_episode_use_case),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
) -> PatientEpisodeOfCareListResponseSchema:
    """Get a list of patient episodes of care"""
    try:
        episodes = await usecase.get_all(page=page, page_size=page_size)
        total_count = await usecase.count()
        total_pages = (total_count + page_size - 1) // page_size

        return PatientEpisodeOfCareListResponseSchema(
            episodes=episodes,
            page=page,
            page_size=page_size,
            total=total_count,
            total_pages=total_pages,
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error listing patient episodes: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("/{episode_id}", response_model=PatientEpisodeOfCareGetByIdResponseSchema)
async def get_patient_episode_by_id_route(
    episode_id: UUID,
    usecase: PatientEpisodeOfCareUseCase = Depends(get_patient_episode_use_case),
) -> PatientEpisodeOfCareGetByIdResponseSchema:
    """Get a patient episode of care by ID"""
    try:
        episode = await usecase.get_by_id(episode_id)
        if not episode:
            raise HTTPException(status_code=404, detail="Patient episode not found")
        return episode
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error getting patient episode: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.put("/{episode_id}")
async def update_patient_episode_route(
    episode_id: UUID,
    request: PatientEpisodeOfCareUpdateSchema,
    usecase: PatientEpisodeOfCareUseCase = Depends(get_patient_episode_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> PatientEpisodeOfCareResponseDTO:
    """Update a patient episode of care"""
    try:
        episode_dto = map_update_request_to_dto(request, user_context)
        return await usecase.update(episode_dto, context=user_context)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error updating patient episode: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.delete("/{episode_id}")
async def delete_patient_episode_route(
    episode_id: UUID,
    usecase: PatientEpisodeOfCareUseCase = Depends(get_patient_episode_use_case),
    user_context: AuthSession = Depends(get_user_context),
) -> dict:
    """Delete a patient episode of care"""
    try:
        success = await usecase.delete(episode_id, context=user_context)
        if not success:
            raise HTTPException(status_code=404, detail="Patient episode not found")
        return {"message": "Patient episode deleted successfully"}
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error deleting patient episode: {e!s}")
        raise HTTPException(status_code=400, detail=str(e)) from e
