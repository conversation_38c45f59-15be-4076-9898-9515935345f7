"""Patient Aggregate"""

from typing import Any
from uuid import UUID

from src.domain.entities.caregiver import Caregiver
from src.domain.entities.email import EmailInfo
from src.domain.entities.emergency_contact import EmergencyContact
from src.domain.entities.medical_profile import MedicalProfile
from src.domain.entities.patient_episode_of_care import PatientEpisodeOfCareEntity
from src.domain.entities.patient_pii import PatientPII
from src.domain.entities.patient_preferences import PatientPreferences
from src.domain.entities.phone import PhoneInfo
from src.domain.entities.referring_info import ReferringInformation
from src.domain.events.patient_events import (
    CaregiverAssignedEvent,
    PatientCreatedEvent,
    PatientUpdatedEvent,
)
from src.domain.exceptions.base import ValidationError
from src.domain.foundations.base.aggregate_base import AggregateRoot
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.record_status_enum import RecordStatusEnum


class Patient(AggregateRoot):
    """Patient Aggregate Root with improved encapsulation and invariants"""

    def __init__(
        self,
        audit_stamp: AuditStamp,
        pii: PatientPII,
        id: UUID | None = None,
        preferences: PatientPreferences | None = None,
        profile: MedicalProfile | None = None,
        caregivers: list[Caregiver] | None = None,
        referring_mrns: list[ReferringInformation] | None = None,
        episodes_of_care: list[PatientEpisodeOfCareEntity] | None = None,
        mpu_id: str | None = None,
        nhid: str | None = None,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        if caregivers is None:
            caregivers = []
        if referring_mrns is None:
            referring_mrns = []
        if episodes_of_care is None:
            episodes_of_care = []
        self._pii = pii
        self._preferences = preferences
        self._profile = profile
        self._caregivers = caregivers
        self._referring_mrns = referring_mrns
        self._episodes_of_care = episodes_of_care
        self._mpu_id = mpu_id
        self._nhid = nhid
        self._domain_events: list[Any] = []

        # Validate invariants
        self._ensure_invariants()

        # Raise creation event
        self._domain_events.append(PatientCreatedEvent(aggregate_id=self.id))

    def _ensure_invariants(self) -> None:
        """Ensure all business rules and invariants are satisfied"""
        if not self._pii:
            raise ValidationError("Patient PII information is required")

        # if not self._caregivers:
        #     raise ValidationError("Active patient must have at least one caregiver")

    @property
    def pii(self) -> PatientPII:
        return self._pii

    @pii.setter
    def pii(self, value: PatientPII) -> None:
        self._pii = value

    @property
    def preferences(self) -> PatientPreferences | None:
        return self._preferences

    @property
    def profile(self) -> MedicalProfile | None:
        return self._profile

    @property
    def caregivers(self) -> list[Caregiver]:
        return self._caregivers

    @property
    def referring_mrns(self) -> list[ReferringInformation]:
        return self._referring_mrns

    @property
    def episodes_of_care(self) -> list[PatientEpisodeOfCareEntity]:
        return self._episodes_of_care

    @property
    def mpu_id(self) -> str | None:
        return self._mpu_id

    @property
    def is_active(self) -> bool:
        return self.audit_stamp.record_status == RecordStatusEnum.ACTIVE

    @property
    def nhid(self) -> str | None:
        return self._nhid

    @property
    def domain_events(self) -> list[Any]:
        return self._domain_events

    def add_phone(self, phone: PhoneInfo) -> None:
        """Add a new phone number to the patient's contact information"""
        if phone.is_primary:
            # Remove primary status from other phones
            for existing_phone in self._pii.contact_info.phones:
                if existing_phone.is_primary:
                    object.__setattr__(existing_phone, "is_primary", False)
        self._pii.contact_info.phones.append(phone)

    def add_email(self, email: EmailInfo) -> None:
        """Add a new email address to the patient's contact information"""
        self._pii.contact_info.emails.append(email)

    def add_emergency_contact(self, contact: EmergencyContact) -> None:
        """Add a new emergency contact"""
        self._pii.contact_info.emergency_contacts.append(contact)

    def get_primary_phone(self) -> PhoneInfo | None:
        """Get the primary phone number"""
        return next((phone for phone in self._pii.contact_info.phones if phone.is_primary), None)

    def add_caregiver(self, caregiver: Caregiver) -> None:
        """Add a new caregiver to the patient's care team"""
        self._caregivers.append(caregiver)

        # Raise domain event
        self._domain_events.append(
            CaregiverAssignedEvent(aggregate_id=self.id, caregiver_id=caregiver.id)
        )

        # Revalidate invariants
        self._ensure_invariants()

    def remove_caregiver(self, caregiver_id: UUID) -> None:
        """Remove a caregiver from the patient's care team"""
        if not any(c.id == caregiver_id for c in self._caregivers):
            raise ValidationError("Caregiver not found")

        self._caregivers = [c for c in self._caregivers if c.id != caregiver_id]

        # Revalidate invariants
        self._ensure_invariants()

    def add_referring_mrn(self, mrn: ReferringInformation) -> None:
        """Add a new referring MRN"""
        if any(
            existing_mrn.referring_mrn == mrn.referring_mrn for existing_mrn in self._referring_mrns
        ):
            raise ValidationError("MRN already exists")

        self._referring_mrns.append(mrn)

        # Revalidate invariants
        self._ensure_invariants()

    def update_pii(self, pii: PatientPII) -> None:
        """Update the patient's PII information"""
        if not pii:
            raise ValidationError("PII cannot be null")

        self._pii = pii

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"pii": pii})
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_preferences(self, new_preferences: PatientPreferences) -> None:
        """Update the patient's preferences"""
        if not new_preferences:
            raise ValidationError("Preferences cannot be null")

        self._preferences = new_preferences

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(
                aggregate_id=self.id, updated_fields={"preferences": new_preferences}
            )
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_profile(self, new_profile: MedicalProfile) -> None:
        """Update the patient's medical profile"""
        if not new_profile:
            raise ValidationError("Profile cannot be null")

        self._profile = new_profile

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"profile": new_profile})
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_caregivers(self, caregivers: list[Caregiver]) -> None:
        """Update the patient's caregivers"""
        if not caregivers:
            raise ValidationError("Caregivers list cannot be empty")

        self._caregivers = caregivers

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"caregivers": caregivers})
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_referring_mrns(self, referring_mrns: list[ReferringInformation]) -> None:
        """Update the patient's referring MRNs"""
        if not referring_mrns:
            raise ValidationError("Referring MRNs list cannot be empty")

        # Validate no duplicate MRNs
        mrn_values = [mrn.referring_mrn for mrn in referring_mrns if mrn.referring_mrn]
        if len(mrn_values) != len(set(mrn_values)):
            raise ValidationError("Duplicate MRNs are not allowed")

        self._referring_mrns = referring_mrns

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(
                aggregate_id=self.id, updated_fields={"referring_mrns": referring_mrns}
            )
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_mpu_id(self, mpu_id: str, audit_stamp: AuditStamp) -> None:
        """Update the patient's MPU ID"""
        if not mpu_id or not mpu_id.strip():
            raise ValidationError("MPU ID cannot be empty")

        self._mpu_id = mpu_id.strip()
        self.audit_stamp = audit_stamp

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"mpu_id": self._mpu_id})
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_nhid(self, nhid: str, audit_stamp: AuditStamp) -> None:
        """Update the patient's NHID"""
        if not nhid or not nhid.strip():
            raise ValidationError("NHID cannot be empty")

        self._nhid = nhid.strip()
        self.audit_stamp = audit_stamp

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"nhid": self._nhid})
        )

        # Revalidate invariants
        self._ensure_invariants()

    def add_episode_of_care(self, episode: PatientEpisodeOfCareEntity) -> None:
        """Add a new episode of care"""
        self._episodes_of_care.append(episode)

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(
                aggregate_id=self.id, updated_fields={"episodes_of_care": self._episodes_of_care}
            )
        )

        # Revalidate invariants
        self._ensure_invariants()

    def update_episodes_of_care(self, episodes: list[PatientEpisodeOfCareEntity]) -> None:
        """Update the patient's episodes of care"""
        if not episodes:
            raise ValidationError("Episodes of care list cannot be empty")

        self._episodes_of_care = episodes

        # Raise domain event
        self._domain_events.append(
            PatientUpdatedEvent(aggregate_id=self.id, updated_fields={"episodes_of_care": episodes})
        )

        # Revalidate invariants
        self._ensure_invariants()
