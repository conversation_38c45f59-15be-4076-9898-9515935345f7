#!/usr/bin/env python3
"""
Healthcare Scheduling System - Scenario Demo

This script demonstrates how to use different scenarios to showcase
various features of the healthcare appointment scheduling system.
"""

import subprocess
import sys
import time
import yaml
from pathlib import Path

print("DEBUG: Script starting...")  # Debug output


def run_command(command, description, timeout=30):
    """Run a command and display the result with timeout."""
    print(f"\n🔄 {description}")
    print("=" * 50)
    print(f"Command: {command}")
    print("-" * 30)
    
    try:
        # Use timeout to prevent hanging
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print("✅ Command completed successfully")
            if result.stdout:
                print("Output:")
                print(result.stdout[-500:])  # Show last 500 chars
        else:
            print("❌ Command failed")
            if result.stderr:
                print("Error:")
                print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"⏰ Command timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False


def show_scenario_data(scenario_name):
    """Show the data for a specific scenario without running optimization."""
    print(f"\n📊 SCENARIO DATA: {scenario_name.upper()}")
    print("=" * 50)
    
    scenario_path = Path(f"data/scenarios/{scenario_name}")
    if not scenario_path.exists():
        print(f"❌ Scenario {scenario_name} not found")
        return False
    
    # Show providers
    providers_file = scenario_path / "providers.yml"
    if providers_file.exists():
        with open(providers_file, 'r') as f:
            providers_data = yaml.safe_load(f)
            print(f"👨‍⚕️  Providers: {len(providers_data.get('providers', []))}")
            for provider in providers_data.get('providers', [])[:3]:  # Show first 3
                print(f"   - {provider.get('name', 'Unknown')} ({', '.join(provider.get('skills', []))})")
            if len(providers_data.get('providers', [])) > 3:
                print(f"   ... and {len(providers_data.get('providers', [])) - 3} more")
    
    # Show consumers
    consumers_file = scenario_path / "consumers.yml"
    if consumers_file.exists():
        with open(consumers_file, 'r') as f:
            consumers_data = yaml.safe_load(f)
            print(f"👥 Consumers: {len(consumers_data.get('consumers', []))}")
            for consumer in consumers_data.get('consumers', [])[:3]:  # Show first 3
                print(f"   - {consumer.get('name', 'Unknown')} ({consumer.get('location', {}).get('city', 'Unknown')})")
            if len(consumers_data.get('consumers', [])) > 3:
                print(f"   ... and {len(consumers_data.get('consumers', [])) - 3} more")
    
    # Show appointments
    appointments_file = scenario_path / "appointments.yml"
    if appointments_file.exists():
        with open(appointments_file, 'r') as f:
            appointments_data = yaml.safe_load(f)
            print(f"📅 Appointments: {len(appointments_data.get('appointments', []))}")
            for appointment in appointments_data.get('appointments', [])[:3]:  # Show first 3
                skills = ', '.join(appointment.get('required_skills', []))
                print(f"   - {appointment.get('appointment_type', 'Unknown')} ({skills})")
            if len(appointments_data.get('appointments', [])) > 3:
                print(f"   ... and {len(appointments_data.get('appointments', [])) - 3} more")
    
    return True


def demo_basic_scenario():
    """Demo the basic scenario."""
    print("\n🏥 BASIC DEMO SCENARIO")
    print("=" * 50)
    print("This demonstrates core scheduling functionality with:")
    print("- Basic appointment assignment")
    print("- Provider skill matching")
    print("- Geographic service areas")
    print("- Simple workload distribution")
    
    # Show scenario data
    show_scenario_data("basic_demo")
    
    # Switch to basic demo (fast operation)
    success = run_command("python switch_scenario.py switch basic_demo", "Switching to Basic Demo Scenario", timeout=10)
    if not success:
        return False
    
    print("\n✅ Basic demo scenario loaded successfully!")
    print("💡 To run the full optimization, use:")
    print("   python -m src.appointment_scheduler.jobs.assign_appointments")
    print("   python -m src.appointment_scheduler.jobs.day_plan")
    return True


def demo_geographic_clustering():
    """Demo geographic clustering."""
    print("\n🗺️  GEOGRAPHIC CLUSTERING SCENARIO")
    print("=" * 50)
    print("This demonstrates geographic optimization with:")
    print("- Geographic clustering optimization")
    print("- Service area constraints")
    print("- Provider location optimization")
    print("- Travel time minimization")
    
    # Show scenario data
    show_scenario_data("geographic_clustering")
    
    # Switch to geographic clustering (fast operation)
    success = run_command("python switch_scenario.py switch geographic_clustering", "Switching to Geographic Clustering Scenario", timeout=10)
    if not success:
        return False
    
    print("\n✅ Geographic clustering scenario loaded successfully!")
    print("💡 To run the full optimization, use:")
    print("   python -m src.appointment_scheduler.jobs.assign_appointments")
    print("   python -m src.appointment_scheduler.jobs.day_plan")
    return True


def demo_continuity_of_care():
    """Demo continuity of care."""
    print("\n👥 CONTINUITY OF CARE SCENARIO")
    print("=" * 50)
    print("This demonstrates care continuity with:")
    print("- Continuity of care optimization")
    print("- Provider-patient relationship maintenance")
    print("- Care episode grouping")
    print("- Historical assignment consideration")
    
    # Show scenario data
    show_scenario_data("continuity_of_care")
    
    # Switch to continuity of care (fast operation)
    success = run_command("python switch_scenario.py switch continuity_of_care", "Switching to Continuity of Care Scenario", timeout=10)
    if not success:
        return False
    
    print("\n✅ Continuity of care scenario loaded successfully!")
    print("💡 To run the full optimization, use:")
    print("   python -m src.appointment_scheduler.jobs.assign_appointments")
    print("   python -m src.appointment_scheduler.jobs.day_plan")
    return True


def demo_skill_hierarchy():
    """Demo skill hierarchy."""
    print("\n🎯 SKILL HIERARCHY SCENARIO")
    print("=" * 50)
    print("This demonstrates skill matching with:")
    print("- Perfect skill matches")
    print("- Skill hierarchy assignments")
    print("- No-match scenarios")
    print("- Multiple skill requirements")
    
    # Show scenario data
    show_scenario_data("skill_hierarchy")
    
    # Switch to skill hierarchy (fast operation)
    success = run_command("python switch_scenario.py switch skill_hierarchy", "Switching to Skill Hierarchy Scenario", timeout=10)
    if not success:
        return False
    
    print("\n✅ Skill hierarchy scenario loaded successfully!")
    print("💡 To run the full optimization, use:")
    print("   python -m src.appointment_scheduler.jobs.assign_appointments")
    print("   python -m src.appointment_scheduler.jobs.day_plan")
    return True


def show_feature_toggles():
    """Show how to configure feature toggles."""
    print("\n⚙️  FEATURE TOGGLE CONFIGURATION")
    print("=" * 50)
    print("You can enable/disable features in config/scheduler.yml:")
    print()
    print("Basic Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: false")
    print("  enable_provider_capacity_management: false")
    print("  enable_route_optimization: false")
    print()
    print("Premium Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: false")
    print()
    print("Enterprise Plan Features:")
    print("  enable_geographic_clustering: true")
    print("  enable_continuity_of_care: true")
    print("  enable_workload_balancing: true")
    print("  enable_patient_preferences: true")
    print("  enable_provider_capacity_management: true")
    print("  enable_route_optimization: true")
    print("  enable_advanced_traffic_integration: true")
    print()
    print("💡 To modify these settings, edit config/scheduler.yml")


def show_available_scenarios():
    """Show all available scenarios."""
    print("\n📋 AVAILABLE SCENARIOS")
    print("=" * 50)
    
    scenarios_path = Path("data/scenarios")
    if not scenarios_path.exists():
        print("❌ No scenarios directory found")
        return
    
    scenarios = [d.name for d in scenarios_path.iterdir() if d.is_dir()]
    scenarios.sort()
    
    print(f"Found {len(scenarios)} scenarios:")
    for i, scenario in enumerate(scenarios, 1):
        readme_file = scenarios_path / scenario / "README.md"
        if readme_file.exists():
            with open(readme_file, 'r') as f:
                first_line = f.readline().strip()
                if first_line.startswith('#'):
                    description = first_line[1:].strip()
                else:
                    description = "No description available"
        else:
            description = "No description available"
        
        print(f"{i:2d}. {scenario:<25} - {description}")
    
    print(f"\n💡 To switch to any scenario, use:")
    print(f"   python switch_scenario.py switch <scenario_name>")


def main():
    """Main demo function."""
    print("DEBUG: Main function called")  # Debug output
    print("🏥 Healthcare Scheduling System - Scenario Demo")
    print("=" * 55)
    print()
    print("This demo showcases different scenarios and features")
    print("of the healthcare appointment scheduling system.")
    print()
    
    print(f"DEBUG: Arguments: {sys.argv}")  # Debug output
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python demo_scenarios.py basic          - Run basic demo")
        print("  python demo_scenarios.py geographic     - Run geographic clustering demo")
        print("  python demo_scenarios.py continuity     - Run continuity of care demo")
        print("  python demo_scenarios.py skills         - Run skill hierarchy demo")
        print("  python demo_scenarios.py all            - Run all demos")
        print("  python demo_scenarios.py toggles        - Show feature toggle examples")
        print("  python demo_scenarios.py scenarios      - Show all available scenarios")
        print()
        return
    
    command = sys.argv[1].lower()
    print(f"DEBUG: Command: {command}")  # Debug output
    
    if command == "basic":
        demo_basic_scenario()
    elif command == "geographic":
        demo_geographic_clustering()
    elif command == "continuity":
        demo_continuity_of_care()
    elif command == "skills":
        demo_skill_hierarchy()
    elif command == "all":
        print("Running all demos...")
        demo_basic_scenario()
        time.sleep(1)
        demo_geographic_clustering()
        time.sleep(1)
        demo_continuity_of_care()
        time.sleep(1)
        demo_skill_hierarchy()
    elif command == "toggles":
        show_feature_toggles()
    elif command == "scenarios":
        show_available_scenarios()
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'basic', 'geographic', 'continuity', 'skills', 'all', 'toggles', or 'scenarios'")


if __name__ == "__main__":
    print("DEBUG: Script main block")  # Debug output
    main() 