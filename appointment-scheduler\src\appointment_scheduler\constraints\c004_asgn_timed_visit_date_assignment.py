"""
Timed Visit Date Assignment Constraint (C004)

This constraint ensures that provider roles match appointment requirements when specified.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import AppointmentAssignment
def provider_role_match(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider role should match appointment requirements when specified."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and 
                                      assignment.appointment_data.required_role is not None and
                                      assignment.provider.role != assignment.appointment_data.required_role))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Provider role match")) 