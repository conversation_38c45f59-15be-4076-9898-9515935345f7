from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.domain.enums import CaregiverRelationship
from src.infrastructure.adapters.persistence.models.base import BaseModel


class PatientCaregiver(BaseModel):
    __tablename__ = "patient_caregiver"
    # Overriding the default 'id' primary key from BaseModel
    id = Column("patient_caregiver_id", UUID(as_uuid=True), primary_key=True)
    patient_id = Column(UUID(as_uuid=True), ForeignKey("patient.patient_id"))
    caregiver_relationship = Column(
        Enum(CaregiverRelationship, name="caregiver_relationship", create_type=False),
        nullable=False,
    )
    caregiver_firstname = Column(String)
    caregiver_lastname = Column(String)
    caregiver_gender = Column(String)
    caregiver_email = Column(String)
    caregiver_email_verified = Column(Boolean)
    caregiver_phone_number = Column(String)
    caregiver_phone_country_code = Column(String)
    caregiver_phone_verified = Column(Boolean)
    location_id = Column(UUID(as_uuid=True))

    patient = relationship("Patient", back_populates="caregivers", lazy="joined")
