# Geographic Edge Cases

**Purpose**: Test geographic edge cases with various edge cases
**Best for**: Demonstrating robust geographic edge cases handling
**Complexity**: High

## Edge Cases Covered

### Within_area
- within area scenarios
- Edge case handling
- Error recovery

### Outside_area
- outside area scenarios
- Edge case handling
- Error recovery

### Multiple_areas
- multiple areas scenarios
- Edge case handling
- Error recovery

### Boundary_case
- boundary case scenarios
- Edge case handling
- Error recovery

### Cross_state
- cross state scenarios
- Edge case handling
- Error recovery

### Invalid_coordinates
- invalid coordinates scenarios
- Edge case handling
- Error recovery

### Null_location
- null location scenarios
- Edge case handling
- Error recovery

### International
- international scenarios
- Edge case handling
- Error recovery

### Rural_areas
- rural areas scenarios
- Edge case handling
- Error recovery

## Data Overview
- **Providers**: 6-10 (with various configurations)
- **Patients**: 8-15 (with diverse requirements)
- **Appointments**: 12-20 (testing all edge cases)

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/geographic_edge_cases/* data/

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Edge cases should be handled gracefully
- System should not crash on invalid data
- Appropriate error messages should be logged
- Valid cases should still work correctly
