"""Health check routes"""

from fastapi import APIRouter, status
from pydantic import BaseModel

from src.config.settings import settings


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    service: str
    version: str
    environment: str


router = APIRouter()


@router.get(
    "/health",
    response_model=HealthResponse,
    status_code=status.HTTP_200_OK,
    summary="Health Check",
    description="Check if the service is healthy and running",
)
async def health_check() -> HealthResponse:
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        service=settings.PROJECT_NAME,
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
    )


@router.get(
    "/ready",
    response_model=HealthResponse,
    status_code=status.HTTP_200_OK,
    summary="Readiness Check",
    description="Check if the service is ready to accept requests",
)
async def readiness_check() -> HealthResponse:
    """Readiness check endpoint for Kubernetes"""
    # In a full implementation, this would check database connectivity,
    # external service availability, etc.
    return HealthResponse(
        status="ready",
        service=settings.PROJECT_NAME,
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
    )
