"""Scheduling API routes"""

from datetime import date
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from src.config.settings import settings
from src.application.services.scheduling_engine import SchedulingEngine


class SchedulingRequest(BaseModel):
    """Scheduling request model"""
    appointment_ids: List[str] = []
    target_date: str = ""
    constraints: Dict[str, Any] = {}


class SchedulingResponse(BaseModel):
    """Scheduling response model"""
    success: bool
    message: str
    scheduled_count: int
    failed_count: int
    processing_time_seconds: float
    details: Dict[str, Any] = {}


class DayPlanRequest(BaseModel):
    """Day plan request model"""
    target_date: str


class DayPlanResponse(BaseModel):
    """Day plan response model"""
    success: bool
    message: str
    assigned_count: int
    processing_time_seconds: float
    visit_order: List[Dict[str, Any]] = []


router = APIRouter()


@router.post(
    "/assign",
    response_model=SchedulingResponse,
    status_code=status.HTTP_200_OK,
    summary="Assign Appointments",
    description="Run appointment assignment optimization",
)
async def assign_appointments(request: SchedulingRequest) -> SchedulingResponse:
    """Assign appointments to providers"""

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run assignment
    result = await engine.assign_appointments(
        appointment_ids=request.appointment_ids if request.appointment_ids else None,
        target_date=request.target_date if request.target_date else None,
        constraints=request.constraints
    )

    return SchedulingResponse(
        success=result["success"],
        message=result["message"],
        scheduled_count=result["scheduled_count"],
        failed_count=result["failed_count"],
        processing_time_seconds=result["processing_time_seconds"],
        details=result.get("details", {})
    )


@router.post(
    "/dayplan",
    response_model=DayPlanResponse,
    status_code=status.HTTP_200_OK,
    summary="Run Day Plan",
    description="Optimize daily schedule and routing",
)
async def run_dayplan(request: DayPlanRequest) -> DayPlanResponse:
    """Run day plan optimization"""

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run day plan
    result = await engine.run_day_plan(
        target_date=request.target_date
    )

    return DayPlanResponse(
        success=result["success"],
        message=result["message"],
        assigned_count=result["assigned_count"],
        processing_time_seconds=result["processing_time_seconds"],
        visit_order=result["visit_order"]
    )


@router.get(
    "/status",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Scheduling Status",
    description="Get current scheduling system status",
)
async def get_scheduling_status() -> Dict[str, Any]:
    """Get scheduling system status"""
    
    return {
        "status": "operational",
        "solver_engine": "timefold",
        "version": settings.VERSION,
        "features": {
            "assignment_optimization": True,
            "day_plan_optimization": True,
            "route_optimization": True,
            "constraint_satisfaction": True
        },
        "performance": {
            "avg_assignment_time_seconds": 30.0,
            "avg_dayplan_time_seconds": 60.0,
            "success_rate_percent": 95.0
        }
    }
