"""CareAxl Scheduling Engine API routes - Core optimization endpoints only"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, status
from pydantic import BaseModel

from src.config.settings import settings
from src.application.services.scheduling_engine import SchedulingEngine


class AppointmentAssignmentRequest(BaseModel):
    """Request model for appointment assignment

    The engine fetches:
    - Existing appointments as batch (pending assignments)
    - Available providers
    - Consumers/patients for those appointments
    Then assigns providers and dates with rolling window scheduling
    """
    appointment_batch_ids: Optional[List[str]] = None  # Specific appointment IDs to process
    target_date_range: Optional[Dict[str, str]] = None  # Rolling window: {"start": "2025-06-25", "end": "2025-07-01"}
    constraints: Optional[Dict[str, Any]] = None  # Additional assignment constraints


class AppointmentAssignmentResponse(BaseModel):
    """Response model for appointment assignment results"""
    success: bool
    message: str
    assigned_count: int  # Number of appointments successfully assigned
    failed_count: int    # Number of appointments that couldn't be assigned
    processing_time_seconds: float
    assignments: List[Dict[str, Any]] = []  # List of appointment assignments with provider and date
    details: Dict[str, Any] = {}  # Algorithm details, constraint violations, etc.


class DayPlanRequest(BaseModel):
    """Request model for day plan optimization

    The engine fetches:
    - Scheduled appointments for the target date
    - Assigned providers for those appointments
    - Patient locations and requirements
    Then optimizes timing and routing for the day
    """
    target_date: str  # Date to optimize (YYYY-MM-DD format)
    provider_ids: Optional[List[str]] = None  # Specific providers to optimize (optional)


class DayPlanResponse(BaseModel):
    """Response model for day plan optimization results"""
    success: bool
    message: str
    optimized_count: int  # Number of appointments optimized for timing/routing
    processing_time_seconds: float
    visit_schedules: List[Dict[str, Any]] = []  # Optimized visit schedule per provider with start times


class ReplanRequest(BaseModel):
    """Request model for replanning appointments due to changes"""
    appointment_ids: List[str]  # Appointments that need replanning
    reason: str  # Reason for replanning: "patient_request" or "provider_unavailable"
    constraints: Optional[Dict[str, Any]] = None  # Additional constraints for replanning
    preferred_date: Optional[str] = None  # Patient's preferred new date (if applicable)
    unavailable_provider_id: Optional[str] = None  # Provider who became unavailable


class ReplanResponse(BaseModel):
    """Response model for replanning results"""
    success: bool
    message: str
    replanned_count: int
    failed_count: int
    processing_time_seconds: float
    new_assignments: List[Dict[str, Any]] = []  # New appointment assignments
    affected_appointments: List[str] = []  # Other appointments that were affected by replanning


router = APIRouter()


@router.post(
    "/assign",
    response_model=AppointmentAssignmentResponse,
    status_code=status.HTTP_200_OK,
    summary="Appointment Assignment",
    description="Fetch pending appointments batch and assign providers and dates with rolling window scheduling",
)
async def assign_appointments(request: AppointmentAssignmentRequest) -> AppointmentAssignmentResponse:
    """Fetch pending appointments batch and assign providers and dates

    Process:
    1. Fetch existing appointments (pending) as batch
    2. Fetch available providers
    3. Fetch consumers/patients for those appointments
    4. Assign providers and dates with rolling window scheduling
    5. Return assignment results with provider and date for each appointment
    """

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run appointment assignment
    result = await engine.assign_appointments(
        appointment_ids=request.appointment_batch_ids,
        target_date=None,  # Will use rolling window from constraints
        constraints=request.constraints
    )

    return AppointmentAssignmentResponse(
        success=result["success"],
        message=result["message"],
        assigned_count=result["scheduled_count"],
        failed_count=result["failed_count"],
        processing_time_seconds=result["processing_time_seconds"],
        assignments=result.get("assignments", []),
        details=result.get("details", {})
    )


@router.post(
    "/dayplan",
    response_model=DayPlanResponse,
    status_code=status.HTTP_200_OK,
    summary="Run Day Plan",
    description="Optimize daily schedule and routing",
)
async def run_dayplan(request: DayPlanRequest) -> DayPlanResponse:
    """Run day plan optimization"""

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run day plan
    result = await engine.run_day_plan(
        target_date=request.target_date
    )

    return DayPlanResponse(
        success=result["success"],
        message=result["message"],
        optimized_count=result.get("assigned_count", 0),
        processing_time_seconds=result["processing_time_seconds"],
        visit_schedules=result.get("visit_schedules", [])
    )


@router.post(
    "/replan",
    response_model=ReplanResponse,
    status_code=status.HTTP_200_OK,
    summary="Replan Appointments",
    description="Replan appointments due to patient requests or provider unavailability",
)
async def replan_appointments(request: ReplanRequest) -> ReplanResponse:
    """Replan appointments due to changes

    Handles:
    - Patient requests for date changes
    - Provider unavailability on scheduled dates
    - Cascading effects on other appointments
    """

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run replanning
    result = await engine.replan_appointments(
        appointment_ids=request.appointment_ids,
        reason=request.reason,
        constraints=request.constraints,
        preferred_date=request.preferred_date,
        unavailable_provider_id=request.unavailable_provider_id
    )

    return ReplanResponse(
        success=result["success"],
        message=result["message"],
        replanned_count=result.get("replanned_count", 0),
        failed_count=result.get("failed_count", 0),
        processing_time_seconds=result["processing_time_seconds"],
        new_assignments=result.get("new_assignments", []),
        affected_appointments=result.get("affected_appointments", [])
    )


@router.get(
    "/status",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Scheduling Status",
    description="Get current scheduling system status",
)
async def get_scheduling_status() -> Dict[str, Any]:
    """Get scheduling system status"""
    
    return {
        "status": "operational",
        "solver_engine": "timefold",
        "version": settings.VERSION,
        "features": {
            "assignment_optimization": True,
            "day_plan_optimization": True,
            "route_optimization": True,
            "constraint_satisfaction": True
        },
        "performance": {
            "avg_assignment_time_seconds": 30.0,
            "avg_dayplan_time_seconds": 60.0,
            "success_rate_percent": 95.0
        }
    }
