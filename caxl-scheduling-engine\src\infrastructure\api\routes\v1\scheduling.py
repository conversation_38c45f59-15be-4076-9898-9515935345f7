"""CareAxl Scheduling Engine API routes - Core optimization endpoints only"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, status
from pydantic import BaseModel

from src.config.settings import settings
from src.application.services.scheduling_engine import SchedulingEngine


class AssignmentRequest(BaseModel):
    """Request model for appointment assignment optimization

    The engine fetches:
    - Existing appointments as batch (pending assignments)
    - Available providers
    - Consumers/patients for those appointments
    Then runs assignment optimization with rolling window scheduling
    """
    appointment_batch_ids: Optional[List[str]] = None  # Specific appointment IDs to process
    target_date_range: Optional[Dict[str, str]] = None  # Rolling window: {"start": "2025-06-25", "end": "2025-07-01"}
    constraints: Optional[Dict[str, Any]] = None  # Additional optimization constraints


class AssignmentResponse(BaseModel):
    """Response model for assignment optimization results"""
    success: bool
    message: str
    assigned_count: int  # Number of appointments successfully assigned
    failed_count: int    # Number of appointments that couldn't be assigned
    processing_time_seconds: float
    details: Dict[str, Any] = {}  # Algorithm details, constraint violations, etc.


class DayPlanRequest(BaseModel):
    """Request model for day plan optimization

    The engine fetches:
    - Scheduled appointments for the target date
    - Assigned providers for those appointments
    - Patient locations and requirements
    Then runs day plan optimization for timing and routing
    """
    target_date: str  # Date to optimize (YYYY-MM-DD format)
    provider_ids: Optional[List[str]] = None  # Specific providers to optimize (optional)


class DayPlanResponse(BaseModel):
    """Response model for day plan optimization results"""
    success: bool
    message: str
    optimized_count: int  # Number of appointments optimized for timing/routing
    processing_time_seconds: float
    visit_order: List[Dict[str, Any]] = []  # Optimized visit schedule per provider


router = APIRouter()


@router.post(
    "/assign",
    response_model=AssignmentResponse,
    status_code=status.HTTP_200_OK,
    summary="Assignment Optimization",
    description="Fetch pending appointments batch and run provider assignment optimization with rolling window scheduling",
)
async def assign_appointments(request: AssignmentRequest) -> AssignmentResponse:
    """Fetch pending appointments batch and run assignment optimization

    Process:
    1. Fetch existing appointments (pending) as batch
    2. Fetch available providers
    3. Fetch consumers/patients for those appointments
    4. Run assignment optimization with rolling window scheduling
    5. Return assignment results
    """

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run assignment optimization
    result = await engine.assign_appointments(
        appointment_ids=request.appointment_batch_ids,
        target_date=None,  # Will use rolling window from constraints
        constraints=request.constraints
    )

    return AssignmentResponse(
        success=result["success"],
        message=result["message"],
        assigned_count=result["scheduled_count"],
        failed_count=result["failed_count"],
        processing_time_seconds=result["processing_time_seconds"],
        details=result.get("details", {})
    )


@router.post(
    "/dayplan",
    response_model=DayPlanResponse,
    status_code=status.HTTP_200_OK,
    summary="Run Day Plan",
    description="Optimize daily schedule and routing",
)
async def run_dayplan(request: DayPlanRequest) -> DayPlanResponse:
    """Run day plan optimization"""

    # Initialize scheduling engine
    engine = SchedulingEngine()

    # Run day plan
    result = await engine.run_day_plan(
        target_date=request.target_date
    )

    return DayPlanResponse(
        success=result["success"],
        message=result["message"],
        optimized_count=result.get("assigned_count", 0),
        processing_time_seconds=result["processing_time_seconds"],
        visit_order=result["visit_order"]
    )


@router.get(
    "/status",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Scheduling Status",
    description="Get current scheduling system status",
)
async def get_scheduling_status() -> Dict[str, Any]:
    """Get scheduling system status"""
    
    return {
        "status": "operational",
        "solver_engine": "timefold",
        "version": settings.VERSION,
        "features": {
            "assignment_optimization": True,
            "day_plan_optimization": True,
            "route_optimization": True,
            "constraint_satisfaction": True
        },
        "performance": {
            "avg_assignment_time_seconds": 30.0,
            "avg_dayplan_time_seconds": 60.0,
            "success_rate_percent": 95.0
        }
    }
