# Makefile for Appointment Scheduler Service

.PHONY: help setup-dev install test test-coverage lint type-check security-check run-dev run-prod build clean docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  setup-dev      - Setup development environment"
	@echo "  install        - Install dependencies"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  lint           - Run linting"
	@echo "  type-check     - Run type checking"
	@echo "  security-check - Run security checks"
	@echo "  run-dev        - Run development server"
	@echo "  run-prod       - Run production server"
	@echo "  build          - Build the application"
	@echo "  clean          - Clean build artifacts"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"

# Development setup
setup-dev:
	pip install -e ".[dev]"
	pre-commit install

# Install dependencies
install:
	pip install -e .

# Testing
test:
	pytest

test-coverage:
	pytest --cov=src --cov-report=html --cov-report=term-missing

test-unit:
	pytest tests/unit/

test-integration:
	pytest tests/integration/

test-api:
	pytest tests/test_api.py

# Code quality
lint:
	black --check src tests
	ruff check src tests

lint-fix:
	black src tests
	ruff check --fix src tests

type-check:
	mypy src

security-check:
	bandit -r src

# Development server
run-dev:
	uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload

# Production server
run-prod:
	uvicorn src.main:app --host 0.0.0.0 --port 8080

# Build
build:
	python -m build

# Clean
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

# Docker
docker-build:
	docker build -t appointment-scheduler:latest .

docker-build-dev:
	docker build -f Dockerfile.dev -t appointment-scheduler:dev .

docker-run:
	docker run -p 8080:8080 appointment-scheduler:latest

docker-run-dev:
	docker run -p 8080:8080 -v $(PWD):/app appointment-scheduler:dev

# Docker Compose
compose-up:
	docker-compose up --build

compose-up-prod:
	docker-compose -f docker-compose.prod.yml up --build

compose-down:
	docker-compose down

# Database
db-init:
	docker-compose exec postgres psql -U admin -d scheduler_dev -f /docker-entrypoint-initdb.d/init.sql

# Logs
logs:
	docker-compose logs -f

logs-app:
	docker-compose logs -f appointment_scheduler_service
