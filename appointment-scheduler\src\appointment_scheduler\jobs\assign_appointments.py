#!/usr/bin/env python3
"""
AssignAppointment Job - Stage 1 of healthcare scheduling optimization.

This job assigns providers and dates to appointments using the Timefold solver.
It is the first stage of the 2-stage optimization process:
1. Assignment Solver: Assigns providers and dates to appointments (this job)
2. Day Plan Solver: Optimizes timing and routing within a day (DayPlan job)
"""

import logging
import os
import sys
import time
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from uuid import uuid4

from timefold.solver import SolverFactory, SolutionManager
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver.domain import (
    PlanningId, PlanningVariable, PlanningEntityCollectionProperty, 
    ProblemFactCollectionProperty, ValueRangeProvider, PlanningScore
)
from timefold.solver.score import HardSoftScore

from ..config_manager import ConfigManager
from ..constraints import define_constraints
from ..data_loader import create_demo_data
from ..domain import (
    Provider, Consumer, AppointmentData,
    ServiceConfig, SchedulerConfig, ScheduledAppointment,
    AppointmentAssignment as DomainAppointmentAssignment,
    AppointmentSchedule as DomainAppointmentSchedule
)
from ..planning_models import (
    AppointmentAssignment, AppointmentSchedule,
    create_planning_schedule, create_domain_schedule
)

# Ensure Provider class is available for solver
_ = Provider  # Force import and make class available

from loguru import logger

logger = logging.getLogger(__name__)


class AssignAppointmentJob:
    """Job for assigning providers and dates to appointments."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, daemon_mode: bool = False):
        """Initialize the assignment job."""
        self.config_manager = config_manager or ConfigManager()
        self.scheduler_config = self.config_manager.get_scheduler_config()
        self.service_configs = self.config_manager.get_all_service_configs()
        self.daemon_mode = daemon_mode
        
        # Initialize solver with proper configuration for large datasets
        solver_config = SolverConfig(
            solution_class=AppointmentSchedule,
            entity_class_list=[AppointmentAssignment],  # Provider is not a planning entity
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=120),  # 2 minutes for large dataset
                unimproved_spent_limit=Duration(seconds=30)  # Stop if no improvement for 30 seconds
            )
        )
        
        self.solver_factory = SolverFactory.create(solver_config)
        self.solution_manager = SolutionManager.create(self.solver_factory)
        
        logger.info("AssignAppointment job initialized with solver config")
    
    def run(self, target_date: Optional[date] = None, service_type: Optional[str] = None) -> Dict[str, Any]:
        """Run the appointment assignment job."""
        start_time = time.time()
        
        logger.info("🚀 === ASSIGNMENT JOB STARTED ===")
        logger.info(f"📅 Target Date: {target_date or 'Today'}")
        logger.info(f"🏥 Service Type: {service_type or 'All Services'}")
        
        try:
            # STAGE 1: Load demo data
            logger.info("=== STAGE 1: Loading Data ===")
            demo_data = create_demo_data()
            providers = demo_data["providers"]
            consumers = demo_data["consumers"]
            appointments = demo_data["appointments"]
            
            logger.info(f"📊 Initial data loaded: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")
            
            # STAGE 2: Filter appointments by service type
            if service_type:
                logger.info(f"=== STAGE 2A: Filtering by Service Type ({service_type}) ===")
                appointments = self._filter_appointments_by_service(appointments, service_type)
                logger.info(f"✅ Filtered to {len(appointments)} appointments for service type: {service_type}")
            else:
                logger.info("=== STAGE 2A: No service type filter applied ===")
                logger.info(f"✅ Using all {len(appointments)} appointments")
            
            # STAGE 3: Filter appointments by date range
            if target_date:
                logger.info(f"=== STAGE 2B: Filtering by Date Range ===")
                logger.info(f"📅 Date range: {target_date} to {target_date + timedelta(days=self.scheduler_config.rolling_window_days - 1)}")
                appointments = self._filter_appointments_by_date_range(appointments, target_date)
                logger.info(f"✅ Filtered to {len(appointments)} appointments for date range")
            else:
                logger.info("=== STAGE 2B: No date filter applied ===")
                logger.info(f"✅ Using all {len(appointments)} appointments")
            
            if not appointments:
                logger.warning("❌ No appointments to assign after filtering")
                result = {
                    "success": True,
                    "message": "No appointments to assign",
                    "assignments": [],
                    "processing_time": time.time() - start_time
                }
                self._handle_completion(result)
                return result
            
            # STAGE 4: Create planning entities
            logger.info("=== STAGE 3: Creating Planning Entities ===")
            domain_assignments = self._create_domain_assignments(appointments)
            planning_assignments = self._create_planning_assignments(appointments)
            logger.info(f"✅ Created {len(planning_assignments)} planning entities")
            
            # STAGE 5: Create available dates
            logger.info("=== STAGE 4: Creating Available Dates ===")
            available_dates = self._create_available_dates(target_date)
            logger.info(f"✅ Created {len(available_dates)} available dates: {[d.strftime('%Y-%m-%d') for d in available_dates]}")
            
            # STAGE 6: Create solution
            logger.info("=== STAGE 5: Creating Optimization Solution ===")
            # Create domain solution first
            domain_solution = DomainAppointmentSchedule(
                id=str(uuid4()),
                providers=providers,
                available_dates=available_dates,
                appointment_assignments=domain_assignments
            )
            # Convert to planning solution for solver
            solution = create_planning_schedule(domain_solution)
            logger.info(f"✅ Solution created with {len(planning_assignments)} assignments, {len(available_dates)} available dates")
            
            # STAGE 7: Solve the problem
            logger.info("=== STAGE 6: Starting Optimization Solver ===")
            solved_solution = self._solve_assignment_problem(solution, service_type)
            logger.info("✅ Optimization solver completed")
            
            # STAGE 8: Process results
            logger.info("=== STAGE 7: Processing Results ===")
            results = self._process_assignment_results(solved_solution, consumers, start_time)
            
            logger.info("🎉 === ASSIGNMENT JOB COMPLETED ===")
            logger.info(f"⏱️  Total processing time: {results['processing_time']:.2f} seconds")
            
            self._handle_completion(results)
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in AssignAppointment job: {e}", exc_info=True)
            result = {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
            self._handle_completion(result)
            return result
    
    def _filter_appointments_by_service(self, appointments: List[AppointmentData], service_type: str) -> List[AppointmentData]:
        """Filter appointments by service type based on required skills."""
        service_config = self.service_configs.get(service_type)
        if not service_config:
            logger.warning(f"No service config found for {service_type}, using all appointments")
            return appointments
        
        filtered_appointments = []
        for appointment in appointments:
            # Check if appointment requires any of the service skills
            if any(skill in appointment.required_skills for skill in service_config.required_skills):
                filtered_appointments.append(appointment)
        
        return filtered_appointments
    
    def _filter_appointments_by_date_range(self, appointments: List[AppointmentData], target_date: date) -> List[AppointmentData]:
        """Filter appointments by date range starting from target date."""
        start_date = target_date
        end_date = target_date + timedelta(days=self.scheduler_config.rolling_window_days - 1)
        
        filtered_appointments = []
        for appointment in appointments:
            if start_date <= appointment.appointment_date <= end_date:
                filtered_appointments.append(appointment)
        
        return filtered_appointments
    
    def _create_domain_assignments(self, appointments: List[AppointmentData]) -> List[DomainAppointmentAssignment]:
        """Create domain assignments for the domain solution."""
        assignments = []
        for appointment in appointments:
            assignment = DomainAppointmentAssignment(
                id=str(appointment.id),
                appointment_data=appointment,
                provider=None,  # Will be assigned by solver
                assigned_date=None  # Will be assigned by solver
            )
            assignments.append(assignment)
        
        return assignments
    
    def _create_planning_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create planning assignments for the solver."""
        assignments = []
        for appointment in appointments:
            assignment = AppointmentAssignment(
                id=str(appointment.id),
                appointment_data=appointment,
                provider=None,  # Will be assigned by solver
                assigned_date=None  # Will be assigned by solver
            )
            assignments.append(assignment)
        
        return assignments
    
    def _create_available_dates(self, target_date: Optional[date]) -> List[date]:
        """Create list of available dates for assignment."""
        if target_date is None:
            target_date = date.today()
        
        available_dates = []
        for i in range(self.scheduler_config.rolling_window_days):
            available_date = target_date + timedelta(days=i)
            # Skip weekends for now (can be made configurable)
            if available_date.weekday() < 5:  # Monday to Friday
                available_dates.append(available_date)
        
        return available_dates
    
    def _solve_assignment_problem(self, solution: AppointmentSchedule, service_type: Optional[str]) -> AppointmentSchedule:
        """Solve the appointment assignment problem using Timefold."""
        logger.info(f"🔧 Starting solver with {len(solution.appointment_assignments)} assignments")
        
        # Get service config for constraints
        service_config = None
        if service_type:
            service_config = self.service_configs.get(service_type)
            if service_config:
                logger.info(f"🏥 Using service config: {service_type}")
                logger.info(f"   - Required skills: {service_config.required_skills}")
                logger.info(f"   - Geographic radius: {service_config.geographic_radius_miles} miles")
                logger.info(f"   - Max daily appointments: {service_config.max_daily_appointments_per_provider}")
            else:
                logger.info(f"🏥 Service config not found for: {service_type}")
        else:
            logger.info("🏥 No specific service type - using default constraints")
        
        if not service_config:
            # Use first available service config or create default
            if self.service_configs:
                service_config = list(self.service_configs.values())[0]
                logger.info(f"🏥 Using default service config: {service_config.service_type}")
            else:
                service_config = ServiceConfig(
                    service_type="default",
                    required_skills=["basic_care"],
                    geographic_radius_miles=25.0,
                    max_daily_appointments_per_provider=8,
                    max_weekly_hours_per_provider=40,
                    continuity_weight=0.7,
                    workload_balance_weight=0.6,
                    geographic_clustering_weight=0.4,
                    patient_preference_weight=0.7,
                    capacity_threshold_percentage=0.9
                )
                logger.info("🏥 Created default service config")
        
        # Create solver
        logger.info("🔧 Creating solver instance...")
        solver = self.solver_factory.build_solver()
        
        # Set solving time limit
        solving_time_seconds = self.scheduler_config.max_solving_time_seconds
        logger.info(f"⏱️  Solver time limit: {solving_time_seconds} seconds")
        
        logger.info("🚀 Starting optimization process...")
        logger.info("   - Applying constraint rules...")
        logger.info("   - Balancing workload across providers...")
        logger.info("   - Optimizing geographic distribution...")
        logger.info("   - Considering patient preferences...")
        
        # Solve
        solved_solution = solver.solve(solution)
        
        logger.info(f"✅ Solver completed successfully!")
        logger.info(f"📊 Final score: {solved_solution.score}")
        
        return solved_solution
    
    def _process_assignment_results(self, solution: AppointmentSchedule, consumers: List[Consumer], start_time: float) -> Dict[str, Any]:
        """Process and format the assignment results."""
        logger.info("📊 Processing assignment results...")
        processing_time = time.time() - start_time
        
        # Create consumer lookup
        consumer_lookup = {consumer.id: consumer for consumer in consumers}
        
        # Process assignments
        assignments = []
        assigned_count = 0
        unassigned_count = 0
        constraint_violations = []
        constraint_satisfactions = []
        
        logger.info("🔍 Analyzing assignment results...")
        for assignment in solution.appointment_assignments:
            consumer = consumer_lookup.get(assignment.appointment_data.consumer_id)
            consumer_name = consumer.name if consumer else "Unknown"
            
            if assignment.provider is not None and assignment.assigned_date is not None:
                assigned_count += 1
                
                # Check constraint satisfaction
                satisfaction_status = self._check_constraint_satisfaction(assignment, solution)
                constraint_satisfactions.extend(satisfaction_status["satisfied"])
                constraint_violations.extend(satisfaction_status["violated"])
                
                assignment_result = {
                    "appointment_id": str(assignment.appointment_data.id),
                    "patient_name": consumer_name,
                    "service_type": self._determine_service_type(assignment.appointment_data),
                    "provider_name": assignment.provider.name,
                    "assigned_date": assignment.assigned_date.isoformat(),
                    "visit_type": "Timed" if assignment.appointment_data.timing.is_timed_visit else "Flexible",
                    "duration_minutes": assignment.appointment_data.duration_min,
                    "urgent": assignment.appointment_data.urgent,
                    "constraints_satisfied": satisfaction_status["satisfied"],
                    "constraints_violated": satisfaction_status["violated"]
                }
                
                assignments.append(assignment_result)
                
                logger.info(f"✅ ASSIGNED: {consumer_name} -> {assignment.provider.name} on {assignment.assigned_date}")
                
            else:
                unassigned_count += 1
                logger.warning(f"❌ UNASSIGNED: {consumer_name} - No provider or date assigned")
        
        # Calculate summary statistics
        total_appointments = len(solution.appointment_assignments)
        assignment_rate = (assigned_count / total_appointments * 100) if total_appointments > 0 else 0
        
        logger.info("📈 Calculating assignment statistics...")
        
        # Group by service type
        service_type_stats = {}
        for assignment in assignments:
            service_type = assignment["service_type"]
            if service_type not in service_type_stats:
                service_type_stats[service_type] = {"assigned": 0, "total": 0}
            service_type_stats[service_type]["assigned"] += 1
        
        # Count total appointments by service type
        for assignment in solution.appointment_assignments:
            service_type = self._determine_service_type(assignment.appointment_data)
            if service_type not in service_type_stats:
                service_type_stats[service_type] = {"assigned": 0, "total": 0}
            service_type_stats[service_type]["total"] += 1
        
        # Calculate success rates
        for service_type in service_type_stats:
            stats = service_type_stats[service_type]
            stats["success_rate"] = (stats["assigned"] / stats["total"] * 100) if stats["total"] > 0 else 0
        
        results = {
            "success": True,
            "processing_time": processing_time,
            "summary": {
                "total_appointments": total_appointments,
                "assigned_appointments": assigned_count,
                "unassigned_appointments": unassigned_count,
                "assignment_rate_percent": assignment_rate,
                "final_score": str(solution.score) if solution.score else "N/A"
            },
            "service_type_statistics": service_type_stats,
            "constraint_summary": {
                "satisfied_constraints": len(constraint_satisfactions),
                "violated_constraints": len(constraint_violations),
                "constraint_violation_types": self._count_constraint_violations(constraint_violations)
            },
            "assignments": assignments
        }
        
        # Log detailed results
        logger.info("=== ASSIGNMENT RESULTS SUMMARY ===")
        logger.info(f"📊 Total appointments: {total_appointments}")
        logger.info(f"✅ Assigned: {assigned_count} ({assignment_rate:.1f}%)")
        logger.info(f"❌ Unassigned: {unassigned_count}")
        logger.info(f"⏱️  Processing time: {processing_time:.2f} seconds")
        logger.info(f"📈 Final score: {solution.score}")
        
        logger.info("=== SERVICE TYPE STATISTICS ===")
        for service_type, stats in service_type_stats.items():
            logger.info(f"🏥 {service_type}: {stats['assigned']}/{stats['total']} ({stats['success_rate']:.1f}%)")
        
        logger.info("=== CONSTRAINT SUMMARY ===")
        logger.info(f"✅ Satisfied constraints: {len(constraint_satisfactions)}")
        logger.info(f"❌ Violated constraints: {len(constraint_violations)}")
        
        violation_counts = self._count_constraint_violations(constraint_violations)
        for violation_type, count in violation_counts.items():
            logger.info(f"   ❌ {violation_type}: {count}")
        
        return results
    
    def _determine_service_type(self, appointment: AppointmentData) -> str:
        """Determine service type based on required skills."""
        for service_type, config in self.service_configs.items():
            if any(skill in appointment.required_skills for skill in config.required_skills):
                return service_type
        
        # Default based on role
        if appointment.required_role == "RN":
            return "skilled_nursing"
        elif appointment.required_role == "PT":
            return "physical_therapy"
        elif appointment.required_role == "CNA":
            return "personal_care"
        else:
            return "general"
    
    def _check_constraint_satisfaction(self, assignment: AppointmentAssignment, solution: AppointmentSchedule) -> Dict[str, List[str]]:
        """Check constraint satisfaction for an assignment."""
        satisfied = []
        violated = []
        
        # Check provider skills
        if assignment.provider and assignment.appointment_data.required_skills:
            if all(skill in assignment.provider.skills for skill in assignment.appointment_data.required_skills):
                satisfied.append("required_provider_skills")
            else:
                violated.append("required_provider_skills")
        
        # Check provider availability
        if assignment.provider and assignment.assigned_date:
            # Use the new availability structure
            if assignment.provider.availability is None:
                # If no availability is configured, assume available on weekdays
                weekday = assignment.assigned_date.weekday()
                if weekday < 5:  # Monday to Friday
                    satisfied.append("provider_availability")
                else:
                    violated.append("provider_availability")
            else:
                # Check using the new ProviderAvailability model
                shift_hours = assignment.provider.availability.get_shift_hours(assignment.assigned_date)
                if shift_hours is not None:
                    satisfied.append("provider_availability")
                else:
                    violated.append("provider_availability")
        
        # Check provider role
        if assignment.provider and assignment.appointment_data.required_role:
            if assignment.provider.role == assignment.appointment_data.required_role:
                satisfied.append("provider_role_match")
            else:
                violated.append("provider_role_match")
        
        # Check geographic service area
        if (assignment.provider and assignment.provider.home_location and 
            assignment.appointment_data.location):
            # This would need actual distance calculation
            satisfied.append("geographic_service_area")
        
        return {
            "satisfied": satisfied,
            "violated": violated
        }
    
    def _count_constraint_violations(self, violations: List[str]) -> Dict[str, int]:
        """Count constraint violations by type."""
        violation_counts = {}
        for violation in violations:
            violation_counts[violation] = violation_counts.get(violation, 0) + 1
        return violation_counts
    
    def _handle_completion(self, results: Dict[str, Any]):
        """Handle job completion based on daemon mode."""
        if not self.daemon_mode:
            logger.info("Job completed.")
            # Don't exit when called programmatically (e.g., from API)
            # Only exit when run as main script
        else:
            logger.info("Job completed. Continuing in daemon mode...")


def setup_logging():
    # Absolute path to log directory and file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_dir = os.path.abspath(os.path.join(script_dir, '..', '..', '..', 'logs'))
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"assign_appointments_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log")

    # Remove all handlers associated with the root logger
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    logging.info("Logger initialized. Log file: %s", log_file)
    return log_file

def main(daemon_mode: bool = False):
    """
    Main function to run the assignment job.
    
    Args:
        daemon_mode: If True, the job will not exit after completion (for continuous operation)
    """
    log_file = setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Initializing AssignAppointmentJob...")
    job = AssignAppointmentJob(daemon_mode=daemon_mode)

    # Run for today's date
    target_date = date.today()
    logger.info("Running job for target date: %s", target_date)
    results = job.run(target_date=target_date)

    if results["success"]:
        print("AssignAppointment job completed successfully!")
        if "summary" in results:
            print(f"Assigned {results['summary']['assigned_appointments']} out of {results['summary']['total_appointments']} appointments")
        else:
            print(f"Message: {results.get('message', 'No details available')}")
    else:
        print(f"AssignAppointment job failed: {results['error']}")
    
    print(f"Log file: {log_file}")
    logging.shutdown()
    
    # Exit if not in daemon mode and running as main script
    if not daemon_mode and __name__ == "__main__":
        logger.info("Job completed. Exiting...")
        sys.exit(0 if results["success"] else 1)
    else:
        logger.info("Job completed. Continuing in daemon mode...")

if __name__ == "__main__":
    # Check for daemon mode argument
    import sys
    daemon_mode = "--daemon" in sys.argv or "-d" in sys.argv
    main(daemon_mode=daemon_mode) 