# API Documentation

Complete documentation for the Appointment Scheduler REST API.

## Overview

The Appointment Scheduler API provides RESTful endpoints for managing healthcare appointment scheduling using Timefold optimization. The API supports loading patient and provider data, triggering optimization jobs, and handling dynamic rescheduling scenarios.

**Base URL**: `http://localhost:8000`  
**API Version**: v1  
**Content-Type**: `application/json`

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    Client[Client Application] --> API[FastAPI Server]
    API --> DataLoader[Data Loader]
    API --> Scheduler[Appointment Scheduler]
    
    DataLoader --> DataFiles[(Data Files<br/>YAML)]
    Scheduler --> AssignJob[Assignment Job]
    Scheduler --> Day<PERSON>lanJob[Day Plan Job]
    
    AssignJob --> Timefold[Timefold Solver]
    DayPlanJob --> Timefold
    
    Timefold --> Constraints[Constraint Engine]
    Constraints --> Solution[Optimized Solution]
    
    subgraph "Data Sources"
        DataFiles
        Scenarios[(Scenario Data)]
        Config[(Configuration)]
    end
    
    subgraph "Optimization Engine"
        Timefold
        Constraints
        Solution
    end
```

### API Flow Diagram

```mermaid
sequenceDiagram
    participant C as Client
    participant API as FastAPI
    participant DL as DataLoader
    participant S as Scheduler
    participant T as Timefold
    participant DB as Data Store

    Note over C,DB: Data Loading Flow
    C->>API: GET /api/v1/patients
    API->>DL: load_consumers()
    DL->>DB: Read consumers.yml
    DB-->>DL: Consumer data
    DL-->>API: Parsed consumers
    API-->>C: Patient list (JSON)

    Note over C,DB: Assignment Flow
    C->>API: POST /api/v1/assign-appointments
    API->>S: run_assign_appointments()
    S->>DL: load_all_data()
    DL->>DB: Read all YAML files
    DB-->>DL: Complete dataset
    DL-->>S: Providers, Consumers, Appointments
    S->>T: solve(planning_problem)
    T-->>S: Optimized assignments
    S-->>API: Assignment results
    API-->>C: Assignment response (JSON)

    Note over C,DB: Day Planning Flow
    C->>API: POST /api/v1/run-dayplan
    API->>S: run_day_plan()
    S->>T: optimize_time_slots()
    T-->>S: Time-optimized schedule
    S-->>API: Day plan results
    API-->>C: Day plan response (JSON)
```

## Authentication

Currently, the API does not require authentication. In production environments, implement appropriate authentication mechanisms such as:
- API Keys
- OAuth 2.0
- JWT tokens

## Endpoints

### Health Check

#### GET /health
Check API server health status.

**Response**:
```json
{
  "status": "healthy"
}
```

**Status Codes**:
- `200`: Service is healthy
- `503`: Service unavailable

---

### Root Endpoint

#### GET /
Get API information and documentation links.

**Response**:
```json
{
  "message": "Appointment Scheduler API",
  "version": "1.0.0",
  "docs": "/docs"
}
```

---

### Patient Management

#### GET /api/v1/patients
Load patients from data/consumers.yaml using data_loader.py.

**Response**:
```json
[
  {
    "id": "patient-001",
    "name": "John Doe",
    "location": {
      "latitude": 40.7589,
      "longitude": -73.9851,
      "city": "New York",
      "state": "NY",
      "address": "123 Main St, New York, NY 10001"
    },
    "care_episode_id": "episode-001",
    "consumer_preferences": {
      "preferred_days": ["monday", "wednesday", "friday"],
      "preferred_hours": ["09:00", "17:00"],
      "language": "English"
    }
  }
]
```

**Status Codes**:
- `200`: Success
- `500`: Data loading error

---

### Care Staff Management

#### GET /api/v1/carestaff
Load care staff from data/providers.yaml using data_loader.py.

**Response**:
```json
[
  {
    "id": "provider-001",
    "name": "Dr. Sarah Johnson, RN",
    "role": "RN",
    "skills": ["medication_management", "wound_care", "assessment"],
    "home_location": {
      "latitude": 40.7589,
      "longitude": -73.9851,
      "city": "New York",
      "state": "NY",
      "address": "456 Provider Ave, New York, NY 10002"
    },
    "availability": {
      "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
      "working_hours": ["08:00", "17:00"]
    },
    "capacity": {
      "max_hours_per_day": 8,
      "max_tasks_count_in_day": 6
    }
  }
]
```

**Status Codes**:
- `200`: Success
- `500`: Data loading error

---

### Appointment Assignment

#### POST /api/v1/assign-appointments
Accepts batch of appointments and triggers assignment solver. Ensures scheduler.py runs in daemon mode.

**Request Body**:
```json
{
  "appointments": [
    {
      "consumer_id": "patient-001",
      "required_skills": ["basic_care"],
      "duration_min": 30,
      "priority": "normal",
      "urgent": false,
      "location": {
        "latitude": 40.7580,
        "longitude": -73.9855,
        "city": "New York",
        "state": "NY"
      }
    }
  ],
  "target_date": "2024-01-15",
  "service_type": "skilled_nursing"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Assignment completed successfully",
  "total_appointments": 10,
  "assigned_appointments": 8,
  "unassigned_appointments": 2,
  "processing_time_seconds": 45.2,
  "assignments": [
    {
      "appointment_id": "apt-001",
      "consumer_id": "patient-001",
      "provider_id": "provider-001",
      "scheduled_time": "2024-01-15T10:00:00",
      "estimated_duration": 30,
      "travel_time_minutes": 15
    }
  ]
}
```

**Status Codes**:
- `200`: Assignment completed
- `400`: Invalid request data
- `500`: Assignment failed

---

### Appointment Reassignment

#### POST /api/v1/reassign
Handles changes (e.g., provider unavailable, patient request) → unpin and reassign.

**Request Body**:
```json
{
  "appointment_ids": ["apt-001", "apt-002"],
  "reason": "provider_unavailable",
  "force_unpin": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Reassignment completed: provider_unavailable",
  "total_appointments": 5,
  "assigned_appointments": 4,
  "unassigned_appointments": 1,
  "processing_time_seconds": 23.1,
  "reassigned_appointments": [
    {
      "appointment_id": "apt-001",
      "old_provider_id": "provider-001",
      "new_provider_id": "provider-002",
      "new_scheduled_time": "2024-01-15T11:00:00"
    }
  ]
}
```

**Status Codes**:
- `200`: Reassignment completed
- `400`: Invalid appointment IDs
- `500`: Reassignment failed

---

### Day Plan Generation

#### POST /api/v1/run-dayplan
Runs dayplan solver for providers to optimize daily schedules.

**Request Body**:
```json
{
  "target_date": "2024-01-15",
  "provider_ids": ["provider-001", "provider-002"]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Day plan completed successfully",
  "total_appointments": 12,
  "assigned_appointments": 12,
  "unassigned_appointments": 0,
  "processing_time_seconds": 18.7,
  "day_plans": [
    {
      "provider_id": "provider-001",
      "date": "2024-01-15",
      "schedule": [
        {
          "time_slot": "09:00-09:30",
          "appointment_id": "apt-001",
          "patient_name": "John Doe",
          "location": "123 Main St",
          "travel_time_from_previous": 0
        },
        {
          "time_slot": "10:00-10:30",
          "appointment_id": "apt-002",
          "patient_name": "Jane Smith",
          "location": "456 Oak Ave",
          "travel_time_from_previous": 15
        }
      ],
      "total_travel_time": 45,
      "total_working_time": 480
    }
  ]
}
```

**Status Codes**:
- `200`: Day plan completed
- `400`: Invalid date or provider IDs
- `500`: Day plan generation failed

## Data Models

### LocationModel
```json
{
  "latitude": 40.7589,
  "longitude": -73.9851,
  "city": "New York",
  "state": "NY",
  "address": "123 Main Street, New York, NY 10001"
}
```

### AppointmentModel
```json
{
  "id": "apt-001",
  "consumer_id": "patient-001",
  "appointment_date": "2024-01-15",
  "required_skills": ["basic_care", "medication_management"],
  "duration_min": 30,
  "urgent": false,
  "location": { /* LocationModel */ },
  "priority": "normal"
}
```

### AssignmentResult
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "total_appointments": 10,
  "assigned_appointments": 8,
  "unassigned_appointments": 2,
  "processing_time_seconds": 45.2,
  "assignments": [ /* Array of assignment objects */ ]
}
```

## Error Handling

### Error Response Format
```json
{
  "error": "ValidationError",
  "message": "Invalid appointment data provided",
  "details": {
    "field": "consumer_id",
    "issue": "Consumer ID not found"
  }
}
```

### Common Error Codes

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 400 | Bad Request | Invalid request data or parameters |
| 404 | Not Found | Endpoint or resource not found |
| 405 | Method Not Allowed | HTTP method not supported |
| 422 | Unprocessable Entity | Valid JSON but invalid data structure |
| 500 | Internal Server Error | Server-side processing error |
| 503 | Service Unavailable | Service temporarily unavailable |

## Rate Limiting

Currently, no rate limiting is implemented. For production use, consider implementing:
- Request rate limits per IP
- Concurrent request limits
- Resource-based throttling

## Testing the API

### Using curl

**Health Check**:
```bash
curl http://localhost:8000/health
```

**Load Patients**:
```bash
curl http://localhost:8000/api/v1/patients
```

**Trigger Assignment**:
```bash
curl -X POST http://localhost:8000/api/v1/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{
    "appointments": [
      {
        "consumer_id": "patient-001",
        "required_skills": ["basic_care"],
        "duration_min": 30,
        "priority": "normal"
      }
    ]
  }'
```

### Using Python requests

```python
import requests
import json

base_url = "http://localhost:8000"

# Health check
response = requests.get(f"{base_url}/health")
print(f"Health: {response.json()}")

# Load patients
response = requests.get(f"{base_url}/api/v1/patients")
patients = response.json()
print(f"Loaded {len(patients)} patients")

# Trigger assignment
assignment_data = {
    "appointments": [
        {
            "consumer_id": "patient-001",
            "required_skills": ["basic_care"],
            "duration_min": 30,
            "priority": "normal"
        }
    ]
}

response = requests.post(
    f"{base_url}/api/v1/assign-appointments",
    headers={"Content-Type": "application/json"},
    data=json.dumps(assignment_data)
)

result = response.json()
print(f"Assignment result: {result['message']}")
```

## Interactive Documentation

When the API server is running, interactive documentation is available at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

These interfaces provide:
- Interactive endpoint testing
- Request/response examples
- Schema documentation
- Authentication testing (when implemented)

## Deployment Considerations

### Production Configuration
- Enable HTTPS/TLS
- Configure CORS appropriately
- Implement authentication
- Add request logging
- Set up monitoring and alerting
- Configure rate limiting
- Use environment variables for configuration

### Scaling
- Use load balancers for multiple API instances
- Implement caching for frequently accessed data
- Consider async processing for long-running optimization jobs
- Monitor resource usage and optimize accordingly
