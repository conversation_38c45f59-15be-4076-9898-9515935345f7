"""Emergency Contact Entity"""

from uuid import UUID

from src.domain.enums import EmergencyContactRelationship
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase


class EmergencyContact(EntityBase):
    """Emergency contact value object"""

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        first_name: str,
        last_name: str,
        relationship: EmergencyContactRelationship,
        phone_number: str,
        phone_country_code: str,
        email: str,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        self._first_name = first_name.strip()
        self._last_name = last_name.strip()
        self._relationship = relationship
        self._phone_number = phone_number.strip()
        self._phone_country_code = phone_country_code.strip()
        self._email = email.strip()

        if not self._phone_number:
            raise ValueError("Phone number is required for emergency contact")

    @property
    def first_name(self) -> str:
        return self._first_name

    @first_name.setter
    def first_name(self, value: str) -> None:
        if not value or not value.strip():
            raise ValueError("First name is required")
        self._first_name = value.strip()

    @property
    def last_name(self) -> str:
        return self._last_name

    @last_name.setter
    def last_name(self, value: str) -> None:
        if not value or not value.strip():
            raise ValueError("Last name is required")
        self._last_name = value.strip()

    @property
    def relationship(self) -> EmergencyContactRelationship:
        return self._relationship

    @relationship.setter
    def relationship(self, value: EmergencyContactRelationship) -> None:
        if not value:
            raise ValueError("Relationship is required")
        self._relationship = value

    @property
    def phone_number(self) -> str:
        return self._phone_number

    @phone_number.setter
    def phone_number(self, value: str) -> None:
        if not value or not value.strip():
            raise ValueError("Phone number is required for emergency contact")
        self._phone_number = value.strip()

    @property
    def phone_country_code(self) -> str:
        return self._phone_country_code

    @phone_country_code.setter
    def phone_country_code(self, value: str) -> None:
        if not value or not value.strip():
            raise ValueError("Phone country code is required")
        self._phone_country_code = value.strip()

    @property
    def email(self) -> str:
        return self._email

    @email.setter
    def email(self, value: str) -> None:
        self._email = value.strip() if value else ""

    @property
    def full_name(self) -> str:
        return f"{self._first_name} {self._last_name}"

    def is_contactable(self) -> bool:
        """Returns True if the emergency contact can be reached through at least one method"""
        return bool(self._phone_number or self._email)
