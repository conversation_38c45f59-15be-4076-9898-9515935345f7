"""Care Giver Entity"""

from uuid import UUID

from src.domain.entities.email import EmailInfo
from src.domain.enums import CaregiverRelationship
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase
from src.domain.value_objects.personal_info import BasicPersonalInfo


class Caregiver(EntityBase):
    """Care Giver entity"""

    def __init__(
        self,
        id: UUID,
        audit_stamp: AuditStamp,
        patient_id: UUID,
        relationship: CaregiverRelationship,
        personal_info: BasicPersonalInfo,
        email: str,
        email_verified: bool,
        phone_number: str,
        phone_country_code: str,
        is_phone_verified: bool,
        location_id: UUID | None = None,
    ):
        super().__init__(id=id, audit_stamp=audit_stamp)
        if not relationship:
            raise ValueError("Relationship is required")

        self._patient_id = patient_id
        self._relationship = relationship.value
        self._personal_info = personal_info
        self._email = email
        self._email_verified = email_verified
        self._phone_number = phone_number
        self._phone_country_code = phone_country_code
        self._is_phone_verified = is_phone_verified
        self._location_id = location_id

    @property
    def patient_id(self) -> UUID:
        return self._patient_id

    @property
    def full_name(self) -> str:
        return f"{self.personal_info.first_name} {self.personal_info.last_name}"

    @property
    def relationship(self) -> str:
        return self._relationship

    @relationship.setter
    def relationship(self, value: CaregiverRelationship) -> None:
        if not value:
            raise ValueError("Relationship cannot be empty")
        self._relationship = value

    @property
    def personal_info(self) -> BasicPersonalInfo:
        return self._personal_info

    @personal_info.setter
    def personal_info(self, value: BasicPersonalInfo) -> None:
        self._personal_info = value

    @property
    def email(self) -> str:
        return self._email

    @email.setter
    def email(self, value: EmailInfo) -> None:
        self._email = value

    @property
    def email_verified(self) -> bool:
        return self._email_verified

    @email_verified.setter
    def email_verified(self, value: bool) -> None:
        self._email_verified = value

    @property
    def phone_number(self) -> str:
        return self._phone_number

    @phone_number.setter
    def phone_number(self, value: str) -> None:
        self._phone_number = value

    @property
    def phone_country_code(self) -> str:
        return self._phone_country_code

    @phone_country_code.setter
    def phone_country_code(self, value: str) -> None:
        self._phone_country_code = value

    @property
    def is_phone_verified(self) -> bool:
        return self._is_phone_verified

    @is_phone_verified.setter
    def is_phone_verified(self, value: bool) -> None:
        self._is_phone_verified = value

    @property
    def location_id(self) -> UUID | None:
        return self._location_id

    @location_id.setter
    def location_id(self, value: UUID | None) -> None:
        self._location_id = value

    def __post_init__(self):
        if not self.personal_info.first_name or not self.personal_info.first_name.strip():
            raise ValueError("First name cannot be empty")
        if not self.personal_info.last_name or not self.personal_info.last_name.strip():
            raise ValueError("Last name cannot be empty")
        if not self.relationship or not self.relationship.strip():
            raise ValueError("Relationship to patient cannot be empty")
        if not self.phone_number:
            raise ValueError("Phone number is required")
