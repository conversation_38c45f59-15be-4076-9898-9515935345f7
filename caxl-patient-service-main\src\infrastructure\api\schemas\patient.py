"""Patient schemas for API layer"""

from datetime import date, datetime
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, constr

from src.application.dtos.patient import PatientDTO


class BasicPersonalInfoSchema(BaseModel):
    """Basic personal information schema"""

    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    gender: str | None = Field(..., min_length=1, max_length=50)
    dob: date | None


class PersonalInfoSchema(BaseModel):
    """Personal information schema"""

    basic_info: BasicPersonalInfoSchema
    ssn: constr(pattern=r"^\d{9}$") | None = None
    ethnicity: str | None = Field(None, max_length=50)
    race: str | None = Field(None, max_length=50)


class PhoneTypeSchema(str, Enum):
    HOME = "home"
    MOBILE = "mobile"
    WORK = "work"
    EMERGENCY = "911"


class LocationNameSchema(str, Enum):
    HOME = "home"
    WORK = "work"
    FACILITY = "facility"
    FAMILY = "family"


class EmailInfoSchema(BaseModel):
    id: str | None = None
    email: str
    email_type: str | None = None
    is_verified: bool = False


class PhoneInfoSchema(BaseModel):
    id: str | None = None
    phone_number: str
    phone_type: PhoneTypeSchema | None = None
    phone_country_code: str | None = None
    is_primary: bool = False
    is_verified: bool = False
    preferred_for_sms: bool = False


class LocationSchema(BaseModel):
    id: str | None = None
    location_name: LocationNameSchema
    location_id: UUID
    is_primary: bool = False


class EmergencyContactRelationshipSchema(str, Enum):
    SPOUSE = "spouse"
    DAUGHTER = "daughter"
    SON = "son"
    FRIEND = "friend"
    PARENT = "parent"
    SIBLING = "sibling"
    OTHER = "other"


class EmergencyContactSchema(BaseModel):
    id: str | None = None
    first_name: str
    last_name: str
    phone_number: str
    phone_country_code: str
    relationship: EmergencyContactRelationshipSchema
    email: str


class ContactInfoSchema(BaseModel):
    emails: list[EmailInfoSchema]
    phones: list[PhoneInfoSchema]
    locations: list[LocationSchema]
    emergency_contacts: list[EmergencyContactSchema]


class PatientPIISchema(BaseModel):
    """Patient PII schema"""

    id: UUID | None = None
    personal_info: PersonalInfoSchema
    contact_info: ContactInfoSchema


class LabPreferenceSchema(BaseModel):
    """Lab preference schema"""

    name: str = Field(..., min_length=1, max_length=100)
    location_id: UUID
    phone: str
    phone_country_code: str
    email: EmailStr | None = None


class PharmacyPreferenceSchema(BaseModel):
    """Pharmacy preference schema"""

    name: str = Field(..., min_length=1, max_length=100)
    location_id: UUID
    phone: constr(pattern=r"^\d{10}$") | None = None
    email: EmailStr | None = None


class PatientPreferencesSchema(BaseModel):
    """Patient preferences schema"""

    id: UUID | None = None
    pref_language: str | None = Field(None, max_length=50)
    lab_preference: LabPreferenceSchema | None = None
    pharmacy_preference: PharmacyPreferenceSchema | None = None
    time_pref: str | None = Field(None, max_length=50)


class InsuranceSchema(BaseModel):
    """Insurance schema"""

    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(..., min_length=1, max_length=50)
    number: str = Field(..., min_length=1, max_length=50)


class MedicalProfileSchema(BaseModel):
    """Medical profile schema"""

    id: UUID | None = None
    insurance: InsuranceSchema
    clinical_diagnosis: str | None = Field(None, max_length=500)
    medical_history: str | None = Field(None, max_length=1000)
    social_history: str | None = Field(None, max_length=1000)
    allergies: str | None = Field(None, max_length=500)
    notes: str | None = Field(None, max_length=1000)


class CaregiverSchema(BaseModel):
    """Caregiver schema"""

    id: UUID | None = None
    personal_info: BasicPersonalInfoSchema
    email: str
    email_verified: bool
    phone_number: str
    phone_country_code: str
    is_phone_verified: bool
    location_id: UUID
    relationship: str = Field(..., min_length=1, max_length=50)


class ReferringMRNSchema(BaseModel):
    """Referring MRN schema"""

    id: UUID | None = None
    referring_mrn: str = Field(..., min_length=1, max_length=50)
    referring_name: str | None = Field(None, max_length=100)
    referring_state: str | None = Field(None, max_length=2)
    referring_hospital: str | None = Field(None, max_length=100)
    referring_npi: str | None = Field(None, max_length=10)
    inpatient_discharge_date: datetime | None = None


class PatientListResponseSchema(BaseModel):
    """Patient list response schema"""

    patients: list[PatientDTO]
    total: int
    page: int
    page_size: int
    total_pages: int


class CreatePatientRequestSchema(BaseModel):
    """Request schema for creating a patient via API"""

    pii: PatientPIISchema
    preferences: PatientPreferencesSchema | None = None
    profile: MedicalProfileSchema | None = None
    referring_mrns: list[ReferringMRNSchema] | None = Field(default_factory=list)
    caregivers: list[CaregiverSchema] | None = Field(default_factory=list)
    mpu_id: str | None = Field(None, max_length=50)
    nhid: str | None = Field(None, max_length=50)


class UpdatePatientRequestSchema(BaseModel):
    """Request schema for updating a patient via API"""

    pii: PatientPIISchema | None = None
    preferences: PatientPreferencesSchema | None = None
    profile: MedicalProfileSchema | None = None
    referring_mrns: list[ReferringMRNSchema] | None = None
    mpu_id: str | None = Field(None, max_length=50)
    nhid: str | None = Field(None, max_length=50)


class PatientResponseSchema(BaseModel):
    """Response schema for patient data via API"""

    id: UUID
    pii: PatientPIISchema | None = None
    preferences: PatientPreferencesSchema | None = None
    profile: MedicalProfileSchema | None = None
    referring_mrns: list[ReferringMRNSchema] | None = Field(default_factory=list)
    mpu_id: str | None = Field(None, max_length=50)
    nhid: str | None = Field(None, max_length=50)
    record_status: str | None = None

    class Config:
        """Pydantic model configuration"""

        from_attributes = True
