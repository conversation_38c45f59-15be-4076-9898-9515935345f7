"""Provider service API client"""

import httpx
from typing import List, Optional, Dict, Any
from src.config.settings import settings


class ProviderServiceClient:
    """Client for provider service API"""
    
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or settings.PROVIDER_SERVICE_URL
        self.timeout = 30.0
    
    async def get_providers(self, provider_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get providers from provider service"""
        
        # For now, return mock data (stub implementation)
        # In production, this would make actual API calls
        mock_providers = [
            {
                "id": "provider-001",
                "name": "<PERSON>",
                "role": "RN",
                "skills": ["wound_care", "medication_administration", "patient_assessment"],
                "status": "active",
                "location": {
                    "latitude": 40.7589,
                    "longitude": -73.9851,
                    "address": "Manhattan, NY"
                },
                "service_radius_miles": 25.0,
                "email": "<EMAIL>",
                "phone": "(*************",
                "availability": {
                    "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                    "working_hours": {"start": "08:00", "end": "17:00"},
                    "break_periods": [{"start": "12:00", "end": "13:00"}],
                    "max_appointments_per_day": 8,
                    "max_hours_per_day": 8
                }
            },
            {
                "id": "provider-002",
                "name": "Michael Chen",
                "role": "LPN",
                "skills": ["basic_care", "vital_signs", "medication_administration"],
                "status": "active",
                "location": {
                    "latitude": 40.7505,
                    "longitude": -73.9934,
                    "address": "Manhattan, NY"
                },
                "service_radius_miles": 20.0,
                "email": "<EMAIL>",
                "phone": "(*************",
                "availability": {
                    "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                    "working_hours": {"start": "09:00", "end": "18:00"},
                    "break_periods": [{"start": "13:00", "end": "14:00"}],
                    "max_appointments_per_day": 6,
                    "max_hours_per_day": 8
                }
            },
            {
                "id": "provider-003",
                "name": "Maria Garcia",
                "role": "CNA",
                "skills": ["personal_care", "mobility_assistance", "companionship"],
                "status": "active",
                "location": {
                    "latitude": 40.7614,
                    "longitude": -73.9776,
                    "address": "Manhattan, NY"
                },
                "service_radius_miles": 15.0,
                "email": "<EMAIL>",
                "phone": "(*************",
                "availability": {
                    "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"],
                    "working_hours": {"start": "07:00", "end": "16:00"},
                    "break_periods": [{"start": "11:30", "end": "12:30"}],
                    "max_appointments_per_day": 10,
                    "max_hours_per_day": 8
                }
            }
        ]
        
        # Filter by provider_ids if provided
        if provider_ids:
            mock_providers = [p for p in mock_providers if p["id"] in provider_ids]
        
        return mock_providers
    
    async def get_provider(self, provider_id: str) -> Optional[Dict[str, Any]]:
        """Get single provider by ID"""
        providers = await self.get_providers([provider_id])
        return providers[0] if providers else None
    
    async def get_available_providers(self, date: str, skills: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get providers available on specific date with optional skill filtering"""
        providers = await self.get_providers()
        
        # Filter by skills if provided
        if skills:
            filtered_providers = []
            for provider in providers:
                provider_skills = [skill.lower() for skill in provider.get("skills", [])]
                if any(skill.lower() in provider_skills for skill in skills):
                    filtered_providers.append(provider)
            providers = filtered_providers
        
        return providers
