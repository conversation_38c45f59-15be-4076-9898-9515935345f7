#!/usr/bin/env python3
"""
API test script for CareAxl Scheduling Engine.

This script tests the scheduling engine APIs with scenario-based test data.
"""

import asyncio
import sys
import json
import time
import requests
from pathlib import Path
from typing import Dict, Any, List
import logging

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from infrastructure.data.data_loader import SchedulingDataLoader, list_available_scenarios

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class APITester:
    """API test runner for scheduling scenarios."""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def test_scenario_apis(self, scenario_name: str) -> Dict[str, Any]:
        """Test all APIs with scenario data."""
        logger.info(f"🚀 Testing APIs with scenario: {scenario_name}")
        
        try:
            # Load scenario data
            scenario_path = f"data/scenarios/{scenario_name}"
            loader = SchedulingDataLoader(scenario_path)
            data = loader.load_all_data()
            
            logger.info(f"📊 Loaded data: {len(data['providers'])} providers, "
                       f"{len(data['consumers'])} patients, {len(data['appointments'])} appointments")
            
            results = {
                'scenario': scenario_name,
                'data_summary': {
                    'providers': len(data['providers']),
                    'consumers': len(data['consumers']),
                    'appointments': len(data['appointments'])
                },
                'api_tests': {}
            }
            
            # Test 1: Health Check
            logger.info("🏥 Testing health check...")
            health_result = self._test_health()
            results['api_tests']['health'] = health_result
            
            # Test 2: Status Check
            logger.info("📊 Testing status...")
            status_result = self._test_status()
            results['api_tests']['status'] = status_result
            
            # Test 3: Assignment API
            logger.info("🔄 Testing assignment API...")
            assignment_result = self._test_assignment_api(data)
            results['api_tests']['assignment'] = assignment_result
            
            # Test 4: Day Plan API
            logger.info("🗓️ Testing day plan API...")
            dayplan_result = self._test_dayplan_api(data)
            results['api_tests']['dayplan'] = dayplan_result
            
            # Test 5: Replan API
            logger.info("🔄 Testing replan API...")
            replan_result = self._test_replan_api(data)
            results['api_tests']['replan'] = replan_result
            
            # Test 6: Pinned Appointments (if applicable)
            pinned_appointments = [apt for apt in data['appointments'] if apt.get('is_pinned', False)]
            if pinned_appointments:
                logger.info("📌 Testing pinned appointments API...")
                pinned_result = self._test_pinned_appointments_api(data, pinned_appointments)
                results['api_tests']['pinned_appointments'] = pinned_result
            
            logger.info(f"✅ API tests for scenario {scenario_name} completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ API tests for scenario {scenario_name} failed: {e}")
            return {
                'scenario': scenario_name,
                'error': str(e),
                'success': False
            }
    
    def _test_health(self) -> Dict[str, Any]:
        """Test health check endpoint."""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health")
            duration = time.time() - start_time
            
            return {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response': response.json() if response.status_code == 200 else response.text,
                'duration_seconds': duration
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }
    
    def _test_status(self) -> Dict[str, Any]:
        """Test status endpoint."""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/v1/scheduling/status")
            duration = time.time() - start_time
            
            return {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response': response.json() if response.status_code == 200 else response.text,
                'duration_seconds': duration
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }
    
    def _test_assignment_api(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test assignment API."""
        try:
            start_time = time.time()
            
            # Get pending appointments
            pending_appointments = [apt for apt in data['appointments'] if apt.get('status') == 'pending']
            appointment_ids = [apt['id'] for apt in pending_appointments[:5]]  # Test with first 5
            
            request_data = {
                'appointment_batch_ids': appointment_ids,
                'constraints': {
                    'enable_geographic_clustering': True,
                    'enable_skill_matching': True
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/scheduling/assign",
                json=request_data
            )
            duration = time.time() - start_time
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'duration_seconds': duration,
                'request_data': request_data
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result['response'] = {
                    'success': response_data.get('success'),
                    'message': response_data.get('message'),
                    'assigned_count': response_data.get('assigned_count'),
                    'failed_count': response_data.get('failed_count'),
                    'assignments_sample': response_data.get('assignments', [])[:3]  # First 3
                }
            else:
                result['error'] = response.text
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }
    
    def _test_dayplan_api(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test day plan API."""
        try:
            start_time = time.time()
            
            request_data = {
                'target_date': '2025-06-25',
                'provider_ids': None
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/scheduling/dayplan",
                json=request_data
            )
            duration = time.time() - start_time
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'duration_seconds': duration,
                'request_data': request_data
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result['response'] = {
                    'success': response_data.get('success'),
                    'message': response_data.get('message'),
                    'optimized_count': response_data.get('optimized_count'),
                    'visit_schedules_count': len(response_data.get('visit_schedules', [])),
                    'sample_schedule': response_data.get('visit_schedules', [])[:1]  # First schedule
                }
            else:
                result['error'] = response.text
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }
    
    def _test_replan_api(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test replan API."""
        try:
            start_time = time.time()
            
            # Test with first 2 appointments
            appointment_ids = [apt['id'] for apt in data['appointments'][:2]]
            
            request_data = {
                'appointment_ids': appointment_ids,
                'reason': 'patient_request',
                'preferred_date': '2025-06-27'
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/scheduling/replan",
                json=request_data
            )
            duration = time.time() - start_time
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'duration_seconds': duration,
                'request_data': request_data
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result['response'] = {
                    'success': response_data.get('success'),
                    'message': response_data.get('message'),
                    'replanned_count': response_data.get('replanned_count'),
                    'failed_count': response_data.get('failed_count'),
                    'new_assignments': response_data.get('new_assignments', [])
                }
            else:
                result['error'] = response.text
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }
    
    def _test_pinned_appointments_api(self, data: Dict[str, Any], pinned_appointments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test pinned appointments handling."""
        try:
            start_time = time.time()
            
            # Test assignment with pinned appointments
            pinned_ids = [apt['id'] for apt in pinned_appointments]
            
            request_data = {
                'appointment_batch_ids': pinned_ids,
                'constraints': {
                    'respect_pinned_times': True
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/scheduling/assign",
                json=request_data
            )
            duration = time.time() - start_time
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'duration_seconds': duration,
                'request_data': request_data,
                'pinned_appointments_tested': len(pinned_appointments)
            }
            
            if response.status_code == 200:
                response_data = response.json()
                assignments = response_data.get('assignments', [])
                
                # Check if pinned times were preserved
                pinned_preserved = 0
                for assignment in assignments:
                    apt_id = assignment.get('appointment_id')
                    original_apt = next((apt for apt in pinned_appointments if apt['id'] == apt_id), None)
                    
                    if original_apt and original_apt.get('is_pinned'):
                        if assignment.get('assigned_time') == original_apt.get('pinned_time'):
                            pinned_preserved += 1
                
                result['response'] = {
                    'success': response_data.get('success'),
                    'message': response_data.get('message'),
                    'assigned_count': response_data.get('assigned_count'),
                    'pinned_preserved': pinned_preserved,
                    'total_pinned': len(pinned_appointments),
                    'assignments_sample': assignments[:2]  # First 2
                }
            else:
                result['error'] = response.text
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration_seconds': 0
            }


def main():
    """Main API test runner."""
    if len(sys.argv) < 2:
        print("Usage: python test_api.py <scenario_name> [--all] [--url <base_url>]")
        print("\nAvailable scenarios:")
        scenarios = list_available_scenarios()
        for scenario in scenarios:
            print(f"  - {scenario['name']}: {scenario['description']}")
        return
    
    # Parse arguments
    base_url = "http://localhost:8080"
    if "--url" in sys.argv:
        url_index = sys.argv.index("--url")
        if url_index + 1 < len(sys.argv):
            base_url = sys.argv[url_index + 1]
    
    tester = APITester(base_url)
    
    if sys.argv[1] == "--all":
        # Test all scenarios
        scenarios = list_available_scenarios()
        for scenario in scenarios:
            result = tester.test_scenario_apis(scenario['name'])
            print(f"\n{'='*60}")
            print(f"API TESTS: {scenario['name']}")
            print(f"{'='*60}")
            print(json.dumps(result, indent=2))
    else:
        # Test specific scenario
        scenario_name = sys.argv[1]
        result = tester.test_scenario_apis(scenario_name)
        print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()
