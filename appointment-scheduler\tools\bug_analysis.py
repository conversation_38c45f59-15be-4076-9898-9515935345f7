#!/usr/bin/env python3
"""
Bug Analysis and Gap Detection Tool

Analyzes the appointment scheduler codebase for potential bugs,
missing components, and gaps in functionality.
"""

import sys
import ast
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Set
import yaml

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class BugAnalyzer:
    """Analyzes codebase for potential bugs and gaps."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src" / "appointment_scheduler"
        self.tests_dir = self.project_root / "tests"
        self.data_dir = self.project_root / "data"
        self.config_dir = self.project_root / "config"
        
        self.issues = []
        self.gaps = []
        self.recommendations = []
    
    def analyze_all(self) -> Dict[str, Any]:
        """Run comprehensive analysis."""
        print("🔍 Starting comprehensive bug analysis...")
        
        # Check project structure
        self._check_project_structure()
        
        # Check imports and dependencies
        self._check_imports()
        
        # Check configuration files
        self._check_configuration()
        
        # Check data files
        self._check_data_files()
        
        # Check test coverage
        self._check_test_coverage()
        
        # Check for common Python issues
        self._check_python_issues()
        
        # Check for scheduler-specific issues
        self._check_scheduler_issues()
        
        return self._generate_report()
    
    def _check_project_structure(self):
        """Check if all required directories and files exist."""
        print("  📁 Checking project structure...")
        
        required_dirs = [
            self.src_dir,
            self.tests_dir,
            self.data_dir,
            self.config_dir,
            self.src_dir / "jobs",
            self.src_dir / "constraints",
            self.src_dir / "api",
            self.src_dir / "utils"
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                self.issues.append({
                    "type": "missing_directory",
                    "severity": "medium",
                    "path": str(dir_path),
                    "message": f"Required directory missing: {dir_path}"
                })
        
        required_files = [
            self.src_dir / "__init__.py",
            self.src_dir / "domain.py",
            self.src_dir / "data_loader.py",
            self.src_dir / "scheduler.py",
            self.src_dir / "jobs" / "__init__.py",
            self.src_dir / "jobs" / "assign_appointments.py",
            self.src_dir / "jobs" / "day_plan.py",
            self.project_root / "pyproject.toml"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                self.issues.append({
                    "type": "missing_file",
                    "severity": "high",
                    "path": str(file_path),
                    "message": f"Required file missing: {file_path}"
                })
    
    def _check_imports(self):
        """Check for import issues."""
        print("  📦 Checking imports...")
        
        python_files = list(self.src_dir.rglob("*.py"))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                try:
                    tree = ast.parse(content)
                    self._analyze_imports_in_ast(tree, file_path)
                except SyntaxError as e:
                    self.issues.append({
                        "type": "syntax_error",
                        "severity": "high",
                        "path": str(file_path),
                        "message": f"Syntax error: {e}"
                    })
            except Exception as e:
                self.issues.append({
                    "type": "file_read_error",
                    "severity": "medium",
                    "path": str(file_path),
                    "message": f"Could not read file: {e}"
                })
    
    def _analyze_imports_in_ast(self, tree: ast.AST, file_path: Path):
        """Analyze imports in AST."""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    self._check_import_availability(alias.name, file_path)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    self._check_import_availability(node.module, file_path)
    
    def _check_import_availability(self, module_name: str, file_path: Path):
        """Check if import is available."""
        # Skip relative imports and built-ins
        if module_name.startswith('.') or module_name in ['sys', 'os', 'time', 'datetime', 'typing', 'pathlib']:
            return
        
        try:
            importlib.import_module(module_name)
        except ImportError:
            # Check if it's a project-internal import
            if not module_name.startswith('appointment_scheduler'):
                self.issues.append({
                    "type": "missing_dependency",
                    "severity": "high",
                    "path": str(file_path),
                    "message": f"Missing dependency: {module_name}"
                })
    
    def _check_configuration(self):
        """Check configuration files."""
        print("  ⚙️  Checking configuration...")
        
        config_files = [
            "scheduler.yml",
            "physical_therapy.yml",
            "skilled_nursing.yml",
            "logger.yaml"
        ]
        
        for config_file in config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r') as f:
                        yaml.safe_load(f)
                except yaml.YAMLError as e:
                    self.issues.append({
                        "type": "invalid_yaml",
                        "severity": "high",
                        "path": str(config_path),
                        "message": f"Invalid YAML: {e}"
                    })
            else:
                self.gaps.append({
                    "type": "missing_config",
                    "severity": "medium",
                    "path": str(config_path),
                    "message": f"Configuration file missing: {config_file}"
                })
    
    def _check_data_files(self):
        """Check data files and scenarios."""
        print("  📊 Checking data files...")
        
        # Check if basic data files exist
        basic_data_files = ["providers.yml", "consumers.yml", "appointments.yml"]
        data_exists = all((self.data_dir / f).exists() for f in basic_data_files)
        
        if not data_exists:
            self.gaps.append({
                "type": "missing_data",
                "severity": "medium",
                "message": "Basic data files missing in data/ directory"
            })
        
        # Check scenarios
        scenarios_dir = self.data_dir / "scenarios"
        if scenarios_dir.exists():
            scenario_count = len([d for d in scenarios_dir.iterdir() if d.is_dir()])
            if scenario_count == 0:
                self.gaps.append({
                    "type": "no_scenarios",
                    "severity": "low",
                    "message": "No test scenarios found"
                })
        else:
            self.gaps.append({
                "type": "missing_scenarios_dir",
                "severity": "medium",
                "message": "Scenarios directory missing"
            })
    
    def _check_test_coverage(self):
        """Check test coverage."""
        print("  🧪 Checking test coverage...")
        
        if not self.tests_dir.exists():
            self.gaps.append({
                "type": "no_tests",
                "severity": "high",
                "message": "Tests directory missing"
            })
            return
        
        test_files = list(self.tests_dir.glob("test_*.py"))
        if len(test_files) == 0:
            self.gaps.append({
                "type": "no_test_files",
                "severity": "high",
                "message": "No test files found"
            })
        
        # Check if main modules have corresponding tests
        main_modules = [
            "data_loader",
            "scheduler",
            "domain",
            "planning_models"
        ]
        
        for module in main_modules:
            test_file = self.tests_dir / f"test_{module}.py"
            if not test_file.exists():
                self.gaps.append({
                    "type": "missing_test",
                    "severity": "medium",
                    "message": f"No tests for {module}.py"
                })
    
    def _check_python_issues(self):
        """Check for common Python issues."""
        print("  🐍 Checking Python issues...")
        
        python_files = list(self.src_dir.rglob("*.py"))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for common issues
                if "print(" in content and "logger" in content:
                    self.issues.append({
                        "type": "mixed_logging",
                        "severity": "low",
                        "path": str(file_path),
                        "message": "File contains both print() and logger calls"
                    })
                
                if "TODO" in content or "FIXME" in content:
                    self.issues.append({
                        "type": "todo_comment",
                        "severity": "low",
                        "path": str(file_path),
                        "message": "File contains TODO/FIXME comments"
                    })
                
                # Check for hardcoded paths
                if "/tmp/" in content or "C:\\" in content:
                    self.issues.append({
                        "type": "hardcoded_path",
                        "severity": "medium",
                        "path": str(file_path),
                        "message": "File contains hardcoded paths"
                    })
                    
            except Exception:
                pass  # Already handled in import check
    
    def _check_scheduler_issues(self):
        """Check for scheduler-specific issues."""
        print("  📅 Checking scheduler-specific issues...")
        
        # Check if jobs can be imported
        try:
            from appointment_scheduler.jobs.assign_appointments import AssignAppointmentJob
            from appointment_scheduler.jobs.day_plan import DayPlanJob
        except ImportError as e:
            self.issues.append({
                "type": "job_import_error",
                "severity": "high",
                "message": f"Cannot import job classes: {e}"
            })
        
        # Check if data loader works
        try:
            from appointment_scheduler.data_loader import DataLoader
            # Try to create instance (may fail if no data)
            loader = DataLoader("data")
        except Exception as e:
            self.issues.append({
                "type": "data_loader_error",
                "severity": "high",
                "message": f"DataLoader issues: {e}"
            })
        
        # Check if API can be imported
        try:
            from appointment_scheduler.api.app import create_app
        except ImportError as e:
            self.issues.append({
                "type": "api_import_error",
                "severity": "medium",
                "message": f"Cannot import API: {e}"
            })
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive report."""
        # Categorize issues by severity
        high_issues = [i for i in self.issues if i.get("severity") == "high"]
        medium_issues = [i for i in self.issues if i.get("severity") == "medium"]
        low_issues = [i for i in self.issues if i.get("severity") == "low"]
        
        # Generate recommendations
        if high_issues:
            self.recommendations.append("Address high-severity issues immediately")
        if len(self.gaps) > 5:
            self.recommendations.append("Consider implementing missing components")
        if not self.tests_dir.exists():
            self.recommendations.append("Set up comprehensive test suite")
        
        return {
            "summary": {
                "total_issues": len(self.issues),
                "high_severity": len(high_issues),
                "medium_severity": len(medium_issues),
                "low_severity": len(low_issues),
                "total_gaps": len(self.gaps)
            },
            "issues": {
                "high": high_issues,
                "medium": medium_issues,
                "low": low_issues
            },
            "gaps": self.gaps,
            "recommendations": self.recommendations
        }
    
    def print_report(self, report: Dict[str, Any]):
        """Print formatted report."""
        summary = report["summary"]
        
        print(f"\n{'='*60}")
        print("🐛 BUG ANALYSIS REPORT")
        print(f"{'='*60}")
        
        print(f"📊 SUMMARY:")
        print(f"  Total Issues: {summary['total_issues']}")
        print(f"  High Severity: {summary['high_severity']}")
        print(f"  Medium Severity: {summary['medium_severity']}")
        print(f"  Low Severity: {summary['low_severity']}")
        print(f"  Gaps: {summary['total_gaps']}")
        
        # Print high severity issues
        if report["issues"]["high"]:
            print(f"\n🚨 HIGH SEVERITY ISSUES:")
            for issue in report["issues"]["high"]:
                print(f"  - {issue['message']}")
                if "path" in issue:
                    print(f"    Path: {issue['path']}")
        
        # Print gaps
        if report["gaps"]:
            print(f"\n🔍 GAPS IDENTIFIED:")
            for gap in report["gaps"][:5]:  # Show first 5
                print(f"  - {gap['message']}")
            if len(report["gaps"]) > 5:
                print(f"    ... and {len(report['gaps']) - 5} more")
        
        # Print recommendations
        if report["recommendations"]:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in report["recommendations"]:
                print(f"  - {rec}")
        
        print(f"\n{'='*60}")


def main():
    """Main entry point."""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description='Analyze codebase for bugs and gaps')
    parser.add_argument('--project-root', default='.', help='Project root directory')
    parser.add_argument('--output', help='Output file for detailed report (JSON)')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix simple issues')
    
    args = parser.parse_args()
    
    try:
        analyzer = BugAnalyzer(args.project_root)
        report = analyzer.analyze_all()
        analyzer.print_report(report)
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"\n📄 Detailed report saved to: {args.output}")
        
        # Exit with error code if high severity issues found
        if report["summary"]["high_severity"] > 0:
            print(f"\n❌ Found {report['summary']['high_severity']} high-severity issues")
            sys.exit(1)
        else:
            print(f"\n✅ No high-severity issues found")
    
    except Exception as e:
        print(f"❌ Bug analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
