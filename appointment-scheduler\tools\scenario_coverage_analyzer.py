#!/usr/bin/env python3
"""
Scenario Coverage Analyzer

Analyzes existing scenarios and identifies gaps in test coverage across
the specified dimensions for appointment scheduling optimization.
"""

import sys
from pathlib import Path
from typing import Dict, List, Set, Any
import yaml

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from appointment_scheduler.utils.scenario_validator import ScenarioValidator


class ScenarioCoverageAnalyzer:
    """Analyzes scenario coverage across test dimensions."""
    
    # Define the required test dimensions from the requirements
    REQUIRED_DIMENSIONS = {
        "skill_matching": [
            "perfect_match", "partial_match", "hierarchy_match", 
            "missing_skills", "invalid_skills", "case_sensitivity"
        ],
        "availability": [
            "available", "unavailable", "blackout_periods", "weekend_only",
            "holidays", "leap_years", "dst_transitions"
        ],
        "location": [
            "inside_service_area", "outside_service_area", "invalid_coordinates",
            "cross_state", "rural_areas"
        ],
        "time_matching": [
            "exact_match", "windowed_match", "flexible_timing", "strict_timing",
            "timezone_handling", "leap_seconds"
        ],
        "working_hours": [
            "within_hours", "outside_hours", "break_periods", "overtime",
            "holiday_hours", "shift_patterns"
        ],
        "appointment_overlap": [
            "no_overlap", "partial_overlap", "full_overlap", "adjacency",
            "zero_duration", "long_duration", "concurrent_appointments"
        ],
        "punctuality": [
            "on_time", "early_arrival", "late_arrival", "time_deviations",
            "preference_changes"
        ],
        "task_order": [
            "sequential_tasks", "parallel_tasks", "dependency_chains",
            "orphaned_tasks", "broken_dependencies"
        ],
        "travel_optimization": [
            "minimal_travel", "multiple_stops", "base_returns",
            "traffic_considerations", "fuel_optimization"
        ],
        "load_balancing": [
            "underloaded_providers", "overloaded_providers", "skill_based_balancing",
            "seasonal_variations", "emergency_handling"
        ],
        "geographic_distribution": [
            "clustered_appointments", "scattered_appointments", "urban_areas",
            "rural_areas", "island_locations", "cross_region"
        ],
        "preferences": [
            "single_preferences", "multiple_preferences", "preference_changes",
            "conflicting_preferences", "cultural_considerations"
        ],
        "capacity": [
            "optimal_capacity", "under_capacity", "over_capacity",
            "dynamic_capacity", "seasonal_capacity", "emergency_capacity"
        ],
        "provider_assignment": [
            "same_provider", "different_providers", "episode_continuity",
            "care_teams", "provider_changes"
        ]
    }
    
    def __init__(self, scenarios_dir: str = "data/scenarios"):
        self.scenarios_dir = Path(scenarios_dir)
        self.scenarios = {}
        self.coverage_matrix = {}
        
    def analyze_scenarios(self) -> Dict[str, Any]:
        """Analyze all scenarios and generate coverage report."""
        print("🔍 Analyzing scenario coverage...")
        
        # Discover and validate scenarios
        self._discover_scenarios()
        
        # Analyze coverage
        self._analyze_coverage()
        
        # Generate report
        return self._generate_coverage_report()
    
    def _discover_scenarios(self):
        """Discover and validate all scenarios."""
        if not self.scenarios_dir.exists():
            raise FileNotFoundError(f"Scenarios directory not found: {self.scenarios_dir}")
        
        for scenario_path in self.scenarios_dir.iterdir():
            if scenario_path.is_dir() and not scenario_path.name.startswith('.'):
                validator = ScenarioValidator(scenario_path)
                validation_result = validator.validate_scenario()
                
                self.scenarios[scenario_path.name] = {
                    "path": scenario_path,
                    "valid": validation_result["valid"],
                    "errors": validation_result["errors"],
                    "warnings": validation_result["warnings"],
                    "dimensions": self._analyze_scenario_dimensions(scenario_path)
                }
    
    def _analyze_scenario_dimensions(self, scenario_path: Path) -> Dict[str, List[str]]:
        """Analyze which dimensions a scenario covers."""
        dimensions = {}
        
        # Read scenario files to understand what it tests
        try:
            # Read README for explicit dimension information
            readme_path = scenario_path / "README.md"
            if readme_path.exists():
                readme_content = readme_path.read_text().lower()
                dimensions.update(self._extract_dimensions_from_readme(readme_content))
            
            # Analyze YAML files for implicit dimensions
            if (scenario_path / "providers.yml").exists():
                with open(scenario_path / "providers.yml", 'r') as f:
                    providers_data = yaml.safe_load(f)
                    dimensions.update(self._analyze_providers_dimensions(providers_data))
            
            if (scenario_path / "consumers.yml").exists():
                with open(scenario_path / "consumers.yml", 'r') as f:
                    consumers_data = yaml.safe_load(f)
                    dimensions.update(self._analyze_consumers_dimensions(consumers_data))
            
            if (scenario_path / "appointments.yml").exists():
                with open(scenario_path / "appointments.yml", 'r') as f:
                    appointments_data = yaml.safe_load(f)
                    dimensions.update(self._analyze_appointments_dimensions(appointments_data))
                    
        except Exception as e:
            print(f"⚠️  Error analyzing {scenario_path.name}: {e}")
        
        return dimensions
    
    def _extract_dimensions_from_readme(self, readme_content: str) -> Dict[str, List[str]]:
        """Extract dimension information from README content."""
        dimensions = {}
        
        # Look for dimension keywords in README
        for dimension, subcategories in self.REQUIRED_DIMENSIONS.items():
            if dimension.replace("_", " ") in readme_content or dimension in readme_content:
                dimensions[dimension] = []
                
                # Look for specific subcategories
                for subcategory in subcategories:
                    if subcategory.replace("_", " ") in readme_content:
                        dimensions[dimension].append(subcategory)
        
        return dimensions
    
    def _analyze_providers_dimensions(self, providers_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Analyze provider data for dimension coverage."""
        dimensions = {}
        
        if not providers_data or 'providers' not in providers_data:
            return dimensions
        
        providers = providers_data['providers']
        
        # Skill matching analysis
        skill_variations = set()
        for provider in providers:
            skills = provider.get('skills', [])
            if not skills:
                skill_variations.add('missing_skills')
            elif len(skills) == 1:
                skill_variations.add('perfect_match')
            else:
                skill_variations.add('partial_match')
        
        if skill_variations:
            dimensions['skill_matching'] = list(skill_variations)
        
        # Availability analysis
        availability_variations = set()
        for provider in providers:
            availability = provider.get('availability', {})
            working_days = availability.get('working_days', [])
            
            if not working_days:
                availability_variations.add('unavailable')
            elif set(working_days) == {'saturday', 'sunday'}:
                availability_variations.add('weekend_only')
            elif len(working_days) < 5:
                availability_variations.add('partial_availability')
            else:
                availability_variations.add('available')
        
        if availability_variations:
            dimensions['availability'] = list(availability_variations)
        
        return dimensions
    
    def _analyze_consumers_dimensions(self, consumers_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Analyze consumer data for dimension coverage."""
        dimensions = {}
        
        if not consumers_data or 'consumers' not in consumers_data:
            return dimensions
        
        consumers = consumers_data['consumers']
        
        # Geographic distribution analysis
        locations = []
        for consumer in consumers:
            location = consumer.get('location', {})
            if location:
                lat = location.get('latitude', 0)
                lng = location.get('longitude', 0)
                locations.append((lat, lng))
        
        if len(locations) > 1:
            # Simple clustering analysis
            if self._are_locations_clustered(locations):
                dimensions.setdefault('geographic_distribution', []).append('clustered_appointments')
            else:
                dimensions.setdefault('geographic_distribution', []).append('scattered_appointments')
        
        # Preferences analysis
        preference_variations = set()
        for consumer in consumers:
            preferences = consumer.get('consumer_preferences', {})
            if preferences:
                if preferences.get('language') and preferences['language'] != 'English':
                    preference_variations.add('cultural_considerations')
                if preferences.get('preferred_days'):
                    preference_variations.add('single_preferences')
        
        if preference_variations:
            dimensions['preferences'] = list(preference_variations)
        
        return dimensions
    
    def _analyze_appointments_dimensions(self, appointments_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Analyze appointment data for dimension coverage."""
        dimensions = {}
        
        if not appointments_data or 'appointments' not in appointments_data:
            return dimensions
        
        appointments = appointments_data['appointments']
        
        # Capacity analysis
        if len(appointments) > 10:
            dimensions.setdefault('capacity', []).append('over_capacity')
        elif len(appointments) < 3:
            dimensions.setdefault('capacity', []).append('under_capacity')
        else:
            dimensions.setdefault('capacity', []).append('optimal_capacity')
        
        # Duration analysis
        duration_variations = set()
        for appointment in appointments:
            duration = appointment.get('duration_min', 30)
            if duration == 0:
                duration_variations.add('zero_duration')
            elif duration > 120:
                duration_variations.add('long_duration')
        
        if duration_variations:
            dimensions['appointment_overlap'] = list(duration_variations)
        
        return dimensions
    
    def _are_locations_clustered(self, locations: List[tuple], threshold: float = 0.01) -> bool:
        """Simple clustering check based on coordinate proximity."""
        if len(locations) < 2:
            return False
        
        # Calculate average distance between points
        total_distance = 0
        count = 0
        
        for i in range(len(locations)):
            for j in range(i + 1, len(locations)):
                lat1, lng1 = locations[i]
                lat2, lng2 = locations[j]
                distance = ((lat2 - lat1) ** 2 + (lng2 - lng1) ** 2) ** 0.5
                total_distance += distance
                count += 1
        
        avg_distance = total_distance / count if count > 0 else 0
        return avg_distance < threshold
    
    def _analyze_coverage(self):
        """Analyze coverage across all dimensions."""
        for dimension, subcategories in self.REQUIRED_DIMENSIONS.items():
            self.coverage_matrix[dimension] = {
                'required': subcategories,
                'covered': set(),
                'scenarios': {}
            }
            
            # Check which subcategories are covered by scenarios
            for scenario_name, scenario_info in self.scenarios.items():
                if not scenario_info['valid']:
                    continue
                    
                scenario_dimensions = scenario_info.get('dimensions', {})
                if dimension in scenario_dimensions:
                    covered_subcategories = scenario_dimensions[dimension]
                    for subcategory in covered_subcategories:
                        self.coverage_matrix[dimension]['covered'].add(subcategory)
                        self.coverage_matrix[dimension]['scenarios'].setdefault(subcategory, []).append(scenario_name)
    
    def _generate_coverage_report(self) -> Dict[str, Any]:
        """Generate comprehensive coverage report."""
        total_required = sum(len(info['required']) for info in self.coverage_matrix.values())
        total_covered = sum(len(info['covered']) for info in self.coverage_matrix.values())
        
        coverage_percentage = (total_covered / total_required * 100) if total_required > 0 else 0
        
        # Find gaps
        gaps = {}
        for dimension, info in self.coverage_matrix.items():
            missing = set(info['required']) - info['covered']
            if missing:
                gaps[dimension] = list(missing)
        
        # Valid vs invalid scenarios
        valid_scenarios = [name for name, info in self.scenarios.items() if info['valid']]
        invalid_scenarios = [name for name, info in self.scenarios.items() if not info['valid']]
        
        return {
            'summary': {
                'total_scenarios': len(self.scenarios),
                'valid_scenarios': len(valid_scenarios),
                'invalid_scenarios': len(invalid_scenarios),
                'total_dimensions': len(self.REQUIRED_DIMENSIONS),
                'total_subcategories': total_required,
                'covered_subcategories': total_covered,
                'coverage_percentage': coverage_percentage
            },
            'coverage_matrix': self.coverage_matrix,
            'gaps': gaps,
            'scenarios': {
                'valid': valid_scenarios,
                'invalid': invalid_scenarios
            },
            'scenario_details': self.scenarios
        }
    
    def print_coverage_report(self, report: Dict[str, Any]):
        """Print formatted coverage report."""
        summary = report['summary']
        
        print(f"\n{'='*60}")
        print("📊 SCENARIO COVERAGE ANALYSIS")
        print(f"{'='*60}")
        
        print(f"📁 Total Scenarios: {summary['total_scenarios']}")
        print(f"✅ Valid Scenarios: {summary['valid_scenarios']}")
        print(f"❌ Invalid Scenarios: {summary['invalid_scenarios']}")
        print(f"📋 Coverage: {summary['covered_subcategories']}/{summary['total_subcategories']} ({summary['coverage_percentage']:.1f}%)")
        
        # Print gaps
        if report['gaps']:
            print(f"\n🔍 COVERAGE GAPS:")
            for dimension, missing in report['gaps'].items():
                print(f"  {dimension}:")
                for item in missing:
                    print(f"    - {item}")
        
        # Print invalid scenarios
        if report['scenarios']['invalid']:
            print(f"\n⚠️  INVALID SCENARIOS:")
            for scenario_name in report['scenarios']['invalid']:
                errors = self.scenarios[scenario_name]['errors']
                print(f"  {scenario_name}: {len(errors)} errors")
                for error in errors[:3]:  # Show first 3 errors
                    print(f"    - {error}")
                if len(errors) > 3:
                    print(f"    ... and {len(errors) - 3} more")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze scenario coverage')
    parser.add_argument('--scenarios-dir', default='data/scenarios', help='Scenarios directory path')
    parser.add_argument('--output', help='Output file for detailed report (JSON)')
    
    args = parser.parse_args()
    
    try:
        analyzer = ScenarioCoverageAnalyzer(args.scenarios_dir)
        report = analyzer.analyze_scenarios()
        analyzer.print_coverage_report(report)
        
        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"\n📄 Detailed report saved to: {args.output}")
    
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
