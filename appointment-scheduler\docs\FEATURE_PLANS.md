# Feature Plans and Monetization Tiers

This document outlines the feature plans available for the healthcare appointment scheduling system, organized by implementation status and monetization tiers.

## Current Implementation Status

### ✅ Implemented Constraints (Available for Toggle)

#### Assignment Stage Constraints
- `required_skills` - Provider skill validation (HARD - Always enabled)
- `provider_availability` - Date-based availability (HARD - Always enabled)  
- `provider_role_match` - Role matching (HARD - Always enabled)
- `workload_balancing` - Workload distribution optimization
- `capacity_thresholds` - Geographic clustering optimization
- `geographic_service_area` - Service area constraints
- `continuity_of_care` - Patient continuity optimization
- `patient_preference_matching` - Patient preference matching
- `provider_capacity_management` - Provider capacity constraints

#### Day Planning Stage Constraints
- `time_slot_availability` - Time slot validation (HARD - Always enabled)
- `no_double_booking` - Overlap prevention (HARD - Always enabled)
- `appointment_duration_fit` - Duration fitting (HARD - Always enabled)
- `preferred_hours` - Preferred working hours optimization
- `travel_time_consideration` - Travel time optimization
- `provider_break_time` - Break time management
- `healthcare_task_sequencing` - Medical task sequencing
- `route_optimization_constraints` - Advanced route optimization

## Feature Plans

### 🟢 Basic Plan (Core Features)
**Price: Base tier**
- Geographic clustering optimization
- Continuity of care optimization  
- Workload balancing optimization
- Basic appointment scheduling
- Essential hard constraints (always enabled)

### 🟡 Premium Plan (Advanced Features)
**Price: Base + Premium tier**
- All Basic Plan features
- Patient preference matching
- Provider capacity management
- Healthcare task sequencing
- Travel time optimization
- Break time management
- Route optimization

### 🔴 Enterprise Plan (Full Features)
**Price: Base + Premium + Enterprise tier**
- All Premium Plan features
- Advanced monitoring and metrics
- Detailed logging and constraint tracking
- Email/Slack/SMS notifications
- API access and webhook integrations
- Database persistence
- Custom integrations

## Configuration

Feature toggles are controlled in `config/scheduler.yml`:

```yaml
# Core Feature Toggles (Basic Plan - Implemented)
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true

# Advanced Feature Toggles (Premium Plan - Implemented)
enable_patient_preferences: false
enable_provider_capacity_management: false
enable_healthcare_task_sequencing: false
enable_travel_time_optimization: false
enable_break_time_management: false
enable_route_optimization: false
```

## Future Features (Not Yet Implemented)

The following features are planned but not yet implemented:
- Advanced geographic service areas
- Enhanced provider skill validation
- Time-based availability constraints
- Appointment overlap prevention
- Flexible appointment timing optimization
- Timed visit pinning

These will be added as Enterprise+ tier features when implemented.

## Usage

To enable/disable features for different customer tiers:

1. **Basic Plan**: Set only core features to `true`
2. **Premium Plan**: Enable advanced features as needed
3. **Enterprise Plan**: Enable all features including monitoring and integrations

The system will automatically respect these toggles and only apply the enabled constraints during optimization.

# Feature Plans and Roadmap

## Advanced Traffic Integration (Enterprise Feature)

### Current Implementation
- **Google Maps Routes API**: New Google Routes API for real-time traffic and optimal routing
- **Google Weather API**: Weather-aware routing with real-time conditions
- **OpenWeatherMap Fallback**: Backup weather service when Google Weather API is unavailable
- **Configurable Urban Cities**: Dynamic list of urban areas with speed factors
- **Time-based Adjustments**: Rush hour, weekend, and holiday traffic patterns
- **Weather Impact**: Rain, snow, storms, and temperature effects on travel speed

### API Integration Details

#### Google Maps Routes API
- **Endpoint**: `https://routes.googleapis.com/directions/v2:computeRoutes`
- **Method**: POST with JSON body
- **Features**: 
  - Real-time traffic-aware routing
  - Optimal route selection
  - Departure time specification
  - Traffic duration vs static duration

#### Google Weather API  
- **Endpoint**: `https://weather.googleapis.com/v1/forecast:lookup`
- **Method**: GET with location parameters
- **Features**:
  - Current weather conditions
  - Temperature and precipitation data
  - Weather-aware speed adjustments

#### Fallback System
1. **Primary**: Google Routes API + Google Weather API
2. **Secondary**: OpenWeatherMap for weather data
3. **Tertiary**: Simplified urban/rural model with configurable factors

### Configuration Options
```yaml
traffic_integration:
  google_maps:
    enabled: true
    api_key: "your_api_key"
    base_url: "https://routes.googleapis.com/directions/v2"
    use_routes_api: true
    
  google_weather:
    enabled: true
    api_key: "your_api_key"
    base_url: "https://weather.googleapis.com/v1"
    
  weather:
    enabled: true
    api_key: "your_openweathermap_key"
    provider: "openweathermap"
    
  patterns:
    time_of_day_factors:
      rush_hour_morning: 0.6
      rush_hour_evening: 0.7
      weekend_factor: 1.2
      holiday_factor: 1.3
```

### Weather Impact Factors
- **Rain/Drizzle**: 20% slower travel
- **Snow/Sleet**: 40% slower travel  
- **Storms/Thunder**: 30% slower travel
- **Below Freezing**: 10% slower travel
- **Very Hot (>90°F)**: 5% slower travel

### Future Enhancements
- **Historical Traffic Patterns**: Machine learning-based predictions
- **Multi-modal Routing**: Public transit integration
- **Dynamic Speed Limits**: Real-time speed limit data
- **Accident Reports**: Live incident data integration 