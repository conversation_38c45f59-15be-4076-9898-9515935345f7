"""API integration tests"""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_health_check(client: TestClient):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "appointment-scheduler-service"


def test_readiness_check(client: TestClient):
    """Test readiness check endpoint"""
    response = client.get("/ready")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ready"


def test_list_appointments(client: TestClient):
    """Test list appointments endpoint"""
    response = client.get("/api/v1/appointments/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_get_appointment(client: TestClient):
    """Test get appointment endpoint"""
    response = client.get("/api/v1/appointments/test-id")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "test-id"


def test_list_providers(client: TestClient):
    """Test list providers endpoint"""
    response = client.get("/api/v1/providers/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0


def test_get_provider(client: TestClient):
    """Test get provider endpoint"""
    response = client.get("/api/v1/providers/test-id")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "test-id"


def test_list_consumers(client: TestClient):
    """Test list consumers endpoint"""
    response = client.get("/api/v1/consumers/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0


def test_get_consumer(client: TestClient):
    """Test get consumer endpoint"""
    response = client.get("/api/v1/consumers/test-id")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "test-id"


def test_scheduling_status(client: TestClient):
    """Test scheduling status endpoint"""
    response = client.get("/api/v1/scheduling/status")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "operational"
    assert data["solver_engine"] == "timefold"


def test_assign_appointments(client: TestClient):
    """Test assign appointments endpoint"""
    request_data = {
        "appointment_ids": ["apt-001", "apt-002"],
        "target_date": "2024-01-15",
        "constraints": {}
    }
    response = client.post("/api/v1/scheduling/assign", json=request_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["scheduled_count"] > 0


def test_run_dayplan(client: TestClient):
    """Test run dayplan endpoint"""
    request_data = {
        "target_date": "2024-01-15"
    }
    response = client.post("/api/v1/scheduling/dayplan", json=request_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["assigned_count"] > 0
