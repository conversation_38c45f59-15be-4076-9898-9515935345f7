version: '3.8'

services:
  caxl_patient_service:
    build:
      context: .
      dockerfile: Dockerfile.dev
    network_mode: host
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - HOST=0.0.0.0
      - PORT=8080
      - PROJECT_NAME=caxl-patient-service
      - API_V1_STR=/api/v1
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=True
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      - CORS_EXPOSE_HEADERS=["*"]
      - DB_HOST=localhost
      - DB_PORT=5434
      - DB_USER=admin
      - DB_PASSWORD=patientDb
      - DB_ECHO=false
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
    volumes:
      - .:/app
    command: uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
