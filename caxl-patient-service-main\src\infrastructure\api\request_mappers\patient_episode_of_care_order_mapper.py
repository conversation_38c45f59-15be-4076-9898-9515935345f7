"""Mapper for Patient Episode of Care Order requests"""

from src.application.dtos.patient_episode_of_care_order_dto import (
    AddDirectiveDTO,
    AddDirectivesDTO,
    PatientEpisodeOfCareOrderCreateDTO,
    PatientEpisodeOfCareOrderDirectiveDTO,
    PatientEpisodeOfCareOrderUpdateDTO,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession
from src.infrastructure.api.schemas.patient_episode_of_care_order_schema import (
    AddDirectivesSchema,
    PatientEpisodeOfCareOrderCreateSchema,
    PatientEpisodeOfCareOrderUpdateSchema,
)


def map_create_request_to_dto(
    request: PatientEpisodeOfCareOrderCreateSchema,
    context: AuthSession,
) -> PatientEpisodeOfCareOrderCreateDTO:
    """Map create request to DTO"""
    # Convert schema directives to DTO directives
    directives = None
    if request.directives:
        directives = [
            PatientEpisodeOfCareOrderDirectiveDTO(instructions=directive.instructions)
            for directive in request.directives
        ]

    return PatientEpisodeOfCareOrderCreateDTO(
        patient_episode_of_care_id=request.patient_episode_of_care_id,
        order_status=request.order_status,
        order_date=request.order_date,
        order_notes=request.order_notes,
        directives=directives,
    )


def map_update_request_to_dto(
    request: PatientEpisodeOfCareOrderUpdateSchema,
    context: AuthSession,
) -> PatientEpisodeOfCareOrderUpdateDTO:
    """Map update request to DTO"""
    return PatientEpisodeOfCareOrderUpdateDTO(
        id=request.id,
        patient_episode_of_care_id=request.patient_episode_of_care_id,
        order_status=request.order_status,
        order_date=request.order_date,
        order_notes=request.order_notes,
    )


def map_add_directives_request_to_dto(
    request: AddDirectivesSchema,
    context: AuthSession,
) -> AddDirectivesDTO:
    """Map add directives request to DTO"""
    return AddDirectivesDTO(
        directives=[
            AddDirectiveDTO(instructions=directive.instructions) for directive in request.directives
        ]
    )
