"""Mapper for Patient Episode of Care"""

from src.application.dtos.patient_episode_of_care_dto import (
    PatientEpisodeOfCareCreateDTO,
    PatientEpisodeOfCareResponseDTO,
    PatientEpisodeOfCareUpdateDTO,
)
from src.application.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper,
)
from src.domain.entities.patient_episode_of_care import PatientEpisodeOfCareEntity
from src.domain.foundations.base.audit_stamp import AuditStamp


class PatientEpisodeOfCareMapper:
    """Mapper for Patient Episode of Care"""

    def __init__(self):
        """Initialize mapper"""
        self._order_mapper = PatientEpisodeOfCareOrderMapper()

    def to_dto(self, entity: PatientEpisodeOfCareEntity) -> PatientEpisodeOfCareResponseDTO:
        """Convert entity to DTO"""
        if not entity:
            return None

        # Map orders
        orders = []
        if hasattr(entity, "orders") and entity.orders:
            orders = [self._order_mapper.to_dto(order) for order in entity.orders if order]

        # Create response DTO
        response = PatientEpisodeOfCareResponseDTO(
            id=entity.id,
            patient_id=entity.patient_id,
            patient_mrn_id=entity.patient_mrn_id,
            location_id=entity.location_id,
            discharge_summary=entity.discharge_summary,
            clinical_diagnosis=entity.clinical_diagnosis,
            date_of_start_of_care=entity.date_of_start_of_care,
            insurance_name=entity.insurance_name,
            insurance_type=entity.insurance_type,
            insurance_number=entity.insurance_number,
            tenant_id=entity.audit_stamp.tenant_id,
            mod_at=entity.audit_stamp.mod_at,
            mod_by=entity.audit_stamp.mod_by,
            mod_service=entity.audit_stamp.mod_service,
            record_status=entity.audit_stamp.record_status.value,
            orders=orders,
        )

        return response

    def to_domain(
        self,
        dto: PatientEpisodeOfCareCreateDTO | PatientEpisodeOfCareUpdateDTO,
        audit_stamp: AuditStamp,
    ) -> PatientEpisodeOfCareEntity:
        """Convert DTO to entity"""
        if not dto:
            return None
        return PatientEpisodeOfCareEntity(
            id=getattr(dto, "id", None),
            audit_stamp=audit_stamp,
            patient_id=dto.patient_id,
            patient_mrn_id=dto.patient_mrn_id,
            location_id=dto.location_id,
            discharge_summary=dto.discharge_summary,
            clinical_diagnosis=dto.clinical_diagnosis,
            date_of_start_of_care=dto.date_of_start_of_care,
            insurance_name=dto.insurance_name,
            insurance_type=dto.insurance_type,
            insurance_number=dto.insurance_number,
        )
