"""Use case for Patient Episode of Care Order operations"""

from datetime import datetime
from uuid import UUID

from src.application.dtos.patient_episode_of_care_order_dto import (
    AddDirectivesDTO,
    PatientEpisodeOfCareOrderCreateDTO,
    PatientEpisodeOfCareOrderResponseDTO,
    PatientEpisodeOfCareOrderUpdateDTO,
    RemoveDirectivesDTO,
)
from src.application.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper,
)
from src.config.logging import logging
from src.config.settings import settings
from src.domain.exceptions.base import ValidationError
from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.record_status_enum import RecordStatusEnum
from src.domain.repositories.patient_episode_of_care_order_repository import (
    PatientEpisodeOfCareOrderRepository,
)
from src.domain.repositories.patient_episode_of_care_repository import (
    PatientEpisodeOfCareRepository,
)
from src.domain.value_objects.patient_episode_of_care_order_directive import (
    PatientEpisodeOfCareOrderDirective,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession

logger = logging.getLogger(__name__)


class PatientEpisodeOfCareOrderUseCase:
    """Use case for Patient Episode of Care Order operations"""

    def __init__(
        self,
        repository: PatientEpisodeOfCareOrderRepository,
        episode_repository: PatientEpisodeOfCareRepository,
        mapper: PatientEpisodeOfCareOrderMapper,
    ):
        """Initialize use case with dependencies"""
        self._repository = repository
        self._episode_repository = episode_repository
        self._mapper = mapper

    async def get_by_id(self, order_id: UUID) -> PatientEpisodeOfCareOrderResponseDTO | None:
        """Get a patient episode of care order by ID"""
        try:
            entity = await self._repository.find_by_id(order_id)
            return self._mapper.to_dto(entity) if entity else None
        except Exception as e:
            logger.error(f"Failed to get order: {e!s}")
            raise ValidationError(f"Failed to get order: {e!s}") from e

    async def get_all(
        self, page: int = 1, page_size: int = 10
    ) -> list[PatientEpisodeOfCareOrderResponseDTO]:
        """Get all patient episode of care orders with pagination"""
        try:
            entities = await self._repository.find_all(page, page_size)
            return [self._mapper.to_dto(entity) for entity in entities]
        except Exception as e:
            logger.error(f"Failed to list orders: {e!s}")
            raise ValidationError(f"Failed to list orders: {e!s}") from e

    async def create(
        self, dto: PatientEpisodeOfCareOrderCreateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareOrderResponseDTO:
        """Create a new patient episode of care order"""
        try:
            # Check if episode exists
            episode = await self._episode_repository.find_by_id(dto.episode_id)
            if not episode:
                raise ValidationError(f"Episode not found with ID: {dto.episode_id}")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Convert DTO to domain entity
            entity = self._mapper.to_domain(dto, audit_stamp)

            # Save order
            saved_entity = await self._repository.save(entity, audit_stamp)
            return self._mapper.to_dto(saved_entity)
        except Exception as e:
            logger.error(f"Failed to create order: {e!s}")
            raise ValidationError(f"Failed to create order: {e!s}") from e

    async def update(
        self, dto: PatientEpisodeOfCareOrderUpdateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareOrderResponseDTO:
        """Update a patient episode of care order"""
        try:
            # Get existing order
            entity = await self._repository.find_by_id(dto.id)
            if not entity:
                raise ValidationError(f"Order not found with ID: {dto.id}")

            # Check if episode exists if episode_id is being updated
            if dto.episode_id and dto.episode_id != entity.episode_id:
                episode = await self._episode_repository.find_by_id(dto.episode_id)
                if not episode:
                    raise ValidationError(f"Episode not found with ID: {dto.episode_id}")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Convert DTO to domain entity
            updated_entity = self._mapper.to_domain(dto, audit_stamp)

            # Update order
            saved_entity = await self._repository.update(updated_entity, audit_stamp)
            return self._mapper.to_dto(saved_entity)
        except Exception as e:
            logger.error(f"Failed to update order: {e!s}")
            raise ValidationError(f"Failed to update order: {e!s}") from e

    async def delete(self, order_id: UUID, context: AuthSession) -> bool:
        """Delete a patient episode of care order"""
        try:
            # Get existing order
            entity = await self._repository.find_by_id(order_id)
            if not entity:
                raise ValidationError(f"Order with ID {order_id} not found")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.DELETED,
            )

            return await self._repository.delete(order_id, audit_stamp)
        except Exception as e:
            logger.error(f"Failed to delete order: {e!s}")
            raise ValidationError(f"Failed to delete order: {e!s}") from e

    async def count(self) -> int:
        """Get total count of orders"""
        try:
            return await self._repository.count()
        except Exception as e:
            logger.error(f"Failed to count orders: {e!s}")
            raise ValidationError(f"Failed to count orders: {e!s}") from e

    async def add_directives(
        self, order_id: UUID, dto: AddDirectivesDTO, context: AuthSession
    ) -> PatientEpisodeOfCareOrderResponseDTO | None:
        """Add directives to an order"""
        try:
            # Get existing order
            entity = await self._repository.find_by_id(order_id)
            if not entity:
                raise ValidationError(f"Order with ID {order_id} not found")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Add directives
            for directive in dto.directives:
                entity.add_directive(
                    PatientEpisodeOfCareOrderDirective(
                        instructions=directive.instructions, audit_stamp=audit_stamp
                    )
                )

            # Update order
            updated_entity = await self._repository.update(entity, audit_stamp)
            return self._mapper.to_dto(updated_entity)
        except Exception as e:
            logger.error(f"Failed to add directives: {e!s}")
            raise ValidationError(f"Failed to add directives: {e!s}") from e

    async def remove_directives(
        self, order_id: UUID, dto: RemoveDirectivesDTO, context: AuthSession
    ) -> PatientEpisodeOfCareOrderResponseDTO | None:
        """Remove directives from an order"""
        try:
            # Get existing order
            entity = await self._repository.find_by_id(order_id)
            if not entity:
                raise ValidationError(f"Order with ID {order_id} not found")

            # Create audit stamp
            audit_stamp = AuditStamp(
                tenant_id=context.tenant_id,
                mod_at=datetime.now(),
                mod_by=context.user_id,
                mod_service=settings.DEFAULT_MOD_SERVICE,
                record_status=RecordStatusEnum.ACTIVE,
            )

            # Remove directives
            for directive_id in dto.directive_ids:
                entity.remove_directive(directive_id)

            # Update order
            updated_entity = await self._repository.update(entity, audit_stamp)
            return self._mapper.to_dto(updated_entity)
        except Exception as e:
            logger.error(f"Failed to remove directives: {e!s}")
            raise ValidationError(f"Failed to remove directives: {e!s}") from e
