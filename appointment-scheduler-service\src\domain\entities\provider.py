"""Provider domain entity"""

from datetime import datetime, time
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import dataclass, field

from src.domain.value_objects.location import Location
from src.domain.value_objects.skill import Skill
from src.domain.value_objects.availability import Availability
from src.domain.value_objects.capacity import Capacity
from src.domain.enums.provider_role import ProviderRole
from src.domain.enums.provider_status import ProviderStatus


@dataclass
class Provider:
    """Provider domain entity"""
    
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    role: ProviderRole = ProviderRole.RN
    
    # Skills and capabilities
    skills: List[Skill] = field(default_factory=list)
    certifications: List[str] = field(default_factory=list)
    
    # Location and service area
    home_location: Optional[Location] = None
    service_radius_miles: float = 25.0
    
    # Availability and capacity
    availability: Optional[Availability] = None
    capacity: Optional[Capacity] = None
    
    # Status and preferences
    status: ProviderStatus = ProviderStatus.ACTIVE
    preferred_service_types: List[str] = field(default_factory=list)
    
    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    
    # Audit fields
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization validation"""
        if not self.name:
            raise ValueError("Provider name is required")
        
        if self.service_radius_miles <= 0:
            raise ValueError("Service radius must be positive")
    
    def has_skill(self, skill: Skill) -> bool:
        """Check if provider has specific skill"""
        return skill in self.skills
    
    def has_all_skills(self, required_skills: List[Skill]) -> bool:
        """Check if provider has all required skills"""
        return all(self.has_skill(skill) for skill in required_skills)
    
    def add_skill(self, skill: Skill) -> None:
        """Add skill to provider"""
        if skill not in self.skills:
            self.skills.append(skill)
            self.updated_at = datetime.utcnow()
    
    def remove_skill(self, skill: Skill) -> None:
        """Remove skill from provider"""
        if skill in self.skills:
            self.skills.remove(skill)
            self.updated_at = datetime.utcnow()
    
    def is_available_on_day(self, day_name: str) -> bool:
        """Check if provider is available on specific day"""
        if not self.availability:
            return False
        return self.availability.is_available_on_day(day_name)
    
    def is_available_at_time(self, check_time: time) -> bool:
        """Check if provider is available at specific time"""
        if not self.availability:
            return False
        return self.availability.is_available_at_time(check_time)
    
    def can_serve_location(self, location: Location) -> bool:
        """Check if provider can serve specific location"""
        if not self.home_location:
            return False
        
        distance = self.home_location.distance_to(location)
        return distance <= self.service_radius_miles
    
    def get_max_appointments_per_day(self) -> int:
        """Get maximum appointments per day"""
        if not self.capacity:
            return 8  # Default
        return self.capacity.max_appointments_per_day
    
    def get_max_hours_per_day(self) -> int:
        """Get maximum working hours per day"""
        if not self.capacity:
            return 8  # Default
        return self.capacity.max_hours_per_day
    
    def activate(self) -> None:
        """Activate provider"""
        self.status = ProviderStatus.ACTIVE
        self.updated_at = datetime.utcnow()
    
    def deactivate(self) -> None:
        """Deactivate provider"""
        self.status = ProviderStatus.INACTIVE
        self.updated_at = datetime.utcnow()
    
    def mark_unavailable(self, reason: str, until_date: Optional[datetime] = None) -> None:
        """Mark provider as temporarily unavailable"""
        self.status = ProviderStatus.UNAVAILABLE
        self.updated_at = datetime.utcnow()
        # In a full implementation, this would update availability records
    
    @property
    def is_active(self) -> bool:
        """Check if provider is active"""
        return self.status == ProviderStatus.ACTIVE
    
    @property
    def display_name(self) -> str:
        """Get display name with role"""
        return f"{self.name}, {self.role.value}"
