"""
Defines the base class for domain events.
"""

from abc import ABC
from dataclasses import dataclass, field
from datetime import UTC, datetime
from uuid import UUID, uuid4


@dataclass(frozen=True)
class DomainEvent(ABC):
    """
    Base class for all domain events.
    Events are immutable once created.
    """

    event_type: str = field(init=False)  # To be set by subclasses
    event_id: UUID = field(default_factory=uuid4)
    occurred_on: datetime = field(default_factory=lambda: datetime.now(UTC))

    def __post_init__(self):
        # Automatically set event_type to the subclass name if not explicitly set by it
        if not hasattr(self, "event_type") or not self.event_type:
            # Object assignment to a frozen dataclass attribute needs care.
            # This uses object.__setattr__ to bypass frozen=True for this default initialization.
            object.__setattr__(self, "event_type", self.__class__.__name__)
