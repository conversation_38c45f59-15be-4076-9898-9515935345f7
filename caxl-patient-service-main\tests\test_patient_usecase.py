"""Unit tests for patient use cases"""

from datetime import date
from unittest.mock import AsyncMock, MagicMock
from uuid import UUID, uuid4

import pytest

from src.application.dtos.patient import (
    BasicPersonalInfoDTO,
    ContactInfoDTO,
    EmailInfoDTO,
    LocationInfoDTO,
    LocationNameDTO,
    PatientDTO,
    PatientListResponse,
    PatientPIIDTO,
    PersonalInfoDTO,
    PhoneInfoDTO,
    PhoneTypeDTO,
)
from src.application.usecases.patient import PatientUseCase
from src.domain.exceptions.base import ValidationError
from src.infrastructure.api.schemas.auth_schemas import AuthSession

# Test data
TEST_TENANT_ID = str(uuid4())
TEST_USER_ID = str(uuid4())
TEST_PATIENT_ID = uuid4()
TEST_CONTEXT = AuthSession(tenant_id=TEST_TENANT_ID, user_id=TEST_USER_ID)


def create_test_patient_dto(patient_id: UUID | None = None):
    """Create a test patient DTO with required fields"""
    return PatientDTO(
        id=patient_id or uuid4(),
        pii=PatientPIIDTO(
            personal_info=PersonalInfoDTO(
                basic_info=BasicPersonalInfoDTO(
                    first_name="John", last_name="Doe", gender="Male", dob=date(1990, 1, 1)
                )
            ),
            contact_info=ContactInfoDTO(
                emails=[EmailInfoDTO(email="<EMAIL>")],
                phones=[
                    PhoneInfoDTO(
                        phone_number="**********", phone_type=PhoneTypeDTO.MOBILE, is_primary=True
                    )
                ],
                locations=[
                    LocationInfoDTO(
                        location_name=LocationNameDTO.HOME, location_id=uuid4(), is_primary=True
                    )
                ],
                emergency_contacts=[],
            ),
        ),
    )


@pytest.fixture
def mock_patient_repository():
    """Mock patient repository"""
    return AsyncMock()


@pytest.fixture
def mock_patient_mapper():
    """Mock patient mapper"""
    return MagicMock()


@pytest.fixture
def patient_use_case(mock_patient_repository, mock_patient_mapper):
    """Create patient use case with mocked dependencies"""
    return PatientUseCase(
        patient_repository=mock_patient_repository, patient_mapper=mock_patient_mapper
    )


@pytest.mark.asyncio
async def test_get_patient_success(patient_use_case, mock_patient_repository, mock_patient_mapper):
    """Test successful patient retrieval"""
    # Arrange
    mock_patient = MagicMock()
    mock_patient_dto = create_test_patient_dto(TEST_PATIENT_ID)
    mock_patient_repository.find_by_id.return_value = mock_patient
    mock_patient_mapper.to_response.return_value = mock_patient_dto

    # Act
    result = await patient_use_case.get_patient(TEST_PATIENT_ID)

    # Assert
    assert result == mock_patient_dto
    mock_patient_repository.find_by_id.assert_called_once_with(TEST_PATIENT_ID)
    mock_patient_mapper.to_response.assert_called_once_with(mock_patient)


@pytest.mark.asyncio
async def test_get_patient_not_found(
    patient_use_case, mock_patient_repository, mock_patient_mapper
):
    """Test patient retrieval when not found"""
    # Arrange
    mock_patient_repository.find_by_id.return_value = None

    # Act
    result = await patient_use_case.get_patient(TEST_PATIENT_ID)

    # Assert
    assert result is None
    mock_patient_repository.find_by_id.assert_called_once_with(TEST_PATIENT_ID)
    mock_patient_mapper.to_response.assert_not_called()


@pytest.mark.asyncio
async def test_list_patients_success(
    patient_use_case, mock_patient_repository, mock_patient_mapper
):
    """Test successful patient listing with pagination"""
    # Arrange
    mock_patients = [MagicMock() for _ in range(2)]
    mock_response = PatientListResponse(
        patients=[create_test_patient_dto() for _ in range(2)],
        page=1,
        page_size=10,
        total_records=2,
        total_pages=1,
    )
    mock_patient_repository.find_all.return_value = mock_patients
    mock_patient_repository.count.return_value = 2
    mock_patient_mapper.to_list_response.return_value = mock_response

    # Act
    result = await patient_use_case.list_patients(page=1, page_size=10)

    # Assert
    assert result == mock_response
    mock_patient_repository.find_all.assert_called_once_with(1, 10)
    mock_patient_repository.count.assert_called_once()
    mock_patient_mapper.to_list_response.assert_called_once_with(
        entities=mock_patients, total=2, page=1, page_size=10
    )


@pytest.mark.asyncio
async def test_create_patient_success(
    patient_use_case, mock_patient_repository, mock_patient_mapper
):
    """Test successful patient creation"""
    # Arrange
    mock_patient_dto = create_test_patient_dto(TEST_PATIENT_ID)
    mock_domain_patient = MagicMock()
    mock_saved_patient = MagicMock()
    mock_patient_mapper.to_domain.return_value = mock_domain_patient
    mock_patient_repository.save.return_value = mock_saved_patient
    mock_patient_mapper.to_response.return_value = mock_patient_dto

    # Act
    result = await patient_use_case.create_patient(mock_patient_dto, TEST_CONTEXT)

    # Assert
    assert result == mock_patient_dto
    mock_patient_mapper.to_domain.assert_called_once()
    mock_patient_repository.save.assert_called_once()
    mock_patient_mapper.to_response.assert_called_once_with(mock_saved_patient)


@pytest.mark.asyncio
async def test_create_patient_validation_error(patient_use_case, mock_patient_mapper):
    """Test patient creation with validation error"""
    # Arrange
    mock_patient_dto = create_test_patient_dto(TEST_PATIENT_ID)
    mock_domain_patient = MagicMock()
    mock_domain_patient.pii = None  # This will trigger our use case validation
    mock_patient_mapper.to_domain.return_value = mock_domain_patient

    # Act & Assert
    with pytest.raises(ValidationError, match="Patient PII is required"):
        await patient_use_case.create_patient(mock_patient_dto, TEST_CONTEXT)


@pytest.mark.asyncio
async def test_delete_patient_success(patient_use_case, mock_patient_repository):
    """Test successful patient deletion"""
    # Arrange
    mock_patient = MagicMock()
    mock_patient_repository.find_by_id.return_value = mock_patient
    mock_patient_repository.delete.return_value = True

    # Act
    result = await patient_use_case.delete_patient(TEST_PATIENT_ID, TEST_CONTEXT)

    # Assert
    assert result is True
    mock_patient_repository.find_by_id.assert_called_once_with(TEST_PATIENT_ID)
    mock_patient_repository.delete.assert_called_once()


@pytest.mark.asyncio
async def test_delete_patient_not_found(patient_use_case, mock_patient_repository):
    """Test patient deletion when not found"""
    # Arrange
    mock_patient_repository.find_by_id.return_value = None

    # Act & Assert
    with pytest.raises(ValidationError, match=f"Patient not found with ID: {TEST_PATIENT_ID}"):
        await patient_use_case.delete_patient(TEST_PATIENT_ID, TEST_CONTEXT)
