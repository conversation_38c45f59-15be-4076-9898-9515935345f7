"""Input port for Patient Episode of Care use cases"""

from abc import ABC, abstractmethod
from uuid import UUID

from src.application.dtos.patient_episode_of_care_dto import (
    PatientEpisodeOfCareCreateDTO,
    PatientEpisodeOfCareResponseDTO,
    PatientEpisodeOfCareUpdateDTO,
)
from src.infrastructure.api.schemas.auth_schemas import AuthSession


class PatientEpisodeOfCareUseCasePort(ABC):
    """Input port for Patient Episode of Care use cases"""

    @abstractmethod
    async def get_by_id(self, episode_id: UUID) -> PatientEpisodeOfCareResponseDTO | None:
        """Get episode of care by ID"""
        pass

    @abstractmethod
    async def get_all(
        self, page: int = 1, page_size: int = 10
    ) -> list[PatientEpisodeOfCareResponseDTO]:
        """Get all episodes of care with pagination"""
        pass

    @abstractmethod
    async def create(
        self, dto: PatientEpisodeOfCareCreateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareResponseDTO:
        """Create new episode of care"""
        pass

    @abstractmethod
    async def update(
        self, dto: PatientEpisodeOfCareUpdateDTO, context: AuthSession
    ) -> PatientEpisodeOfCareResponseDTO:
        """Update existing episode of care"""
        pass

    @abstractmethod
    async def delete(self, episode_id: UUID, context: AuthSession) -> bool:
        """Delete episode of care"""
        pass

    @abstractmethod
    async def count(self) -> int:
        """Get total count of episodes of care"""
        pass
