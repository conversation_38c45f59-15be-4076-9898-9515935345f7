"""Dependency injection container setup"""

from dependency_injector import containers, providers

from src.application.mappers.patient_episode_of_care_mapper import (
    PatientEpisodeOfCareMapper as ApplicationPatientEpisodeOfCareMapper,
)
from src.application.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper as ApplicationPatientEpisodeOfCareOrderMapper,
)
from src.application.mappers.patient_mapper import Patient<PERSON>apper as ApplicationPatientMapper
from src.application.usecases.patient import PatientUseCase
from src.application.usecases.patient_episode_of_care_order_usecase import (
    PatientEpisodeOfCareOrderUseCase,
)
from src.application.usecases.patient_episode_of_care_usecase import PatientEpisodeOfCareUseCase
from src.config.settings import settings
from src.infrastructure.adapters.persistence.mappers.patient_episode_of_care_mapper import (
    PatientEpisodeOfCareMapper,
)
from src.infrastructure.adapters.persistence.mappers.patient_episode_of_care_order_mapper import (
    PatientEpisodeOfCareOrderMapper,
)
from src.infrastructure.adapters.persistence.mappers.patient_mapper import PatientMapper
from src.infrastructure.adapters.persistence.repositories.patient_episode_of_care_order_repository import (
    PatientEpisodeOfCareOrderRepositoryImpl,
)
from src.infrastructure.adapters.persistence.repositories.patient_episode_of_care_repository import (
    PatientEpisodeOfCareRepositoryImpl,
)
from src.infrastructure.adapters.persistence.repositories.patient_repository import (
    PatientRepositoryImpl,
)
from src.infrastructure.adapters.persistence.session import SessionManager


class Container(containers.DeclarativeContainer):
    # Mappers
    persistence_patient_mapper = providers.Singleton(PatientMapper)

    application_patient_mapper = providers.Singleton(ApplicationPatientMapper)

    persistence_patient_episode_mapper = providers.Singleton(PatientEpisodeOfCareMapper)

    application_patient_episode_mapper = providers.Singleton(ApplicationPatientEpisodeOfCareMapper)

    persistence_patient_episode_order_mapper = providers.Singleton(PatientEpisodeOfCareOrderMapper)

    application_patient_episode_order_mapper = providers.Singleton(
        ApplicationPatientEpisodeOfCareOrderMapper
    )

    # Db Session manager
    session_manager = providers.Singleton(SessionManager, db_url=settings.database_url)

    # Repositories (Adapters)
    patient_repository = providers.Factory(
        PatientRepositoryImpl, session_manager=session_manager, mapper=persistence_patient_mapper
    )

    patient_episode_repository = providers.Factory(
        PatientEpisodeOfCareRepositoryImpl,
        session_manager=session_manager,
        mapper=persistence_patient_episode_mapper,
    )

    patient_episode_order_repository = providers.Factory(
        PatientEpisodeOfCareOrderRepositoryImpl,
        session_manager=session_manager,
        mapper=persistence_patient_episode_order_mapper,
    )

    # Use Cases
    patient_use_case = providers.Factory(
        PatientUseCase,
        patient_repository=patient_repository,
        patient_mapper=application_patient_mapper,
    )

    patient_episode_use_case = providers.Factory(
        PatientEpisodeOfCareUseCase,
        patient_episode_repository=patient_episode_repository,
        patient_repository=patient_repository,
        patient_episode_mapper=application_patient_episode_mapper,
    )

    patient_episode_order_use_case = providers.Factory(
        PatientEpisodeOfCareOrderUseCase,
        repository=patient_episode_order_repository,
        episode_repository=patient_episode_repository,
        mapper=application_patient_episode_order_mapper,
    )
