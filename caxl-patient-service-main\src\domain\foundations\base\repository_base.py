"""Base repository output port"""

from abc import abstractmethod
from typing import Generic, TypeVar
from uuid import UUID

from src.domain.foundations.base.audit_stamp import AuditStamp
from src.domain.foundations.base.entity_base import EntityBase

T = TypeVar("T", bound=EntityBase)


class BaseRepository(Generic[T]):
    """Base repository output port"""

    @abstractmethod
    async def find_by_id(self, entity_id: UUID) -> T | None:
        """Find entity by ID"""
        pass

    @abstractmethod
    async def find_all(self, page: int = 1, page_size: int = 10) -> list[T]:
        """Find all entities with pagination"""
        pass

    @abstractmethod
    async def save(self, entity: T, audit_stamp: AuditStamp) -> T:
        """Save entity"""
        pass

    @abstractmethod
    async def update(self, entity: T, audit_stamp: AuditStamp) -> T:
        """Update entity"""
        pass

    @abstractmethod
    async def delete(self, entity_id: UUID, audit_stamp: AuditStamp) -> bool:
        """Delete entity"""
        pass

    @abstractmethod
    async def count(self) -> int:
        """Count total entities"""
        pass
