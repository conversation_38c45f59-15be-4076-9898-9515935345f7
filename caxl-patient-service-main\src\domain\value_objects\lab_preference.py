"""Lab Preference Value Object"""

from dataclasses import dataclass
from uuid import UUID

from src.domain.foundations.base.value_object_base import ValueObjectBase


@dataclass(frozen=True)
class LabPreference(ValueObjectBase):
    """Lab preference value object"""

    name: str
    location_id: UUID
    phone: str
    phone_country_code: str
    email: str

    def __post_init__(self):
        if not self.name or not self.name.strip():
            raise ValueError("Lab name is required")
        object.__setattr__(self, "name", self.name.strip())
