["tests/test_basic_functionality.py::TestDataLoader::test_load_appointments", "tests/test_basic_functionality.py::TestDataLoader::test_load_basic_demo_data", "tests/test_basic_functionality.py::TestDataLoader::test_load_consumers", "tests/test_basic_functionality.py::TestDataLoader::test_load_providers", "tests/test_basic_functionality.py::TestScenarioValidator::test_validate_basic_demo_scenario", "tests/test_basic_functionality.py::TestScenarioValidator::test_validate_incomplete_scenario", "tests/test_basic_functionality.py::TestScenarioValidator::test_validate_missing_directory", "tests/test_basic_functionality.py::TestTestHelpers::test_create_edge_case_scenarios", "tests/test_basic_functionality.py::TestTestHelpers::test_create_minimal_scenario", "tests/test_basic_functionality.py::TestTestHelpers::test_create_temp_scenario"]