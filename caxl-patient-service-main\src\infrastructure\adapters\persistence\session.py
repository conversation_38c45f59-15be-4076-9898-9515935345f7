from __future__ import annotations

from collections.abc import AsyncGenerator

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import declarative_base

from src.config.settings import settings


class SessionManager:
    """Manages asynchronous DB sessions with connection pooling."""

    def __init__(self, db_url: str) -> None:
        self.engine: AsyncEngine | None = None
        self.session_factory: async_sessionmaker[AsyncSession] | None = None
        self.init_db(url=db_url)

    def init_db(self, url: str) -> None:
        """Initialize the database engine and session factory."""
        self.engine = create_async_engine(
            url=url,
            echo=settings.DB_ECHO,
            # poolclass=AsyncAdaptedQueuePool,
            # pool_size=settings.POOL_SIZE,
            # max_overflow=settings.MAX_OVERFLOW,
            # pool_pre_ping=True,
            # pool_recycle=settings.POOL_RECYCLE,
        )

        self.session_factory = async_sessionmaker(
            bind=self.engine,
            expire_on_commit=False,
            autoflush=False,
            class_=AsyncSession,
        )

    async def close(self) -> None:
        """Dispose of the database engine."""
        if self.engine:
            await self.engine.dispose()

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Yield a database session."""
        if not self.session_factory:
            raise RuntimeError("Database session factory is not initialized.")

        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                raise RuntimeError(f"Database session error: {e!r}") from e


Base = declarative_base()
