from pydantic import BaseModel, Field


class UserInfo(BaseModel):
    """User information."""

    username: str = Field(..., description="Username")
    email: str = Field(..., description="Email")
    name: str = Field(..., description="Full name")
    role: str = Field(..., description="User role")


class AuthSession(BaseModel):
    """Response schema for auth session."""

    tenant_id: str = Field(..., description="Unique identifier for tenant")
    user_id: str = Field(..., description="Unique identifier for user")
