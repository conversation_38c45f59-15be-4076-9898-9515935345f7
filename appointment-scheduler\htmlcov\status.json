{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "1b75fcaa5f6aecf7b6dce0f0ac709b8c", "files": {"z_2a3aadb4c065132f___init___py": {"hash": "ac95527e66c1d7d0c5d6c5a94a30c6a8", "index": {"url": "z_2a3aadb4c065132f___init___py.html", "file": "src\\appointment_scheduler\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6572ad792a708ab4___init___py": {"hash": "330b43353a226158e5c36706d35fe7b2", "index": {"url": "z_6572ad792a708ab4___init___py.html", "file": "src\\appointment_scheduler\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6572ad792a708ab4_app_py": {"hash": "ffff0ca0a7d53449dc87f51ef11a2dda", "index": {"url": "z_6572ad792a708ab4_app_py.html", "file": "src\\appointment_scheduler\\api\\app.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6572ad792a708ab4_models_py": {"hash": "567485cd269f9b08ec8ee6bcb917b129", "index": {"url": "z_6572ad792a708ab4_models_py.html", "file": "src\\appointment_scheduler\\api\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6572ad792a708ab4_routes_py": {"hash": "e99175c3436047fcb0c8621ebb4b4d6b", "index": {"url": "z_6572ad792a708ab4_routes_py.html", "file": "src\\appointment_scheduler\\api\\routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_config_manager_py": {"hash": "ba275a69e92c94eb3f4046e97c37e4d5", "index": {"url": "z_2a3aadb4c065132f_config_manager_py.html", "file": "src\\appointment_scheduler\\config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd___init___py": {"hash": "4e2beb5b5374685174645fb26adb16e6", "index": {"url": "z_f5dfb1d826f8cdcd___init___py.html", "file": "src\\appointment_scheduler\\constraints\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_assignment_constraints_py": {"hash": "7c70dad798a75b9e62496cefb97a5d4c", "index": {"url": "z_f5dfb1d826f8cdcd_assignment_constraints_py.html", "file": "src\\appointment_scheduler\\constraints\\assignment_constraints.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_base_constraints_py": {"hash": "eb2b2db3c946ef1291cbdc28bb26cffc", "index": {"url": "z_f5dfb1d826f8cdcd_base_constraints_py.html", "file": "src\\appointment_scheduler\\constraints\\base_constraints.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 0, "n_missing": 215, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py": {"hash": "18d4ccb9a27ac2362dfc3cd9085eb3cd", "index": {"url": "z_f5dfb1d826f8cdcd_c001_asgn_provider_skill_validation_py.html", "file": "src\\appointment_scheduler\\constraints\\c001_asgn_provider_skill_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py": {"hash": "884c2a833c6467999330d7cd27404b68", "index": {"url": "z_f5dfb1d826f8cdcd_c002_asgn_date_based_availability_py.html", "file": "src\\appointment_scheduler\\constraints\\c002_asgn_date_based_availability.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py": {"hash": "d7a4283589129bb18416fb03893e368b", "index": {"url": "z_f5dfb1d826f8cdcd_c003_asgn_geographic_service_area_py.html", "file": "src\\appointment_scheduler\\constraints\\c003_asgn_geographic_service_area.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py": {"hash": "27b07ae5d6774d72c7c70bce7f56dc26", "index": {"url": "z_f5dfb1d826f8cdcd_c004_asgn_time_based_availability_py.html", "file": "src\\appointment_scheduler\\constraints\\c004_asgn_time_based_availability.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py": {"hash": "251824689aff14c10c956539eba033d3", "index": {"url": "z_f5dfb1d826f8cdcd_c004_asgn_timed_visit_date_assignment_py.html", "file": "src\\appointment_scheduler\\constraints\\c004_asgn_timed_visit_date_assignment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py": {"hash": "1358f40af2639aa53d430f57f74e05da", "index": {"url": "z_f5dfb1d826f8cdcd_c005_asgn_workload_balance_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c005_asgn_workload_balance_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py": {"hash": "0586c1278dfec21f4cfb4d05dac81289", "index": {"url": "z_f5dfb1d826f8cdcd_c006_asgn_geographic_clustering_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c006_asgn_geographic_clustering_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py": {"hash": "8c11cb65eb2159f3f918df5b8fc42c82", "index": {"url": "z_f5dfb1d826f8cdcd_c007_asgn_patient_preference_matching_py.html", "file": "src\\appointment_scheduler\\constraints\\c007_asgn_patient_preference_matching.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py": {"hash": "1022cde6f32335f97a0e817b6437b80e", "index": {"url": "z_f5dfb1d826f8cdcd_c008_asgn_provider_capacity_management_py.html", "file": "src\\appointment_scheduler\\constraints\\c008_asgn_provider_capacity_management.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py": {"hash": "159e3f519b5d7ee6efd1d2d6544bbc34", "index": {"url": "z_f5dfb1d826f8cdcd_c009_asgn_continuity_of_care_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c009_asgn_continuity_of_care_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py": {"hash": "607945a50694da293959c24c2b10adbf", "index": {"url": "z_f5dfb1d826f8cdcd_c010_schd_timeslot_availability_validation_py.html", "file": "src\\appointment_scheduler\\constraints\\c010_schd_timeslot_availability_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py": {"hash": "cb8cc682fcc3ea3825cacf3c3682907e", "index": {"url": "z_f5dfb1d826f8cdcd_c011_schd_appointment_overlap_prevention_py.html", "file": "src\\appointment_scheduler\\constraints\\c011_schd_appointment_overlap_prevention.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py": {"hash": "362e36f2469386bcb9335ac6c4e4ab0a", "index": {"url": "z_f5dfb1d826f8cdcd_c012_schd_flexible_appointment_timing_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c012_schd_flexible_appointment_timing_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py": {"hash": "5314ec656979131ff723f37ac3ddcc50", "index": {"url": "z_f5dfb1d826f8cdcd_c013_schd_healthcare_task_sequencing_py.html", "file": "src\\appointment_scheduler\\constraints\\c013_schd_healthcare_task_sequencing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py": {"hash": "ed174c3e39d164464171064484ef54b7", "index": {"url": "z_f5dfb1d826f8cdcd_c014_schd_route_travel_time_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c014_schd_route_travel_time_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py": {"hash": "34db7baca1878f567b27b75e82d45cb6", "index": {"url": "z_f5dfb1d826f8cdcd_c015_schd_timed_appointment_pinning_py.html", "file": "src\\appointment_scheduler\\constraints\\c015_schd_timed_appointment_pinning.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py": {"hash": "8fda830d36f532579cf6e191719ce770", "index": {"url": "z_f5dfb1d826f8cdcd_c016_schd_route_optimization_py.html", "file": "src\\appointment_scheduler\\constraints\\c016_schd_route_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f5dfb1d826f8cdcd_day_constraints_py": {"hash": "caf8c878416ab3311882ed7bdaa17ae4", "index": {"url": "z_f5dfb1d826f8cdcd_day_constraints_py.html", "file": "src\\appointment_scheduler\\constraints\\day_constraints.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_data_loader_py": {"hash": "557508215e042be03f3ebff128af8e7e", "index": {"url": "z_2a3aadb4c065132f_data_loader_py.html", "file": "src\\appointment_scheduler\\data_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_domain_py": {"hash": "45f19d3a8488b0199c755b88c97cc1e7", "index": {"url": "z_2a3aadb4c065132f_domain_py.html", "file": "src\\appointment_scheduler\\domain.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 405, "n_excluded": 0, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c9ee4c2c0763ed7___init___py": {"hash": "ec08a3ba6c063748bc4bb1609edc65d7", "index": {"url": "z_1c9ee4c2c0763ed7___init___py.html", "file": "src\\appointment_scheduler\\jobs\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c9ee4c2c0763ed7_assign_appointments_py": {"hash": "21fc5028ef8f2ff064c27f5b6bce2d03", "index": {"url": "z_1c9ee4c2c0763ed7_assign_appointments_py.html", "file": "src\\appointment_scheduler\\jobs\\assign_appointments.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 291, "n_excluded": 0, "n_missing": 255, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c9ee4c2c0763ed7_day_plan_py": {"hash": "81285cbf336f517bb21faf084ebe952c", "index": {"url": "z_1c9ee4c2c0763ed7_day_plan_py.html", "file": "src\\appointment_scheduler\\jobs\\day_plan.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 275, "n_excluded": 0, "n_missing": 244, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_logging_config_py": {"hash": "d44e04b3c346a00f321fdaf08de128aa", "index": {"url": "z_2a3aadb4c065132f_logging_config_py.html", "file": "src\\appointment_scheduler\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_planning_models_py": {"hash": "247d2e571895bda12e6660f76e8b2112", "index": {"url": "z_2a3aadb4c065132f_planning_models_py.html", "file": "src\\appointment_scheduler\\planning_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a3aadb4c065132f_scheduler_py": {"hash": "20cb736c0d0b2f219b53abd1940e4ef1", "index": {"url": "z_2a3aadb4c065132f_scheduler_py.html", "file": "src\\appointment_scheduler\\scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4681ee4fa776c52b___init___py": {"hash": "607087597125098f8ac341883b932ee2", "index": {"url": "z_4681ee4fa776c52b___init___py.html", "file": "src\\appointment_scheduler\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4681ee4fa776c52b_scenario_validator_py": {"hash": "9c1d6d246b34868a3fec17a75356bb05", "index": {"url": "z_4681ee4fa776c52b_scenario_validator_py.html", "file": "src\\appointment_scheduler\\utils\\scenario_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4681ee4fa776c52b_test_helpers_py": {"hash": "bb577be502dc486120ac3f237ecd776d", "index": {"url": "z_4681ee4fa776c52b_test_helpers_py.html", "file": "src\\appointment_scheduler\\utils\\test_helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}