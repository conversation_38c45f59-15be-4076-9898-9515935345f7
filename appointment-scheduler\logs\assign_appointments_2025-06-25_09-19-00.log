2025-06-25 09:19:00,377 - root - INFO - Logger initialized. Log file: D:\Work\Scheduler\appointment-scheduler\logs\assign_appointments_2025-06-25_09-19-00.log
2025-06-25 09:19:00,377 - __main__ - INFO - Initializing AssignAppointmentJob...
2025-06-25 09:19:00,386 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-25 09:19:00,388 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-25 09:19:00,390 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-25 09:19:03,603 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-25 09:19:03,605 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-25 09:19:03,607 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-25 09:19:06,803 - __main__ - INFO - AssignAppointment job initialized with solver config
2025-06-25 09:19:06,804 - __main__ - INFO - Running job for target date: 2025-06-25
2025-06-25 09:19:06,804 - __main__ - INFO - 🚀 === ASSIGNMENT JOB STARTED ===
2025-06-25 09:19:06,805 - __main__ - INFO - 📅 Target Date: 2025-06-25
2025-06-25 09:19:06,805 - __main__ - INFO - 🏥 Service Type: All Services
2025-06-25 09:19:06,805 - __main__ - INFO - === STAGE 1: Loading Data ===
2025-06-25 09:19:06,806 - src.appointment_scheduler.data_loader - INFO - 🚀 Starting data loading process...
2025-06-25 09:19:06,806 - src.appointment_scheduler.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-25 09:19:06,807 - src.appointment_scheduler.data_loader - INFO - Reading provider data from: data\providers.yml
2025-06-25 09:19:06,825 - src.appointment_scheduler.data_loader - INFO - ✅ Provider data loaded: 4 providers
2025-06-25 09:19:06,826 - src.appointment_scheduler.data_loader - INFO -    - Jennifer Martinez, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-25 09:19:06,826 - src.appointment_scheduler.data_loader - INFO -    - Robert Wilson, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-25 09:19:06,827 - src.appointment_scheduler.data_loader - INFO -    - Patricia O'Connor, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-25 09:19:06,827 - src.appointment_scheduler.data_loader - INFO -    - David Thompson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-25 09:19:06,828 - src.appointment_scheduler.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-25 09:19:06,829 - src.appointment_scheduler.data_loader - INFO - Reading consumer data from: data\consumers.yml
2025-06-25 09:19:06,854 - src.appointment_scheduler.data_loader - INFO - ✅ Consumer data loaded: 12 consumers
2025-06-25 09:19:06,855 - src.appointment_scheduler.data_loader - INFO -    - James Anderson - Episode: episode-dt-001
2025-06-25 09:19:06,855 - src.appointment_scheduler.data_loader - INFO -    - Emily Chen - Episode: episode-dt-002
2025-06-25 09:19:06,855 - src.appointment_scheduler.data_loader - INFO -    - Michael Rodriguez - Episode: episode-dt-003
2025-06-25 09:19:06,856 - src.appointment_scheduler.data_loader - INFO -    - Sarah Johnson - Episode: episode-dt-004
2025-06-25 09:19:06,856 - src.appointment_scheduler.data_loader - INFO -    - Robert Wilson - Episode: episode-mt-001
2025-06-25 09:19:06,857 - src.appointment_scheduler.data_loader - INFO -    - Lisa Thompson - Episode: episode-mt-002
2025-06-25 09:19:06,857 - src.appointment_scheduler.data_loader - INFO -    - David Kim - Episode: episode-mt-003
2025-06-25 09:19:06,858 - src.appointment_scheduler.data_loader - INFO -    - Carmen Rodriguez - Episode: episode-mt-004
2025-06-25 09:19:06,858 - src.appointment_scheduler.data_loader - INFO -    - Patricia O'Connor - Episode: episode-um-001
2025-06-25 09:19:06,859 - src.appointment_scheduler.data_loader - INFO -    - Thomas Brown - Episode: episode-um-002
2025-06-25 09:19:06,859 - src.appointment_scheduler.data_loader - INFO -    - Jennifer Davis - Episode: episode-um-003
2025-06-25 09:19:06,859 - src.appointment_scheduler.data_loader - INFO -    - William Garcia - Episode: episode-um-004
2025-06-25 09:19:06,860 - src.appointment_scheduler.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-25 09:19:06,861 - src.appointment_scheduler.data_loader - INFO - Reading appointment data from: data\appointments.yml
2025-06-25 09:19:06,896 - src.appointment_scheduler.data_loader - INFO - ✅ Appointment data loaded: 15 appointments
2025-06-25 09:19:06,897 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,897 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 45min - 2025-06-25
2025-06-25 09:19:06,898 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 75min - 2025-06-25
2025-06-25 09:19:06,898 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT None - 90min - 2025-06-25
2025-06-25 09:19:06,899 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,900 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,900 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 45min - 2025-06-25
2025-06-25 09:19:06,902 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,902 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 75min - 2025-06-25
2025-06-25 09:19:06,903 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT None - 90min - 2025-06-25
2025-06-25 09:19:06,904 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 90min - 2025-06-25
2025-06-25 09:19:06,904 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,905 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 90min - 2025-06-25
2025-06-25 09:19:06,906 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 60min - 2025-06-25
2025-06-25 09:19:06,907 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular None - 45min - 2025-06-25
2025-06-25 09:19:06,907 - src.appointment_scheduler.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-25 09:19:06,908 - src.appointment_scheduler.data_loader - INFO - 📊 Total records loaded:
2025-06-25 09:19:06,908 - src.appointment_scheduler.data_loader - INFO -    - Providers: 4
2025-06-25 09:19:06,909 - src.appointment_scheduler.data_loader - INFO -    - Consumers: 12
2025-06-25 09:19:06,909 - src.appointment_scheduler.data_loader - INFO -    - Appointments: 15
2025-06-25 09:19:06,910 - src.appointment_scheduler.data_loader - INFO - ✅ All data loaded successfully!
2025-06-25 09:19:06,911 - __main__ - INFO - 📊 Initial data loaded: 4 providers, 12 consumers, 15 appointments
2025-06-25 09:19:06,912 - __main__ - INFO - === STAGE 2A: No service type filter applied ===
2025-06-25 09:19:06,913 - __main__ - INFO - ✅ Using all 15 appointments
2025-06-25 09:19:06,914 - __main__ - INFO - === STAGE 2B: Filtering by Date Range ===
2025-06-25 09:19:06,915 - __main__ - INFO - 📅 Date range: 2025-06-25 to 2025-07-01
2025-06-25 09:19:06,916 - __main__ - INFO - ✅ Filtered to 15 appointments for date range
2025-06-25 09:19:06,917 - __main__ - INFO - === STAGE 3: Creating Planning Entities ===
2025-06-25 09:19:06,918 - __main__ - INFO - ✅ Created 15 planning entities
2025-06-25 09:19:06,919 - __main__ - INFO - === STAGE 4: Creating Available Dates ===
2025-06-25 09:19:06,920 - __main__ - INFO - ✅ Created 5 available dates: ['2025-06-25', '2025-06-26', '2025-06-27', '2025-06-30', '2025-07-01']
2025-06-25 09:19:06,920 - __main__ - INFO - === STAGE 5: Creating Optimization Solution ===
2025-06-25 09:19:06,921 - __main__ - INFO - ✅ Solution created with 15 assignments, 5 available dates
2025-06-25 09:19:06,923 - __main__ - INFO - === STAGE 6: Starting Optimization Solver ===
2025-06-25 09:19:06,923 - __main__ - INFO - 🔧 Starting solver with 15 assignments
2025-06-25 09:19:06,923 - __main__ - INFO - 🏥 No specific service type - using default constraints
2025-06-25 09:19:06,924 - __main__ - INFO - 🏥 Using default service config: skilled_nursing
2025-06-25 09:19:06,924 - __main__ - INFO - 🔧 Creating solver instance...
2025-06-25 09:19:07,078 - __main__ - INFO - ⏱️  Solver time limit: 300 seconds
2025-06-25 09:19:07,078 - __main__ - INFO - 🚀 Starting optimization process...
2025-06-25 09:19:07,079 - __main__ - INFO -    - Applying constraint rules...
2025-06-25 09:19:07,080 - __main__ - INFO -    - Balancing workload across providers...
2025-06-25 09:19:07,080 - __main__ - INFO -    - Optimizing geographic distribution...
2025-06-25 09:19:07,081 - __main__ - INFO -    - Considering patient preferences...
2025-06-25 09:19:07,497 - timefold.solver - INFO - Solving started: time spent (113), best score (0hard/0soft), environment mode (PHASE_ASSERT), move thread count (NONE), random (JDK with seed 0).
2025-06-25 09:19:07,501 - timefold.solver - INFO - Problem scale: entity count (15), variable count (30), approximate value count (9), approximate problem scale (3.276791 × 10^19).
2025-06-25 09:19:20,477 - timefold.solver - INFO - Construction Heuristic phase (0) ended: time spent (13104), best score (0hard/-2soft), move evaluation speed (23/sec), step total (15).
2025-06-25 09:19:51,295 - timefold.solver - INFO - Local Search phase (1) ended: time spent (43922), best score (0hard/0soft), move evaluation speed (17/sec), step total (161).
2025-06-25 09:19:51,297 - timefold.solver - INFO - Solving ended: time spent (43923), best score (0hard/0soft), move evaluation speed (18/sec), phase total (2), environment mode (PHASE_ASSERT), move thread count (NONE).
2025-06-25 09:19:51,341 - __main__ - INFO - ✅ Solver completed successfully!
2025-06-25 09:19:51,341 - __main__ - INFO - 📊 Final score: 0hard/0soft
2025-06-25 09:19:51,343 - __main__ - INFO - ✅ Optimization solver completed
2025-06-25 09:19:51,343 - __main__ - INFO - === STAGE 7: Processing Results ===
2025-06-25 09:19:51,343 - __main__ - INFO - 📊 Processing assignment results...
2025-06-25 09:19:51,344 - __main__ - INFO - 🔍 Analyzing assignment results...
2025-06-25 09:19:51,345 - __main__ - INFO - ✅ ASSIGNED: James Anderson -> David Thompson, RN on 2025-06-30
2025-06-25 09:19:51,345 - __main__ - INFO - ✅ ASSIGNED: Emily Chen -> Robert Wilson, LPN on 2025-06-25
2025-06-25 09:19:51,345 - __main__ - INFO - ✅ ASSIGNED: Michael Rodriguez -> Jennifer Martinez, RN on 2025-06-25
2025-06-25 09:19:51,345 - __main__ - INFO - ✅ ASSIGNED: Sarah Johnson -> Jennifer Martinez, RN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: James Anderson -> David Thompson, RN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: Robert Wilson -> Jennifer Martinez, RN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: Lisa Thompson -> Robert Wilson, LPN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: David Kim -> Robert Wilson, LPN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: Carmen Rodriguez -> Jennifer Martinez, RN on 2025-06-25
2025-06-25 09:19:51,346 - __main__ - INFO - ✅ ASSIGNED: Robert Wilson -> Jennifer Martinez, RN on 2025-06-25
2025-06-25 09:19:51,347 - __main__ - INFO - ✅ ASSIGNED: Patricia O'Connor -> Patricia O'Connor, CNA on 2025-06-25
2025-06-25 09:19:51,347 - __main__ - INFO - ✅ ASSIGNED: Thomas Brown -> Patricia O'Connor, CNA on 2025-06-25
2025-06-25 09:19:51,347 - __main__ - INFO - ✅ ASSIGNED: Jennifer Davis -> Patricia O'Connor, CNA on 2025-06-25
2025-06-25 09:19:51,347 - __main__ - INFO - ✅ ASSIGNED: William Garcia -> Patricia O'Connor, CNA on 2025-06-25
2025-06-25 09:19:51,347 - __main__ - INFO - ✅ ASSIGNED: Patricia O'Connor -> Patricia O'Connor, CNA on 2025-06-25
2025-06-25 09:19:51,348 - __main__ - INFO - 📈 Calculating assignment statistics...
2025-06-25 09:19:51,348 - __main__ - INFO - === ASSIGNMENT RESULTS SUMMARY ===
2025-06-25 09:19:51,348 - __main__ - INFO - 📊 Total appointments: 15
2025-06-25 09:19:51,348 - __main__ - INFO - ✅ Assigned: 15 (100.0%)
2025-06-25 09:19:51,348 - __main__ - INFO - ❌ Unassigned: 0
2025-06-25 09:19:51,348 - __main__ - INFO - ⏱️  Processing time: 44.54 seconds
2025-06-25 09:19:51,349 - __main__ - INFO - 📈 Final score: 0hard/0soft
2025-06-25 09:19:51,349 - __main__ - INFO - === SERVICE TYPE STATISTICS ===
2025-06-25 09:19:51,349 - __main__ - INFO - 🏥 general: 15/15 (100.0%)
2025-06-25 09:19:51,349 - __main__ - INFO - === CONSTRAINT SUMMARY ===
2025-06-25 09:19:51,349 - __main__ - INFO - ✅ Satisfied constraints: 45
2025-06-25 09:19:51,349 - __main__ - INFO - ❌ Violated constraints: 0
2025-06-25 09:19:51,349 - __main__ - INFO - 🎉 === ASSIGNMENT JOB COMPLETED ===
2025-06-25 09:19:51,349 - __main__ - INFO - ⏱️  Total processing time: 44.54 seconds
2025-06-25 09:19:51,350 - __main__ - INFO - Job completed.
2025-06-25 09:19:51,350 - __main__ - INFO - Job completed. Exiting...
