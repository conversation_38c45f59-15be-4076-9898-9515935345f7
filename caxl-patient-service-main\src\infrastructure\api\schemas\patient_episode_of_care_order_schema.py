"""Schema for Patient Episode of Care Order"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel

from src.domain.enums import OrderStatus


class PatientEpisodeOfCareOrderDirectiveSchema(BaseModel):
    """Schema for Patient Episode of Care Order Directive"""

    id: UUID | None = None
    instructions: str

    class Config:
        """Pydantic config"""

        from_attributes = True


class PatientEpisodeOfCareOrderBaseSchema(BaseModel):
    """Base schema for Patient Episode of Care Order"""

    patient_episode_of_care_id: UUID
    order_status: OrderStatus
    order_date: datetime
    order_notes: str | None = None
    directives: list[PatientEpisodeOfCareOrderDirectiveSchema] | None = None


class PatientEpisodeOfCareOrderCreateSchema(PatientEpisodeOfCareOrderBaseSchema):
    """Schema for creating Patient Episode of Care Order"""

    pass


class PatientEpisodeOfCareOrderUpdateSchema(BaseModel):
    """Schema for updating Patient Episode of Care Order"""

    id: UUID
    order_status: OrderStatus
    order_date: datetime
    order_notes: str | None = None
    directives: list[PatientEpisodeOfCareOrderDirectiveSchema] | None = None


class PatientEpisodeOfCareOrderResponseSchema(PatientEpisodeOfCareOrderBaseSchema):
    """Schema for Patient Episode of Care Order response"""

    id: UUID
    mod_at: datetime
    mod_by: UUID
    mod_service: str
    record_status: str

    class Config:
        """Pydantic config"""

        from_attributes = True


class PatientEpisodeOfCareOrderListResponseSchema(BaseModel):
    """Schema for Patient Episode of Care Order list response"""

    orders: list[PatientEpisodeOfCareOrderResponseSchema]
    page: int
    page_size: int
    total: int
    total_pages: int


class AddDirectiveSchema(BaseModel):
    """Schema for adding a single directive"""

    instructions: str

    class Config:
        """Pydantic config"""

        from_attributes = True
        populate_by_name = True


class AddDirectivesSchema(BaseModel):
    """Schema for adding directives to an order"""

    directives: list[AddDirectiveSchema]


class RemoveDirectivesSchema(BaseModel):
    """Schema for removing directives from an order"""

    directive_ids: list[UUID]
