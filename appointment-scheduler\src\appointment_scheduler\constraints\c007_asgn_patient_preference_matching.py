"""
Patient Preference Matching Constraint (C007)

This constraint handles patient preferences for providers, times, or other factors.
This is a SOFT constraint for optimization preferences.
"""

from datetime import date, time
from typing import TYPE_CHECKING

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import AppointmentAssignment
from ..domain import AppointmentData
if TYPE_CHECKING:
    from ..domain import Provider


def patient_preference_matching(constraint_factory: ConstraintFactory) -> Constraint:
    """Match patient preferences when possible."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: assignment.provider is not None and 
                                     assignment.assigned_date is not None)
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: _calculate_preference_penalty(assignment))
            .as_constraint("Patient preference matching"))


def _calculate_preference_penalty(assignment: AppointmentAssignment) -> int:
    """Calculate penalty based on how well patient preferences are matched."""
    if assignment.provider is None or assignment.assigned_date is None:
        return 0
    
    penalty = 0
    appointment_data = assignment.appointment_data
    provider = assignment.provider
    assigned_date = assignment.assigned_date
    
    # 1. Check appointment timing preferences
    penalty += _check_timing_preferences(appointment_data, assigned_date)
    
    # 2. Check appointment priority and urgency
    penalty += _check_priority_preferences(appointment_data, provider)
    
    # 3. Check appointment location preferences
    penalty += _check_location_preferences(appointment_data, provider)
    
    # 4. Check appointment properties for custom preferences
    penalty += _check_custom_preferences(appointment_data, provider)
    
    return penalty


def _check_timing_preferences(appointment_data: AppointmentData, assigned_date: date) -> int:
    """Check if assigned date matches appointment timing preferences."""
    penalty = 0
    
    # Check if appointment has specific timing requirements
    if appointment_data.timing.is_timed_visit:
        # For timed visits, we prefer specific dates that align with timing requirements
        # This is a simplified check - in a real system, you'd have more sophisticated logic
        preferred_time = appointment_data.timing.preferred_time
        if preferred_time is not None:
            # Prefer morning appointments for early preferred times
            preferred_hour = preferred_time.hour
            if preferred_hour < 12:  # Morning preference
                # Check if assigned date is a weekday (better for morning appointments)
                if assigned_date.weekday() >= 5:  # Weekend
                    penalty += 2
            elif preferred_hour >= 17:  # Evening preference
                # Check if assigned date is a weekday (better for evening appointments)
                if assigned_date.weekday() >= 5:  # Weekend
                    penalty += 1
    
    # Check appointment date flexibility
    if not appointment_data.timing.is_flexible():
        # Less flexible appointments get higher priority
        penalty += 1
    
    return penalty


def _check_priority_preferences(appointment_data: AppointmentData, provider: 'Provider') -> int:
    """Check if provider matches appointment priority requirements."""
    penalty = 0
    
    # Check if urgent appointments are assigned to experienced providers
    if appointment_data.urgent:
        # Prefer RNs for urgent appointments
        if provider.role != "RN":
            penalty += 3
        
        # Prefer providers with more skills for urgent cases
        if len(provider.skills) < 3:
            penalty += 2
    
    # Check priority level matching
    if appointment_data.priority == "high":
        # High priority appointments should go to experienced providers
        if provider.role not in ["RN", "PT"]:
            penalty += 2
    
    return penalty


def _check_location_preferences(appointment_data: AppointmentData, provider: 'Provider') -> int:
    """Check if provider location matches appointment location preferences."""
    penalty = 0
    
    # If appointment has a specific location, check provider service areas
    if (appointment_data.location is not None and provider.home_location is not None and
        appointment_data.location.city is not None and provider.home_location.city is not None and
        appointment_data.location.city != "" and provider.home_location.city != "" and
        appointment_data.location.city != provider.home_location.city):
        penalty += 2
    
    return penalty


def _check_custom_preferences(appointment_data: AppointmentData, provider: 'Provider') -> int:
    """Check custom preferences stored in appointment properties."""
    penalty = 0
    
    # Check for custom preferences in appointment properties
    if 'preferred_provider_role' in appointment_data.properties:
        preferred_role = appointment_data.properties['preferred_provider_role']
        if provider.role != preferred_role:
            penalty += 2
    
    if 'preferred_language' in appointment_data.properties:
        preferred_language = appointment_data.properties['preferred_language'].lower()
        if not any(preferred_language in lang.lower() for lang in provider.languages):
            penalty += 3
    
    if 'cultural_considerations' in appointment_data.properties:
        cultural_needs = appointment_data.properties['cultural_considerations']
        if cultural_needs:
            # Add penalty for cultural considerations (simplified)
            penalty += 1
    
    return penalty 