repos:
-   repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
    -   id: black
        language_version: python3.11
        args: [--target-version=py311]

-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.10
    hooks:
    -   id: ruff
        args: ["--fix"]
        additional_dependencies: ["ruff==0.1.10"]

-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.10.0
    hooks:
    -   id: pyupgrade
        args: [--py311-plus]

-   repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
    -   id: bandit
        args: [--ini=.bandit.ini]
