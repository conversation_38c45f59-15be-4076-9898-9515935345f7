from dataclasses import dataclass

from src.domain.enums import InsuranceType
from src.domain.foundations.base.value_object_base import ValueObjectBase


@dataclass(frozen=True)
class Insurance(ValueObjectBase):
    """Insurance information value object"""

    name: str
    type: InsuranceType
    number: str

    def __post_init__(self):
        if not self.name or not self.name.strip():
            raise ValueError("Insurance name cannot be empty")
        if not self.number or not self.number.strip():
            raise ValueError("Insurance number cannot be empty")

        object.__setattr__(self, "name", self.name.strip())
        object.__setattr__(self, "number", self.number.strip())
