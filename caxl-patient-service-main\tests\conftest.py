"""Test configuration and fixtures"""

import asyncio
from collections.abc import As<PERSON><PERSON>enerator, Generator
from uuid import UUID

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from src.infrastructure.adapters.persistence.base import Base
from src.infrastructure.adapters.persistence.session import SessionManager
from src.main import app
from src.config.settings import settings

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for each test case"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create a test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield engine
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await engine.dispose()


@pytest.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session"""
    async_session = sessionmaker(test_engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session() as session:
        yield session


@pytest.fixture
async def client(test_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create a test client with required headers"""
    session_manager = SessionManager(db_url=TEST_DATABASE_URL)

    async def override_get_session():
        async for session in session_manager.get_session():
            yield session

    app.dependency_overrides[session_manager.get_session] = override_get_session
    headers = {
        "x-tenant-id": "elara",
        "x-user-id": "test-user",
        "Authorization": "Bearer test-token",
    }
    async with AsyncClient(app=app, base_url="http://test", headers=headers) as client:
        yield client
    app.dependency_overrides.clear()


@pytest.fixture
def test_patient_id() -> UUID:
    """Return a test patient ID"""
    return UUID("00000000-0000-0000-0000-000000000001")


@pytest.fixture
def test_caregiver_id() -> UUID:
    """Return a test caregiver ID"""
    return UUID("00000000-0000-0000-0000-000000000003")


@pytest.fixture
def test_emergency_contact_id() -> UUID:
    """Return a test emergency contact ID"""
    return UUID("00000000-0000-0000-0000-000000000004")
