"""
Configuration management for CareAxl Scheduling Engine.

This module handles loading and validation of configuration files for the scheduling system,
adapted from the proven appointment-scheduler implementation.
"""

from datetime import date, datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import os

import yaml

from src.domain.models.config import ServiceConfig, SchedulerConfig

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and validation for CareAxl scheduling."""
    
    def __init__(self, config_folder: str = "config"):
        """Initialize the configuration manager."""
        self.config_folder = Path(config_folder)
        self.scheduler_config: Optional[SchedulerConfig] = None
        self.service_configs: Dict[str, ServiceConfig] = {}
        
        # Ensure config folder exists
        self.config_folder.mkdir(exist_ok=True)
        
        # Load configurations
        self._load_scheduler_config()
        self._load_service_configs()
    
    def _load_scheduler_config(self) -> None:
        """Load the main scheduler configuration."""
        config_file = self.config_folder / "scheduler.yml"
        
        if not config_file.exists():
            raise FileNotFoundError(
                f"Scheduler configuration file not found: {config_file}\n"
                f"Please create the configuration file at {config_file} with required settings.\n"
                f"See the documentation for configuration format and examples."
            )
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self.scheduler_config = SchedulerConfig(**config_data)
            logger.info(f"Loaded scheduler configuration from {config_file}")
            
        except Exception as e:
            raise RuntimeError(
                f"Failed to load scheduler configuration from {config_file}: {e}\n"
                f"Please check the configuration file format and ensure it's valid YAML."
            ) from e
    
    def _load_service_configs(self) -> None:
        """Load service-specific configurations."""
        service_config_files = [
            "skilled_nursing.yml",
            "physical_therapy.yml", 
            "home_health.yml",
            "personal_care.yml",
            "hospice.yml"
        ]
        
        for config_file in service_config_files:
            file_path = self.config_folder / config_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    service_config = ServiceConfig(**config_data)
                    service_type = service_config.service_type
                    self.service_configs[service_type] = service_config
                    logger.info(f"Loaded service config for {service_type}: {file_path}")
                    
                except Exception as e:
                    logger.error(f"Error loading service config {config_file}: {e}")
            else:
                logger.debug(f"Service config file not found: {file_path}")
        
        # Require at least one service configuration
        if not self.service_configs:
            raise FileNotFoundError(
                f"No service configuration files found in {self.config_folder}\n"
                f"Expected files: {', '.join(service_config_files)}\n"
                f"Please create at least one service configuration file with required settings.\n"
                f"See the documentation for configuration format and examples."
            )
    
    def get_scheduler_config(self) -> SchedulerConfig:
        """Get the scheduler configuration."""
        if self.scheduler_config is None:
            raise RuntimeError(
                "Scheduler configuration not loaded. Please ensure the scheduler.yml file exists and is valid."
            )
        return self.scheduler_config
    
    def get_service_config(self, service_type: str) -> Optional[ServiceConfig]:
        """Get service configuration for a specific service type."""
        return self.service_configs.get(service_type)
    
    def get_all_service_configs(self) -> Dict[str, ServiceConfig]:
        """Get all service configurations."""
        return self.service_configs.copy()
    
    def get_service_types(self) -> List[str]:
        """Get list of available service types."""
        return list(self.service_configs.keys())
    
    def validate_configs(self) -> List[str]:
        """Validate all configurations and return list of issues."""
        issues = []
        
        # Validate scheduler config
        if not self.scheduler_config:
            issues.append("Scheduler configuration is missing")
        else:
            if self.scheduler_config.rolling_window_days <= 0:
                issues.append("rolling_window_days must be positive")
            if self.scheduler_config.batch_size <= 0:
                issues.append("batch_size must be positive")
            if self.scheduler_config.max_solving_time_seconds <= 0:
                issues.append("max_solving_time_seconds must be positive")
        
        # Validate service configs
        if not self.service_configs:
            issues.append("No service configurations found")
        else:
            for service_type, config in self.service_configs.items():
                if not config.required_skills:
                    issues.append(f"Service {service_type} has no required skills")
                if config.geographic_radius_miles <= 0:
                    issues.append(f"Service {service_type} has invalid geographic radius")
                if config.max_daily_appointments_per_provider <= 0:
                    issues.append(f"Service {service_type} has invalid max daily appointments")
                
                # Validate weights are between 0 and 1
                weights = [
                    config.continuity_weight,
                    config.workload_balance_weight,
                    config.geographic_clustering_weight,
                    config.patient_preference_weight
                ]
                for weight in weights:
                    if not 0 <= weight <= 1:
                        issues.append(f"Service {service_type} has invalid weight value: {weight}")
        
        return issues
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """Check if a feature is enabled in the scheduler configuration."""
        if not self.scheduler_config:
            return False
        return getattr(self.scheduler_config, feature_name, False)
    
    def get_constraint_weights(self, service_type: str) -> Dict[str, float]:
        """Get constraint weights for a specific service type."""
        config = self.get_service_config(service_type)
        if not config:
            # Return default weights if service config not found
            return {
                'continuity_weight': 0.7,
                'workload_balance_weight': 0.6,
                'geographic_clustering_weight': 0.5,
                'patient_preference_weight': 0.6
            }
        
        return {
            'continuity_weight': config.continuity_weight,
            'workload_balance_weight': config.workload_balance_weight,
            'geographic_clustering_weight': config.geographic_clustering_weight,
            'patient_preference_weight': config.patient_preference_weight
        }
    
    def get_service_constraints(self, service_type: str) -> Dict[str, Any]:
        """Get all constraints for a specific service type."""
        config = self.get_service_config(service_type)
        if not config:
            return {}
        
        return {
            'geographic_radius_miles': config.geographic_radius_miles,
            'max_daily_appointments_per_provider': config.max_daily_appointments_per_provider,
            'max_weekly_hours_per_provider': config.max_weekly_hours_per_provider,
            'visit_duration_minutes': config.visit_duration_minutes,
            'allows_weekend_visits': config.allows_weekend_visits,
            'cluster_radius_miles': config.cluster_radius_miles,
            'max_cluster_size': config.max_cluster_size,
            'target_daily_appointments': config.target_daily_appointments,
            'workload_variance_tolerance': config.workload_variance_tolerance,
            'required_skills': config.required_skills
        }
    
    def reload_configs(self) -> None:
        """Reload all configuration files."""
        logger.info("Reloading configuration files...")
        self.service_configs.clear()
        self._load_scheduler_config()
        self._load_service_configs()
        logger.info("Configuration files reloaded successfully")


# Global config manager instance
config_manager = ConfigManager()
