"""
Workload Balance Optimization Constraint (C005)

This constraint balances workload across providers to prevent overloading.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from ..planning_models import AppointmentAssignment
from ..domain import Provider

def workload_balancing(constraint_factory: ConstraintFactory) -> Constraint:
    """Balance workload across providers to prevent overloading."""
    return (constraint_factory
            .for_each(Provider)
            .join(AppointmentAssignment,
                  Joiners.equal(lambda provider: provider,
                               lambda assignment: assignment.provider))
            .group_by(lambda provider, assignment: provider,
                     lambda provider, assignment: 1)
            .filter(lambda provider, count: count > provider.capacity.max_tasks_count_in_day)
            .penalize(HardSoftScore.ONE_SOFT, lambda provider, count: count - provider.capacity.max_tasks_count_in_day)
            .as_constraint("Workload balancing")) 